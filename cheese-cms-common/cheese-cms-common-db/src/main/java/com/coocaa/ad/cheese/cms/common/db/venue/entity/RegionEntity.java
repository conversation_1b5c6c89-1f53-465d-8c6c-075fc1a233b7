package com.coocaa.ad.cheese.cms.common.db.venue.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 大区信息表
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "venue_region")
public class RegionEntity {
    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 业务负责人id
     */
    private Integer businessHead;

    /**
     * 法务bp id
     */
    private Integer legalBp;

    /**
     * 财务bp id
     */
    private Integer financeBp;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 大区名称
     */
    private String name;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 操作人
     */
    private Integer operator;

    /**
     * 删除标识。0-否，1-是
     */
    private Integer deleteFlag;
}
