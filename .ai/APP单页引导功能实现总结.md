# APP单页引导功能实现总结

## 1. 功能概述

实现了APP单页引导功能的后端部分，支持APP在特定页面上线新功能时向用户展示一次性引导提示。系统通过记录用户已读状态，确保每个功能引导只向用户展示一次。

## 2. 代码结构

```
src/main/java/com/coocaa/meht/module/web/
├── controller
│   └── UserGuideController.java        # 控制器
├── dao
│   └── UserGuideReadMapper.java        # 数据访问层接口（使用注解方式）
├── dto
│   └── UserGuideReadDTO.java           # 请求数据传输对象
├── entity
│   └── UserGuideRead.java              # 实体类
├── service
│   └── UserGuideService.java           # 服务接口
│   └── impl
│       └── UserGuideServiceImpl.java   # 服务实现类
└── vo
    └── UserGuideReadVO.java            # 响应数据传输对象

src/main/resources/
└── sql
    └── user_guide_read.sql             # 数据库表创建脚本
```

## 3. 数据库设计

### 用户已读记录表(user_guide_read)

| 字段名       | 类型        | 描述              |
| ------------ | ----------- | ----------------- |
| id           | bigint      | 主键ID            |
| user_code    | varchar(10) | 用户编码          |
| feature_code | varchar(10) | 页面功能编码      |
| read_time    | datetime    | 阅读时间          |
| create_by    | varchar(10) | 创建人            |
| create_time  | datetime    | 创建时间          |
| update_by    | varchar(10) | 修改人            |
| update_time  | datetime    | 修改时间          |
| delete_flag  | tinyint(1)  | 是否删除: 0否,1是 |

- 主键：id
- 唯一索引：uk_user_guide(user_code, feature_code)

## 4. 接口设计

### 4.1 检查功能引导是否已读

- **请求方式**：GET
- **请求路径**：/api/guides/check
- **请求参数**：
  - featureCode: String (必填) - 页面功能编码
  - userCode: String (必填) - 用户编码
- **响应数据**：
  ```json
  {
    "code": 200,
    "data": true
  }
  ```

### 4.2 标记已读

- **请求方式**：POST
- **请求路径**：/api/guides/read
- **请求参数**：
  ```json
  {
    "userCode": "U1001",
    "featureCode": "FILTER01"
  }
  ```
- **响应数据**：
  ```json
  {
    "code": 200,
    "data": true
  }
  ```

## 5. 核心逻辑

### 5.1 引导展示逻辑

1. 用户进入页面，客户端请求后端检查功能引导是否已读
2. 后端查询用户是否已读过该功能引导
3. 返回查询结果给客户端
4. 客户端根据结果决定是否展示引导提示（未读则展示）

### 5.2 标记已读逻辑

1. 用户查看引导后，客户端调用标记已读接口
2. 后端记录用户已读状态
3. 已读的引导不再向该用户展示

## 6. 技术实现

### 6.1 数据访问层

使用MyBatis-Plus实现数据访问，定义了`UserGuideReadMapper`接口，采用注解方式实现SQL查询：

```java
@Mapper
public interface UserGuideReadMapper extends BaseMapper<UserGuideRead> {
    @Select("SELECT COUNT(1) > 0 FROM user_guide_read WHERE user_code = #{userCode} AND feature_code = #{featureCode} AND delete_flag = 0")
    Boolean checkUserRead(@Param("userCode") String userCode, @Param("featureCode") String featureCode);
}
```

### 6.2 服务层

实现了`UserGuideService`接口：

```java
public interface UserGuideService {
    boolean checkUserRead(String featureCode, String userCode);
    boolean markRead(String userCode, String featureCode, String operator);
}
```

服务实现类`UserGuideServiceImpl`中实现了核心业务逻辑：

```java
@Service
public class UserGuideServiceImpl implements UserGuideService {
    @Autowired
    private UserGuideReadMapper userGuideReadMapper;

    @Override
    public boolean checkUserRead(String featureCode, String userCode) {
        return userGuideReadMapper.checkUserRead(userCode, featureCode);
    }

    @Override
    public boolean markRead(String userCode, String featureCode, String operator) {
        UserGuideRead record = new UserGuideRead();
        record.setUserCode(userCode);
        record.setFeatureCode(featureCode);
        record.setReadTime(new Date());
        record.setCreateBy(operator);
        record.setUpdateBy(operator);
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());
        record.setDeleteFlag(0);
        return userGuideReadMapper.insert(record) > 0;
    }
}
```

### 6.3 控制器层

实现了RESTful风格的API接口：

```java
@RestController
@RequestMapping("/api/guides")
public class UserGuideController {
    @Autowired
    private UserGuideService userGuideService;

    @GetMapping("/check")
    public Map<String, Object> checkGuide(@RequestParam String featureCode, @RequestParam String userCode) {
        boolean hasRead = userGuideService.checkUserRead(featureCode, userCode);
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("data", hasRead);
        return result;
    }

    @PostMapping("/read")
    public Map<String, Object> markRead(@RequestBody UserGuideReadDTO dto) {
        boolean success = userGuideService.markRead(dto.getUserCode(), dto.getFeatureCode(), "system");
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("data", success);
        return result;
    }
}
```

## 7. 代码规范

所有Java类文件都添加了标准的文件头注释：

```java
/**
 * <AUTHOR>
 * @version 1.0
 * @description 类描述
 * @since 2023-11-20
 */
```

## 8. 后续工作

### 8.1 客户端SDK开发

需要在客户端实现：
- 网络请求工具类
- 引导UI组件
- 引导管理器

### 8.2 测试与部署

- 进行完整的功能测试
- 部署到测试环境
- 灰度发布到生产环境

### 8.3 文档完善

- 编写详细的API文档
- 编写使用说明
- 编写常见问题解答 