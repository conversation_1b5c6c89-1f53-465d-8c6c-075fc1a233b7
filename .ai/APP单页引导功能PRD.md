# APP单页引导功能产品需求文档(PRD)

## 1. 产品概述

### 1.1 背景

当APP上线新功能时，用户可能不了解如何使用这些功能。为提升用户体验，需要一个引导系统向用户展示功能使用方法。

### 1.2 目标

开发一个简洁的引导功能系统，在用户首次访问特定页面时展示功能引导，帮助用户快速了解新功能的使用方法，同时确保每个功能引导只向用户展示一次。

## 2. 功能需求

### 2.1 核心功能

1. **引导展示**：当用户首次访问含有新功能的页面时，展示引导提示
2. **已读记录**：记录用户已查看的引导，确保同一引导不重复展示

### 2.2 用户流程

1. 用户进入特定页面
2. 系统检查用户是否已读过该页面功能引导
3. 若未读，展示引导提示
4. 用户查看引导后，系统标记为已读
5. 后续访问同一页面不再展示该引导

## 3. 技术方案

### 3.1 数据结构

#### 用户已读记录表(user_guide_read)


| 字段名       | 类型        | 描述              |
| ------------ | ----------- | ----------------- |
| id           | bigint      | 主键ID            |
| user_code    | varchar(10) | 用户编码          |
| feature_code | varchar(10) | 页面功能编码      |
| read_time    | datetime    | 阅读时间          |
| create_by    | varchar(10) | 创建人            |
| create_time  | datetime    | 创建时间          |
| update_by    | varchar(10) | 修改人            |
| update_time  | datetime    | 修改时间          |
| delete_flag  | tinyint(1)  | 是否删除: 0否,1是 |

### 3.2 接口设计

#### 3.2.1 检查功能引导是否已读

- **请求方式**：GET
- **请求路径**：/api/guides/check
- **请求参数**：
  - featureCode: String (必填) - 页面功能编码
  - userCode: String (必填) - 用户编码
- **响应数据**：
  ```json
  {
    "code": 200,
    "data": true
  }
  ```

#### 3.2.2 标记已读

- **请求方式**：POST
- **请求路径**：/api/guides/read
- **请求参数**：
  ```json
  {
    "userCode": "U1001",
    "featureCode": "FILTER01"
  }
  ```
- **响应数据**：
  ```json
  {
    "code": 200,
    "data": true
  }
  ```

## 4. 客户端实现指南

### 4.1 引导内容定义

客户端需要定义各功能的引导内容，包括：

- 引导文本
- 引导图片
- 展示位置
- 功能编码(featureCode)

### 4.2 引导展示逻辑

1. 页面加载时调用检查接口
2. 根据返回结果决定是否展示引导
3. 用户完成引导查看后调用标记已读接口

### 4.3 UI设计建议

- 引导应突出显示，但不应完全阻碍用户操作
- 提供明确的关闭按钮
- 支持多步引导展示
- 考虑不同设备屏幕尺寸的适配

## 5. 测试要点

### 5.1 功能测试

- 首次访问页面时引导是否正确展示
- 标记已读后再次访问是否不再展示
- 多个功能引导是否能正确区分
