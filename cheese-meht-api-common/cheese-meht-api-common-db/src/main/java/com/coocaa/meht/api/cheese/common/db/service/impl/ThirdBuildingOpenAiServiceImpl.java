package com.coocaa.meht.api.cheese.common.db.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.api.cheese.common.db.mapper.ThirdBuildingOpenAiMapper;
import com.coocaa.meht.api.cheese.common.db.entity.ThirdBuildingOpenAi;
import com.coocaa.meht.api.cheese.common.db.service.ThirdBuildingOpenAiService;
/**
* <AUTHOR>
* @version 1.0
* @since 2025-05-29
*/
@Service
public class ThirdBuildingOpenAiServiceImpl extends ServiceImpl<ThirdBuildingOpenAiMapper, ThirdBuildingOpenAi> implements ThirdBuildingOpenAiService{

}
