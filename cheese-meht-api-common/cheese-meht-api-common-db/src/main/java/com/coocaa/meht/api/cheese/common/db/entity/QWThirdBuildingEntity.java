package com.coocaa.meht.api.cheese.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2025/5/27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("temp_third_building_qw")
public class QWThirdBuildingEntity {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    // 楼宇名称
    private String buildingName;

    // 楼宇编号
    private String buildingNo;

    // 价格（元/平米）
    private String thirdBuildingPrice;

    // 建筑等级
    private String thirdBuildingGrade;

    // 建筑位置
    private String thirdBuildingLocation;

    // 建筑年龄（年）
    private String thirdBuildingAge;

    // 建筑外立面材料
    private String thirdBuildingExterior;

    // 建筑评分
    private String thirdBuildingRate;

    // 建筑数量
    private String thirdBuildingNumber;

    // 车库信息
    private String thirdBuildingGarage;

    // 建筑品牌
    private String thirdBuildingBrand;

    // 大堂信息
    private String thirdBuildingLobby;

    // 建筑类型
    private String thirdBuildingType;
}
