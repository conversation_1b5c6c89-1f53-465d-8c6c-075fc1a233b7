package com.coocaa.meht.api.cheese.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.meht.api.cheese.bean.building.AiThirdBuildingParam;
import com.coocaa.meht.api.cheese.bean.building.BuildingGeneVO;
import com.coocaa.meht.api.cheese.bean.building.BuildingMetaDetailVO;
import com.coocaa.meht.api.cheese.bean.building.BuildingMetaImport;
import com.coocaa.meht.api.cheese.bean.building.BuildingMetaImportParam;
import com.coocaa.meht.api.cheese.bean.building.BuildingMetaPointDetailVO;
import com.coocaa.meht.api.cheese.bean.building.BuildingMetaQueryDTO;
import com.coocaa.meht.api.cheese.bean.building.BuildingMetaUpdateParam;
import com.coocaa.meht.api.cheese.bean.building.RefreshForbiddenIndustryParam;
import com.coocaa.meht.api.cheese.bean.building.SspBuildingVO;
import com.coocaa.meht.api.cheese.bean.building.SspSyncBuilding;
import com.coocaa.meht.api.cheese.bean.building.UpdateBaseMessageParam;
import com.coocaa.meht.api.cheese.bean.point.PointDetail;
import com.coocaa.meht.api.cheese.common.db.bean.PointDetailVO;
import com.coocaa.meht.api.cheese.common.db.dto.ProjectForbidIndustriesDTO;
import com.coocaa.meht.api.cheese.common.db.entity.BuildingMetaEntity;
import com.coocaa.meht.api.cheese.common.db.entity.BuildingMetaImgRelationEntity;
import com.coocaa.meht.api.cheese.common.db.entity.BuildingPropertyCompanyEntity;
import com.coocaa.meht.api.cheese.common.db.entity.BuildingRatingEntity;
import com.coocaa.meht.api.cheese.common.db.entity.DBThirdBuildingEntity;
import com.coocaa.meht.api.cheese.common.db.entity.DeepSeekThirdBuildingEntity;
import com.coocaa.meht.api.cheese.common.db.entity.PointContractSnapshotEntity;
import com.coocaa.meht.api.cheese.common.db.entity.PointPicContractSnapshotEntity;
import com.coocaa.meht.api.cheese.common.db.entity.PointPicPriceSnapshotEntity;
import com.coocaa.meht.api.cheese.common.db.entity.PointPriceSnapshotEntity;
import com.coocaa.meht.api.cheese.common.db.entity.PropertyCompanyEntity;
import com.coocaa.meht.api.cheese.common.db.entity.PropertyCompanyPersonEntity;
import com.coocaa.meht.api.cheese.common.db.entity.QWThirdBuildingEntity;
import com.coocaa.meht.api.cheese.common.db.entity.TaskScoreDetailEntity;
import com.coocaa.meht.api.cheese.common.db.entity.ThirdBuildingOpenAi;
import com.coocaa.meht.api.cheese.common.db.service.IBuildingMetaImgRelationService;
import com.coocaa.meht.api.cheese.common.db.service.IBuildingMetaService;
import com.coocaa.meht.api.cheese.common.db.service.IBuildingPropertyCompanyService;
import com.coocaa.meht.api.cheese.common.db.service.IBuildingRatingService;
import com.coocaa.meht.api.cheese.common.db.service.IDBThirdBuildingService;
import com.coocaa.meht.api.cheese.common.db.service.IDeepSeekThirdBuildingService;
import com.coocaa.meht.api.cheese.common.db.service.IPointContractSnapshotService;
import com.coocaa.meht.api.cheese.common.db.service.IPointPicContractSnapshotService;
import com.coocaa.meht.api.cheese.common.db.service.IPointPicPriceSnapshotService;
import com.coocaa.meht.api.cheese.common.db.service.IPointPriceSnapshotService;
import com.coocaa.meht.api.cheese.common.db.service.IPointService;
import com.coocaa.meht.api.cheese.common.db.service.IPropertyCompanyPersonService;
import com.coocaa.meht.api.cheese.common.db.service.IPropertyCompanyService;
import com.coocaa.meht.api.cheese.common.db.service.IQWThirdBuildingService;
import com.coocaa.meht.api.cheese.common.db.service.ISysFileService;
import com.coocaa.meht.api.cheese.common.db.service.ThirdBuildingOpenAiService;
import com.coocaa.meht.api.cheese.common.tools.bean.common.CodeNameVO;
import com.coocaa.meht.api.cheese.common.tools.easyexcel.EasyExcelUtils;
import com.coocaa.meht.api.cheese.common.tools.enums.BooleFlagEnum;
import com.coocaa.meht.api.cheese.common.tools.enums.BuildingMetaCreateTypeEnum;
import com.coocaa.meht.api.cheese.common.tools.enums.BuildingStatusEnum;
import com.coocaa.meht.api.cheese.common.tools.enums.BuildingTypeEnum;
import com.coocaa.meht.api.cheese.common.tools.enums.PropertyTypeEnum;
import com.coocaa.meht.api.cheese.common.tools.result.PageRequestVo;
import com.coocaa.meht.api.cheese.common.tools.result.PageResponseVo;
import com.coocaa.meht.api.cheese.common.tools.result.ResultTemplate;
import com.coocaa.meht.api.cheese.common.tools.utils.RpcUtils;
import com.coocaa.meht.api.cheese.common.tools.utils.StringUtils2;
import com.coocaa.meht.api.cheese.common.tools.utils.UserCodeUtils;
import com.coocaa.meht.api.cheese.utils.BaiduApiService;
import com.coocaa.meht.api.cheese.utils.MehtResultUtils;
import com.coocaa.meht.api.cheese.utils.RsaExample;
import com.coocaa.meht.api.cheese.utils.CodeNameHelper;
import com.coocaa.meht.api.cheese.utils.RentAiService;
import com.coocaa.meht.api.cheese.convert.BuildingMetaConvert;
import com.coocaa.meht.api.cheese.convert.PointConvert;
import com.coocaa.meht.api.cheese.convert.PropertyCompanyPersonConvert;
import com.coocaa.meht.api.cheese.enums.BuildingMetaImgTypeEnum;
import com.coocaa.meht.api.cheese.exception.BusinessException;
import com.coocaa.meht.api.cheese.rpc.AuthorityRpcService;
import com.coocaa.meht.api.cheese.rpc.FeignAuthorityRpc;
import com.coocaa.meht.api.cheese.rpc.FeignMehtRpc;
import com.coocaa.meht.api.cheese.rpc.FeignSspRpc;
import com.coocaa.meht.api.cheese.service.BuildingGeneService;
import com.coocaa.meht.api.cheese.service.BuildingMetaApiService;
import com.coocaa.meht.api.cheese.service.tctask.AiService;
import com.coocaa.meht.api.cheese.service.tctask.TCTaskService;
import com.coocaa.meht.api.cheese.utils.converter.ConverterFactory;
import com.coocaa.meht.api.cheese.vo.BuildingPropertyCompanyVO;
import com.coocaa.meht.api.cheese.vo.PropertyCompanyPersonVO;
import com.coocaa.meht.api.cheese.vo.building.BuildingMetaListVO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.StringJoiner;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/12/16
 */
@Service
@Slf4j
public class BuildingMetaApiServiceImpl implements BuildingMetaApiService {

    public static final String BUILDING_DESC = "楼宇数据同步,没有评级信息";
    public static final String BASE_RENT = "10";
    @Autowired
    private FeignSspRpc feignSspRpc;
    @Autowired
    private IBuildingMetaService buildingMetaService;
    @Autowired
    private IDBThirdBuildingService dbThirdBuildingService;
    @Autowired
    private IDeepSeekThirdBuildingService deepSeekThirdBuildingService;
    @Autowired
    private IQWThirdBuildingService qwThirdBuildingService;
    @Autowired
    private ThirdBuildingOpenAiService thirdBuildingOpenAiService;
    @Autowired
    private AuthorityRpcService authorityRpcService;
    @Autowired
    private BuildingMetaConvert buildingMetaConvert;
    @Autowired
    private CodeNameHelper codeNameHelper;
    @Autowired
    private ConverterFactory converterFactory;
    @Autowired
    private IBuildingMetaImgRelationService buildingMetaImgRelationService;
    @Autowired
    private IPointService pointService;
    @Autowired
    private PointConvert pointConvert;
    @Autowired
    private IPointPriceSnapshotService pointPriceSnapshotService;
    @Autowired
    private IPointContractSnapshotService pointContractSnapshotService;

    @Autowired
    private IPointPicPriceSnapshotService pointPicPriceSnapshotService;
    @Autowired
    private IPointPicContractSnapshotService pointPicContractSnapshotService;
    @Autowired
    private IBuildingRatingService buildingRatingService;

    @Autowired
    private ISysFileService sysFileService;
    @Autowired
    private FeignMehtRpc feignMehtRpc;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private RsaExample rsaExample;
    @Autowired
    private IPropertyCompanyService companyService;
    @Autowired
    private IPropertyCompanyPersonService personService;
    @Autowired
    private IBuildingPropertyCompanyService buildingCompanyService;
    @Autowired
    private BaiduApiService baiduApiService;
    @Autowired
    private FeignAuthorityRpc feignAuthorityRpc;

    @Autowired
    private TCTaskService tcTaskService;
    @Autowired
    private RentAiService rentAiService;
    @Autowired
    private AiService aiService;

    @Resource
    private BuildingGeneService buildingGeneService;


    @Override
    public PageResponseVo<BuildingMetaListVO> listBuildingMeta(PageRequestVo<BuildingMetaQueryDTO> queryDTO) {
        BuildingMetaQueryDTO query = queryDTO.getQuery();
        // 构建查询条件
        LambdaQueryWrapper<BuildingMetaEntity> wrapper = new LambdaQueryWrapper<>();

        // 楼宇ID
        if (StringUtils.isNotBlank(query.getBuildingId())) {
            wrapper.eq(BuildingMetaEntity::getBuildingMetaNo, query.getBuildingId());
        }

        // 楼宇名称
        if (StringUtils.isNotBlank(query.getBuildingName())) {
            wrapper.like(BuildingMetaEntity::getBuildingNameAi, query.getBuildingName());
        }

        // 城市 区县 需要转一下
        Integer cityId = query.getCity();
        Integer district = query.getDistrict();
//        List<String> gbCodes = authorityRpcService.getGBCodeByCityId(cityId, district);
//        if (CollectionUtils.isNotEmpty(gbCodes)) {
//            wrapper.in(BuildingMetaEntity::getMapAdCode, gbCodes);
//        }

        if (cityId != null) {
            ResultTemplate<List<CodeNameVO>> gbByCityId = feignAuthorityRpc.getGbByCityId(cityId);
            if (Objects.nonNull(gbByCityId) && CollectionUtils.isNotEmpty(gbByCityId.getData())) {
                wrapper.in(BuildingMetaEntity::getMapCity, gbByCityId.getData().stream().map(CodeNameVO::getName)
                        .collect(Collectors.toList()));
            }
        }

        if (district != null) {
            ResultTemplate<List<CodeNameVO>> gbByDistrictId = feignAuthorityRpc.getGbByCityId(district);
            if (Objects.nonNull(gbByDistrictId) && CollectionUtils.isNotEmpty(gbByDistrictId.getData())) {
                wrapper.in(BuildingMetaEntity::getMapRegion, gbByDistrictId.getData().stream().map(CodeNameVO::getName)
                        .collect(Collectors.toList()));
            }
        }


        // 认证等级
        if (StringUtils.isNotBlank(query.getCertificationLevel())) {
            wrapper.eq(BuildingMetaEntity::getProjectLevel, query.getCertificationLevel());
        }

        // AI评级
        if (StringUtils.isNotBlank(query.getAiLevel())) {
            wrapper.eq(BuildingMetaEntity::getProjectLevelAi, query.getAiLevel());
        }

        // 负责人
        if (StringUtils.isNotBlank(query.getManager())) {
            wrapper.eq(BuildingMetaEntity::getManager, query.getManager());
        }

        // 认证状态
        if (query.getCertificationStatus() != null) {
            wrapper.eq(BuildingMetaEntity::getBuildingStatus, query.getCertificationStatus());
        }
        wrapper.orderByDesc(BuildingMetaEntity::getCreateTime);
        // 分页查询
        Page<BuildingMetaEntity> page = new Page<>(queryDTO.getCurrentPage(), queryDTO.getPageSize());
        page = buildingMetaService.page(page, wrapper);

        PageResponseVo<BuildingMetaListVO> pageResult = buildingMetaConvert.toPageResponse(page);
        List<BuildingMetaEntity> rows = page.getRecords();
        if (CollectionUtils.isNotEmpty(rows)) {
            List<BuildingMetaListVO> voList = buildingMetaConvert.toBuildingMetaListVOS(rows);
            converterFactory.convert(voList);
            // 填充禁忌行业
            fillingForbiddenIndustry(rows, voList);
            pageResult.setRows(voList);
        }
        return pageResult;
    }

    @Override
    public BuildingMetaDetailVO getMetaDetailById(Long id) {
        BuildingMetaEntity entity = buildingMetaService.getById(id);
        BuildingMetaListVO buildingMetaListVO = buildingMetaConvert.toBuildingMetaListVO(entity);
        //禁忌行业封装
        if (StringUtils.isNotBlank(entity.getForbiddenIndustry())) {
            List<String> codeList = Arrays.asList(entity.getForbiddenIndustry().split(","));
            buildingMetaListVO.setForbiddenIndustry(codeList);
            //查询名称
            Map<String, String> industryMapping = codeNameHelper.getIndustryMapping(codeList);
            List<String> nameList = industryMapping.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .map(Map.Entry::getValue)
                    .collect(Collectors.toList());
            String forbiddenIndustryName = String.join(",", nameList);
            buildingMetaListVO.setForbiddenIndustryName(forbiddenIndustryName);
        }
        BuildingMetaDetailVO vo = buildingMetaConvert.toBuildingMetaDetailVO(buildingMetaListVO);
        //图片
        setPics(vo);
        // 楼宇公司信息
        if (StringUtils.isNotBlank(entity.getMapNo())) {
            setPropertyCompany(entity.getMapNo(), vo);
        }
        //点位详情
        String buildingRatingNo = entity.getBuildingRatingNo();
        if (StringUtils.isNotBlank(buildingRatingNo)) {
            setPointsDetail(buildingRatingNo, vo);

            List<PointContractSnapshotEntity> pointContractList = pointContractSnapshotService.listByBuildingNo(buildingRatingNo);
            if (CollectionUtils.isNotEmpty(pointContractList)) {
                vo.setContractPointPlanId(pointContractList.get(0).getPointPlanId());
            }
            List<PointPriceSnapshotEntity> pointPriceList = pointPriceSnapshotService.listByBuildingNo(buildingRatingNo);
            if (CollectionUtils.isNotEmpty(pointPriceList)) {
                vo.setPricePointPlanId(pointPriceList.get(0).getPointPlanId());
            }
        }
        converterFactory.convert(Lists.newArrayList(vo));
        BuildingGeneVO buildingGene = buildingGeneService.getBuildingGeneByBuildingRatingNo(entity.getBuildingRatingNo());
        vo.setBuildingGene(buildingGene);
        translate(buildingGene);
        vo.setLargeScreen(buildingGeneService.isLargeScreen(buildingGene));
        return vo;
    }

    private void translate(BuildingGeneVO buildingGene) {
        if (buildingGene != null) {
            //禁忌行业封装
            if (StringUtils.isNotBlank(buildingGene.getForbiddenIndustry())) {
                List<String> codeList = Arrays.asList(buildingGene.getForbiddenIndustry().split(","));
                //查询名称
                Map<String, String> industryMapping = codeNameHelper.getIndustryMapping(codeList);
                List<String> nameList = industryMapping.entrySet().stream()
                        .sorted(Map.Entry.comparingByKey())
                        .map(Map.Entry::getValue)
                        .collect(Collectors.toList());
                String forbiddenIndustryName = String.join(",", nameList);
                buildingGene.setForbiddenIndustryName(forbiddenIndustryName);
            }
            if (StringUtils.isNotBlank(buildingGene.getCompetitiveMediaInfo())) {
                JSONArray jsonArray = JSON.parseArray(buildingGene.getCompetitiveMediaInfo());
                List<String> mediaType = jsonArray.stream()
                        .map(obj -> (JSONObject) obj)
                        .map(jsonObject -> jsonObject.getString("mediaType"))
                        .filter(StrUtil::isNotBlank).distinct().toList();
                Map<String, String> mediaTypeDictMapping = codeNameHelper.getDictMapping(mediaType);
                String res = jsonArray.stream()
                        .map(obj -> (JSONObject) obj)
                        .map(jsonObject -> {
                            String code = jsonObject.getString("mediaType");
                            String number = jsonObject.getString("number");
                            return mediaTypeDictMapping.get(code) + "(" + number + ")";
                        }).collect(Collectors.joining("，"));
                buildingGene.setCompetitiveMediaInfoName(res);
            }
        }
    }

    private void setPropertyCompany(String mapNo, BuildingMetaDetailVO vo) {
        BuildingRatingEntity ratingEntity = buildingRatingService.lambdaQuery()
                .eq(BuildingRatingEntity::getMapNo, mapNo)
                .orderByDesc(BuildingRatingEntity::getCreateTime)
                .last("limit 1").one();
        if (Objects.nonNull(ratingEntity)) {
            vo.setPropertyCompanyVOS(getPropertyPerson(ratingEntity.getBuildingNo()));
        }
    }

    private void setPointsDetail(String buildingRatingNo, BuildingMetaDetailVO vo) {
        //改从ssp获取点位信息展示+从H5获取点位的设备信息及到期时间等信息
        ResultTemplate<List<PointDetail>> resultTemplate = feignSspRpc.pointList(buildingRatingNo);
        List<PointDetail> pointDetails = resultTemplate.getData();
        if (ObjectUtil.isNotEmpty(pointDetails)) {
            //查询H5中点位的设备大小
            List<PointDetailVO> details = pointService.listPointToContract(buildingRatingNo);
            Map<String, List<PointDetailVO>> listMap = details.stream()
                    .collect(Collectors.groupingBy(PointDetailVO::getPointCode));
            if (ObjectUtil.isNotEmpty(details)) {
                pointDetails.forEach(e -> {
                    String pointCode = e.getPointCode();
                    List<PointDetailVO> pointDetailList = listMap.get(pointCode);
                    if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(pointDetailList)) {
                        PointDetailVO pointDetailVO = pointDetailList.get(0);
                        e.setDeviceSize(pointDetailVO.getDeviceSize());
                        e.setExpireTime(pointDetailVO.getExpireTime());
                    }
                    e.setBuildingRatingNo(buildingRatingNo);
                });
            }
            List<BuildingMetaPointDetailVO> pointDetailVOS = pointConvert.toPointDetailVOS(pointDetails);
            converterFactory.convert(pointDetailVOS);
            vo.setPointDetailVOS(pointDetailVOS);
        }
    }

    private void setPics(BuildingMetaDetailVO vo) {
        String buildingMetaNo = vo.getBuildingMetaNo();
        List<BuildingMetaImgRelationEntity> imgs = buildingMetaImgRelationService.listByBuildingMetaNo(buildingMetaNo);
        Map<Integer, List<BuildingMetaImgRelationEntity>> map = imgs.stream()
                .collect(Collectors.groupingBy(BuildingMetaImgRelationEntity::getImgType));
        if (MapUtils.isNotEmpty(map)) {
            map.forEach((k, v) -> {
                List<String> pics = v.stream().map(BuildingMetaImgRelationEntity::getImgUrl)
                        .collect(Collectors.toList());
                if (Objects.equals(k, BuildingMetaImgTypeEnum.EXTERIOR_PIC.getType())) {
                    vo.setBuildingExteriorPics(pics);
                }
                if (Objects.equals(k, BuildingMetaImgTypeEnum.LOBBY_PIC.getType())) {
                    vo.setBuildingLobbyPics(pics);
                }
                if (Objects.equals(k, BuildingMetaImgTypeEnum.HALL_PIC.getType())) {
                    vo.setBuildingHallPics(pics);
                }
                if (Objects.equals(k, BuildingMetaImgTypeEnum.ELEVATOR_PIC.getType())) {
                    vo.setBuildingElevatorPic(pics);
                }
                if (Objects.equals(k, BuildingMetaImgTypeEnum.GATE_PIC.getType())) {
                    vo.setBuildingGatePic(pics);
                }
                if (Objects.equals(k, BuildingMetaImgTypeEnum.INSTALL_PIC.getType())) {
                    vo.setBuildingInstallationPic(pics);
                }
            });
        }
    }

    @Override
    @Transactional
    public void updateMeta(BuildingMetaUpdateParam param) {
        Long id = param.getId();
        //只有已认证的才可以修改
        BuildingMetaEntity entity = buildingMetaService.getById(id);
        if (!BuildingStatusEnum.CONFIRMED.getCode().equals(entity.getBuildingStatus())) {
            throw new BusinessException("只有已认证状态的数据可以修改");
        }

        String buildingMetaNo = entity.getBuildingMetaNo();

        updateBuildingMeta(param, entity.getBuildingRatingNo());
        updateBuildingRating(param, entity.getBuildingRatingNo());

        // 处理图片
        savePic(param.getBuildingExteriorPics(), buildingMetaNo, BuildingMetaImgTypeEnum.EXTERIOR_PIC);
        savePic(param.getBuildingHallPics(), buildingMetaNo, BuildingMetaImgTypeEnum.HALL_PIC);
        savePic(param.getBuildingLobbyPics(), buildingMetaNo, BuildingMetaImgTypeEnum.LOBBY_PIC);

        savePic(param.getBuildingElevatorPic(), buildingMetaNo, BuildingMetaImgTypeEnum.ELEVATOR_PIC);
        savePic(param.getBuildingGatePic(), buildingMetaNo, BuildingMetaImgTypeEnum.GATE_PIC);
        savePic(param.getBuildingInstallationPic(), buildingMetaNo, BuildingMetaImgTypeEnum.INSTALL_PIC);
    }

    private void updateBuildingRating(BuildingMetaUpdateParam param, String buildingRatingNo) {
        buildingRatingService.lambdaUpdate()
                .eq(BuildingRatingEntity::getBuildingNo, buildingRatingNo)
                .set(BuildingRatingEntity::getUpdateBy, UserCodeUtils.getUserCode())
                .set(StringUtils.isNotBlank(param.getBuildingName()), BuildingRatingEntity::getBuildingName, param.getBuildingName())
                .set(StringUtils.isNotBlank(param.getMapCity()), BuildingRatingEntity::getMapCity, param.getMapCity())
                .set(StringUtils.isNotBlank(param.getMapProvince()), BuildingRatingEntity::getMapProvince, param.getMapProvince())
                .set(StringUtils.isNotBlank(param.getMapRegion()), BuildingRatingEntity::getMapRegion, param.getMapRegion())
                .set(StringUtils.isNotBlank(param.getMapAddress()), BuildingRatingEntity::getMapAddress, rsaExample.encryptByPublic(param.getMapAddress()))
                .update();
    }

    private void savePic(List<String> pics, String buildingMetaNo, BuildingMetaImgTypeEnum imgTypeEnum) {
        if (pics != null) {
            buildingMetaImgRelationService.saveByType(buildingMetaNo, pics, imgTypeEnum.getType());
        }
    }

    private void updateBuildingMeta(BuildingMetaUpdateParam param, String buildingMetaNo) {
        LambdaUpdateWrapper<BuildingMetaEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(BuildingMetaEntity::getId, param.getId());

        updateWrapper.set(StringUtils.isNotBlank(param.getBuildingName()),
                BuildingMetaEntity::getBuildingName, param.getBuildingName());

        updateWrapper.set(param.getTargetPointCount() != null,
                BuildingMetaEntity::getTargetPointCount, param.getTargetPointCount());

        updateWrapper.set(param.getCompetitorPointCount() != null,
                BuildingMetaEntity::getCompetitorPointCount, param.getCompetitorPointCount());
        // 更新省市区及详细地址
        updateWrapper.set(StringUtils.isNotBlank(param.getMapProvince()),
                BuildingMetaEntity::getMapProvince, param.getMapProvince());
        updateWrapper.set(StringUtils.isNotBlank(param.getMapCity()),
                BuildingMetaEntity::getMapCity, param.getMapCity());
        updateWrapper.set(StringUtils.isNotBlank(param.getMapRegion()),
                BuildingMetaEntity::getMapRegion, param.getMapRegion());

        if (StringUtils.isNotBlank(param.getMapAddress())) {
            updateWrapper.set(BuildingMetaEntity::getMapAddress, rsaExample.encryptByPublic(param.getMapAddress()));
        }
        updateWrapper.set(BuildingMetaEntity::getUpdateBy, UserCodeUtils.getUserCode());

        //禁忌行业修改
        List<String> forbiddenIndustry = param.getForbiddenIndustry();
        if (CollectionUtils.isNotEmpty(forbiddenIndustry)) {
            if (forbiddenIndustry.size() == 1 && "无".equals(forbiddenIndustry.get(0))) {
                //清除关系，表示没有禁忌行业
                updateWrapper.set(BuildingMetaEntity::getForbiddenIndustry, null);
            } else {
                //如果包含了无，并且长度大于1，抛出异常
                if (forbiddenIndustry.contains("无") && forbiddenIndustry.size() > 1) {
                    throw new BusinessException("禁止行业选择无不能再选其他的行业");
                }
                updateWrapper.set(BuildingMetaEntity::getForbiddenIndustry, String.join(",", forbiddenIndustry));
            }
            //修改成功则查询楼宇是否有对应的项目，无则不处理，有则修改项目对应的禁忌行业
            ProjectForbidIndustriesDTO pDto = ProjectForbidIndustriesDTO.builder()
                    .forbidIndustry(String.join(",", forbiddenIndustry))
                    .buildingNo(buildingMetaNo).build();
            //同步修改项目的信息
            RpcUtils.unBox(feignSspRpc.updateProjectForbidIndustries(pDto));
        }
        buildingMetaService.update(updateWrapper);
    }

    @Override
    public List<BuildingMetaPointDetailVO> getPricePointDetailByPointPlanId(Integer pointPlanId) {
        List<PointPriceSnapshotEntity> pointPriceSnapshotEntities = pointPriceSnapshotService.listByPointPlanId(pointPlanId);
        if (CollectionUtils.isNotEmpty(pointPriceSnapshotEntities)) {
            List<BuildingMetaPointDetailVO> vos = buildingMetaConvert.toBuildingMetaPointDetailVOS(pointPriceSnapshotEntities);
            converterFactory.convert(vos);
            Set<Integer> pointIds = vos.stream().map(BuildingMetaPointDetailVO::getPointId).collect(Collectors.toSet());
            List<PointPicPriceSnapshotEntity> list = pointPicPriceSnapshotService.listByPointIds(pointIds);
            if (CollectionUtils.isNotEmpty(list)) {
                Map<Integer, List<PointPicPriceSnapshotEntity>> map = list.stream()
                        .collect(Collectors.groupingBy(PointPicPriceSnapshotEntity::getPointId));
                vos.forEach(e -> {
                    List<PointPicPriceSnapshotEntity> pics = map.get(e.getPointId());
                    if (CollectionUtils.isNotEmpty(pics)) {
                        e.setPointPics(pics.stream().map(PointPicPriceSnapshotEntity::getPic)
                                .collect(Collectors.toList()));
                    }
                });
            }
//            return vos;
//            List<Integer> pointIds = vos.stream().map(BuildingMetaPointDetailVO::getPointId).collect(Collectors.toList());
//            ResultTemplate<List<SspPointPicVO>> resultTemplate = feignSspRpc.pointPicListByPointId(pointIds);
//            log.info("getPricePointDetailByPointPlanId.resultTemplate req:{},res:{}", JSONUtil.toJsonStr(pointIds), JSONUtil.toJsonStr(resultTemplate));
//            if (ObjectUtil.isNotNull(resultTemplate.getData())) {
//                vos.forEach(p -> {
//                    List<String> pics = resultTemplate.getData().stream().filter(f -> f.getPointId().equals(p.getPointId())).map(SspPointPicVO::getPic).collect(Collectors.toList());
//                    if (CollectionUtils.isNotEmpty(pics)) {
//                        p.setPointPics(pics);
//                    }
//                });
//
//            }
//            converterFactory.convert(vos);
            return vos;
        }
        return new ArrayList<>();
    }

    @Override
    public List<BuildingMetaPointDetailVO> getContractPointDetailByPointPlanId(Integer pointPlanId) {
        List<PointContractSnapshotEntity> pointContractSnapshotList = pointContractSnapshotService.listByPointPlanId(pointPlanId);
        if (CollectionUtils.isNotEmpty(pointContractSnapshotList)) {
            List<BuildingMetaPointDetailVO> vos = buildingMetaConvert.toBuildingMetaPointDetailVOListByContract(pointContractSnapshotList);
            converterFactory.convert(vos);
            Set<Integer> pointIds = vos.stream().map(BuildingMetaPointDetailVO::getPointId).collect(Collectors.toSet());

            List<PointPicContractSnapshotEntity> list = pointPicContractSnapshotService.listByPointIds(pointIds);
            if (CollectionUtils.isNotEmpty(list)) {
                Map<Integer, List<PointPicContractSnapshotEntity>> map = list.stream()
                        .collect(Collectors.groupingBy(PointPicContractSnapshotEntity::getPointId));
                vos.forEach(e -> {
                    List<PointPicContractSnapshotEntity> pics = map.get(e.getPointId());
                    if (CollectionUtils.isNotEmpty(pics)) {
                        e.setPointPics(pics.stream().map(PointPicContractSnapshotEntity::getPic)
                                .collect(Collectors.toList()));
                    }
                });
            }
            return vos;
//            List<Integer> pointIds = vos.stream().map(BuildingMetaPointDetailVO::getPointId).collect(Collectors.toList());
//            ResultTemplate<List<SspPointPicVO>> resultTemplate = feignSspRpc.pointPicListByPointId(pointIds);
//            log.info("getContractPointDetailByPointPlanId.resultTemplate req:{},res:{}", JSONUtil.toJsonStr(pointIds), JSONUtil.toJsonStr(resultTemplate));
//            if (ObjectUtil.isNotNull(resultTemplate.getData())) {
//                vos.forEach(p -> {
//                    List<String> pics = resultTemplate.getData().stream().filter(f -> f.getPointId().equals(p.getPointId())).map(SspPointPicVO::getPic).collect(Collectors.toList());
//                    if (CollectionUtils.isNotEmpty(pics)) {
//                        p.setPointPics(pics);
//                    }
//                });
//
//            }
//            return vos;
        }
        return new ArrayList<>();
    }

    @Override
    @Transactional
    public void syncBuildingMeta() {
        updateBuildingMeta();
    }

    public void updateBuildingMeta() {
        List<BuildingMetaEntity> list = buildingMetaService.list();
        Map<String, BuildingMetaEntity> metaMap = list.stream()
                .collect(Collectors.toMap(BuildingMetaEntity::getMapNo, Function.identity()));

        List<BuildingRatingEntity> entities = buildingRatingService.list();
        Map<String, List<BuildingRatingEntity>> map = entities.stream()
                .collect(Collectors.groupingBy(BuildingRatingEntity::getMapNo));
        map.forEach((mapNo, vos) -> {
            BuildingRatingEntity buildingRating = vos.get(vos.size() - 1);
            BuildingMetaEntity metaEntity = metaMap.get(mapNo);
            String metaNo;
            if (metaEntity == null) {
                metaNo = MehtResultUtils.unbox(feignMehtRpc.getBuildMetaNo());

                //获取楼宇主数据no todo已迁移待删除
//                metaNo = getBuildingMetaNo();
            } else {
                metaNo = metaEntity.getBuildingMetaNo();
            }
            buildingMetaService.sync(metaEntity, buildingRating, metaNo);
            //处理图片
          /*  saveExteriorPic(metaNo, buildingRating);
            saveBuildingLobbyPic(metaNo, buildingRating);
            saveHallPic(metaNo, buildingRating);*/
        });
    }

    private void saveHallPic(String metaNo, BuildingRatingEntity buildingRatingEntity) {
        String buildingHallPic = buildingRatingEntity.getBuildingHallPic();
        if (StringUtils.isNotBlank(buildingHallPic)) {
            String[] split = buildingHallPic.split(",");
            List<BuildingMetaImgRelationEntity> images = Arrays.stream(split).map(imageId -> {
                BuildingMetaImgRelationEntity img = new BuildingMetaImgRelationEntity();
                img.setBuildingMetaNo(metaNo);
                img.setImgUrl(sysFileService.getById(Long.parseLong(imageId)).getUrl());
                img.setCreateBy(buildingRatingEntity.getCreateBy());
                img.setImgType(BuildingMetaImgTypeEnum.LOBBY_PIC.getType());
                return img;
            }).collect(Collectors.toList());
            buildingMetaImgRelationService.saveBatch(images);
        }
    }

    private void saveBuildingLobbyPic(String metaNo, BuildingRatingEntity buildingRatingEntity) {
        String buildingLobbyPic = buildingRatingEntity.getBuildingLobbyPic();
        if (StringUtils.isNotBlank(buildingLobbyPic)) {
            String[] split = buildingLobbyPic.split(",");
            List<BuildingMetaImgRelationEntity> images = Arrays.stream(split).map(imageId -> {
                BuildingMetaImgRelationEntity img = new BuildingMetaImgRelationEntity();
                img.setBuildingMetaNo(metaNo);
                img.setImgUrl(sysFileService.getById(Long.parseLong(imageId)).getUrl());
                img.setCreateBy(buildingRatingEntity.getCreateBy());
                img.setImgType(BuildingMetaImgTypeEnum.LOBBY_PIC.getType());
                return img;
            }).collect(Collectors.toList());
            buildingMetaImgRelationService.saveBatch(images);
        }
    }

    private void saveExteriorPic(String metaNo, BuildingRatingEntity buildingRatingEntity) {
        String buildingExteriorPic = buildingRatingEntity.getBuildingExteriorPic();
        if (StringUtils.isNotBlank(buildingExteriorPic)) {
            String[] split = buildingExteriorPic.split(",");
            List<BuildingMetaImgRelationEntity> images = Arrays.stream(split).map(imageId -> {
                BuildingMetaImgRelationEntity img = new BuildingMetaImgRelationEntity();
                img.setBuildingMetaNo(metaNo);
                img.setImgUrl(sysFileService.getById(Long.parseLong(imageId)).getUrl());
                img.setCreateBy(buildingRatingEntity.getCreateBy());
                img.setImgType(1);
                return img;
            }).collect(Collectors.toList());
            buildingMetaImgRelationService.saveBatch(images);
        }
    }

    @Override
    @Transactional
    public void firstSave(BuildingMetaImportParam param) {
        String url = param.getUrl();
        importBuilding(url);
    }

    @Override
    public List<BuildingPropertyCompanyVO> getPropertyPerson(String buildingNo) {
        List<BuildingPropertyCompanyVO> resultVOS = new ArrayList<>();
        // code存在则查该商机下物业，如果有客户id则查客户下所有物业
        // 商机物业关联表
        List<BuildingPropertyCompanyEntity> entities = buildingCompanyService.list(new LambdaQueryWrapper<BuildingPropertyCompanyEntity>()
                .eq(StringUtils.isNotBlank(buildingNo), BuildingPropertyCompanyEntity::getBuildingNo, buildingNo));
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(entities)) {
            return Collections.emptyList();
        }
        // 物业
        List<Integer> buildingCompanyIds = entities.stream().map(BuildingPropertyCompanyEntity::getPropertyId).toList();
        Map<Integer, PropertyCompanyEntity> companyMap = companyService.list(new LambdaQueryWrapper<PropertyCompanyEntity>()
                        .eq(PropertyCompanyEntity::getStatus, BooleFlagEnum.YES.getCode())
                        .in(PropertyCompanyEntity::getId, buildingCompanyIds)).stream()
                .collect(Collectors.toMap(PropertyCompanyEntity::getId, c -> c));
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(companyMap)) {
            return Collections.emptyList();
        }
        // 物业联系人
        Map<Integer, List<PropertyCompanyPersonEntity>> personMap = personService.list(new LambdaQueryWrapper<PropertyCompanyPersonEntity>()
                        .in(PropertyCompanyPersonEntity::getCompanyId, companyMap.keySet())).stream()
                .collect(Collectors.groupingBy(PropertyCompanyPersonEntity::getCompanyId));
        // 组装
        entities.forEach(entity -> {
            if (Objects.nonNull(companyMap.get(entity.getPropertyId()))) {
                BuildingPropertyCompanyVO resultVO = new BuildingPropertyCompanyVO();
                BeanUtils.copyProperties(entity, resultVO);
                BeanUtils.copyProperties(companyMap.get(entity.getPropertyId()), resultVO);
                List<PropertyCompanyPersonVO> personVOS = PropertyCompanyPersonConvert.INSTANCE.toVOs(personMap.getOrDefault(entity.getPropertyId(), Collections.emptyList()));
                converterFactory.convert(personVOS);
                resultVO.setPersonVOS(personVOS);
                resultVOS.add(resultVO);
            }
        });
        return resultVOS;
    }

    /**
     * 获取楼宇主数据no
     */
    public String getBuildingMetaNo() {
        String localDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String key = "meta:id:" + localDate;
        Long increment = redisTemplate.opsForValue().increment(key);
        redisTemplate.expire(key, 1, TimeUnit.DAYS);
        return "BC" + localDate + StringUtils2.file5Code(increment.intValue());
    }


    public void importBuilding(String excelUrl) {
        BuildingRatingListener listener = new BuildingRatingListener();
        // String path = "D:\\楼宇主数据.xlsx";
        try {
            URL url = new URL(excelUrl);
            InputStream inputStream = url.openStream();
            EasyExcel.read(inputStream,
                            BuildingMetaImport.class, listener)
                    .sheet()
                    .doRead();
            List<BuildingMetaImport> list = listener.getList();
            list = list.stream()
                    .filter(e -> com.coocaa.meht.api.cheese.common.tools.utils.StringUtils.isNotBlank(e.getMapNo()))
                    .filter(e -> com.coocaa.meht.api.cheese.common.tools.utils.StringUtils.isNotBlank(e.getRatingLevel()))
                    .filter(e -> !e.getRatingLevel().equals("不合格"))
                    .filter(e -> !e.getBuildingName().equals("building_name"))
                    .collect(Collectors.toList());
            //mapNo可能有重复的
            Map<String, List<BuildingMetaImport>> map = list.stream()
                    .collect(Collectors.groupingBy(BuildingMetaImport::getMapNo));
            //  String buildNoPrefix = "BC"+ LocalDate.now().format(DateTimeFormatter.ofPattern(DatePattern.PATTERN_PURE_DATE));
            //   AtomicInteger i = new AtomicInteger(1);
            List<BuildingMetaEntity> r = map.values().stream().map(buildingMetaImports -> {

                BuildingMetaImport buildingMetaImport = buildingMetaImports.get(0);
                BuildingMetaEntity metaEntity = new BuildingMetaEntity();
                metaEntity.setBuildingMetaNo(MehtResultUtils.unbox(feignMehtRpc.getBuildMetaNo()));
                //获取楼宇主数据no todo已迁移待删除
//                metaEntity.setBuildingNameAi(getBuildingMetaNo());
                String buildingType = buildingMetaImport.getBuildingType();
                try {
                    metaEntity.setBuildingTypeAi(BuildingTypeEnum.getByDesc(buildingType).getCode());
                } catch (Exception e) {
                    log.error("枚举转换出错:{}", buildingType, e);
                    throw new BusinessException("导入数据枚举转换出错");
                }

                metaEntity.setMapNo(buildingMetaImport.getMapNo());
                metaEntity.setProjectLevelAi(buildingMetaImport.getRatingLevel());
                metaEntity.setCreateType(BuildingMetaCreateTypeEnum.SYSTEM.getCode());
                metaEntity.setCreateBy("0");
                metaEntity.setUpdateBy("0");
                // i.getAndIncrement();
                return metaEntity;
            }).collect(Collectors.toList());

            buildingMetaService.saveBatch(r);
        } catch (Exception e) {
            log.error("导入主数据报错:", e);
            throw new BusinessException("导入数据报错");
        }

    }


    static class BuildingRatingListener extends AnalysisEventListener<BuildingMetaImport> {
        private List<BuildingMetaImport> list = new ArrayList<>();

        @Override
        public void invoke(BuildingMetaImport data, AnalysisContext context) {
            list.add(data);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            // 可以在这里处理完整的数据列表
        }

        public List<BuildingMetaImport> getList() {
            return list;
        }
    }

    @Override
    public List<SspBuildingVO> queryByName(Collection<String> names) {
        if (CollectionUtils.isNotEmpty(names)) {
            List<BuildingMetaEntity> entities = buildingMetaService.queryByName(names);
            if (CollectionUtils.isNotEmpty(entities)) {
                return buildingMetaConvert.toSspBuildingVOS(entities);
            }
        }
        return new ArrayList<>();
    }

    @Override
    @Transactional
    public String syncBySsp(SspSyncBuilding sspSyncBuilding) {
        String mapNo = sspSyncBuilding.getMapNo();
        BuildingRatingEntity exist = buildingRatingService.getByMapNo(mapNo);
        if (exist != null) {
            //直接返回不需要刷数据
            return exist.getBuildingNo();
        }
        //存储楼宇评级表
        BuildingRatingEntity buildingRating = new BuildingRatingEntity();
        String userCode = "CC0000";
        buildingRating.setBuildingName(sspSyncBuilding.getProject());
        buildingRating.setCreateBy(userCode);
        buildingRating.setSubmitUser(userCode);
        buildingRating.setSubmitTime(new Date());
        buildingRating.setApproveUser(userCode);
        buildingRating.setApproveTime(new Date());
        buildingRating.setStatus(1);
        buildingRating.setUpdateBy(userCode);
        buildingRating.setBuildingStatus(3);
        buildingRating.setMapProvince(sspSyncBuilding.getProvince());

        String propertyType = sspSyncBuilding.getPropertyType();
        Integer type = PropertyTypeEnum.getByCode(propertyType).getType();
        buildingRating.setBuildingType(type);
        String addressDetail = sspSyncBuilding.getAddress();

        if (StringUtils.isNotBlank(addressDetail)) {
            buildingRating.setMapAddress(rsaExample.encryptByPublic(addressDetail));
        }
        String latitude = sspSyncBuilding.getLatitude();
        if (StringUtils.isNotBlank(latitude)) {
            buildingRating.setMapLatitude(rsaExample.encryptByPublic(latitude));
        }
        String longitude = sspSyncBuilding.getLongitude();
        if (StringUtils.isNotBlank(longitude)) {
            buildingRating.setMapLongitude(rsaExample.encryptByPublic(longitude));
        }

        buildingRating.setMapCity(sspSyncBuilding.getCity());
        buildingRating.setMapRegion(sspSyncBuilding.getDistrict());
        buildingRating.setMapAdCode(sspSyncBuilding.getGbCode());
        buildingRating.setMapNo(sspSyncBuilding.getMapNo());
        buildingRating.setBuildingDesc(BUILDING_DESC);

        buildingRatingService.save(buildingRating);
        Long id = buildingRating.getId();

        String ratingNo = updateBuildRating(id);
        buildingRating.setBuildingNo(ratingNo);
        String metaNo = MehtResultUtils.unbox(feignMehtRpc.getBuildMetaNo());

        buildingMetaService.sync(buildingMetaService.getByMapNo(mapNo), buildingRating, metaNo);

        return ratingNo;
    }

    private void setCityAndCountry(SspSyncBuilding sspSyncBuilding, BuildingRatingEntity buildingRating) {
        List<Integer> cityIds = Lists.newArrayList(sspSyncBuilding.getCityId(), sspSyncBuilding.getDistrictId());
        List<CodeNameVO> cityVOS = authorityRpcService.listWithGbCode(cityIds);
        Map<Integer, CodeNameVO> cityMap = cityVOS.stream()
                .collect(Collectors.toMap(CodeNameVO::getId, Function.identity()));
        CodeNameVO cityVO = cityMap.get(sspSyncBuilding.getCityId());
        buildingRating.setMapCity(cityVO.getName());
        String code = cityVO.getCode();
        if (StringUtils.isBlank(code)) {
            throw new BusinessException("当前城市没有找到区域编码:" + cityVO.getName());
        }
        buildingRating.setMapAdCode(code);
        buildingRating.setMapRegion(cityMap.get(sspSyncBuilding.getDistrictId()).getName());
    }

    private String updateBuildRating(Long id) {
        BuildingRatingEntity update = new BuildingRatingEntity();
        String ratingNo = getRatingNo(id);
        update.setId(id);
        update.setBuildingNo(ratingNo);
        buildingRatingService.updateById(update);
        return ratingNo;
    }

    private String getRatingNo(Long id) {
        return "BRR" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + String.format("%05d", id);
    }

    @Override
    public List<SspBuildingVO> queryByStatus(Integer status) {
        List<BuildingMetaEntity> buildingMetaEntities = buildingMetaService.queryByStatus(status);
        if (CollectionUtils.isNotEmpty(buildingMetaEntities)) {
            return buildingMetaConvert.toSspBuildingVOS(buildingMetaEntities);
        }
        return new ArrayList<>();
    }

    @Override
    public void updateBuildingNameAi(BuildingMetaImportParam param) {
        BuildingRatingListener listener = new BuildingRatingListener();
        // String path = "D:\\楼宇主数据.xlsx";
        try {
            URL url = new URL(param.getUrl());
            InputStream inputStream = url.openStream();
            EasyExcel.read(inputStream,
                            BuildingMetaImport.class, listener)
                    .sheet()
                    .doRead();
            List<BuildingMetaImport> list = listener.getList();
            list = list.stream()
                    .filter(e -> com.coocaa.meht.api.cheese.common.tools.utils.StringUtils.isNotBlank(e.getMapNo()))
                    .filter(e -> com.coocaa.meht.api.cheese.common.tools.utils.StringUtils.isNotBlank(e.getRatingLevel()))
                    .filter(e -> !e.getRatingLevel().equals("不合格"))
                    .filter(e -> !e.getBuildingName().equals("building_name"))
                    .collect(Collectors.toList());
            //mapNo可能有重复的
            Map<String, List<BuildingMetaImport>> map = list.stream()
                    .collect(Collectors.groupingBy(BuildingMetaImport::getMapNo));

            map.values().parallelStream().forEach(buildingMetaImports -> {

                BuildingMetaImport buildingMetaImport = buildingMetaImports.get(0);
                BuildingMetaEntity metaEntity = new BuildingMetaEntity();

                metaEntity.setMapNo(buildingMetaImport.getMapNo());

                metaEntity.setBuildingNameAi(buildingMetaImport.getBuildingName());

                buildingMetaService.updateByMapNo(metaEntity);

            });

        } catch (Exception e) {
            log.error("导入主数据报错:", e);
            throw new BusinessException("导入数据报错");
        }
    }

    @Override
    public void updateLatitude(List<Long> ids) {
        List<BuildingRatingEntity> buildingRatingList;
        if (CollectionUtils.isEmpty(ids)) {
            buildingRatingList = buildingRatingService.listByUserCode("CC0000");
        } else {
            buildingRatingList = buildingRatingService.listByIds(ids);
        }
        if (CollectionUtils.isNotEmpty(buildingRatingList)) {
            buildingRatingList.parallelStream().forEach(e -> {
                try {
                    BaiduApiService.ApiResponse r = baiduApiService.getDetailByBaidu(e.getMapNo());
                    BaiduApiService.Result result = r.getResult();
                    String province = result.getProvince();

                    BuildingRatingEntity update = new BuildingRatingEntity();
                    update.setMapProvince(province);
                    if (StringUtils.isBlank(e.getMapLongitude())) {
                        update.setMapLongitude(rsaExample.encryptByPublic(result.getLocation().getLng()));
                    }
                    if (StringUtils.isBlank(e.getMapLatitude())) {
                        update.setMapLatitude(rsaExample.encryptByPublic(result.getLocation().getLat()));
                    }

                    if (StringUtils.isBlank(e.getMapAddress())) {
                        update.setMapAddress(rsaExample.encryptByPublic(result.getAddress()));
                    }
                    if (StringUtils.isBlank(e.getMapRegion())) {
                        update.setMapRegion(result.getArea());
                    }
                    update.setId(e.getId());
                    buildingRatingService.updateById(update);
                    //更新buildingMeta
                    BuildingMetaEntity bme = buildingMetaService.getByMapNo(e.getMapNo());
                    BuildingMetaEntity buildingMetaEntity = buildingMetaConvert.toBuildingMetaEntity(update);
                    buildingMetaEntity.setId(bme.getId());
                    buildingMetaService.updateById(buildingMetaEntity);

                    //更新ssp的地址 经纬度
                    r.setBuildingRatingNo(e.getBuildingNo());
                    feignSspRpc.syncLatitudeByMeta(r);
                } catch (Exception ex) {
                    log.error("刷新经纬度报错:", ex);
                }

            });
        }
    }

    @Override
    public byte[] exportMeta(BuildingMetaQueryDTO dto) {
        PageRequestVo<BuildingMetaQueryDTO> pageRequestVo = new PageRequestVo<>();
        pageRequestVo.setPageSize(-1);
        pageRequestVo.setCurrentPage(1L);
        pageRequestVo.setQuery(dto);
        PageResponseVo<BuildingMetaListVO> responseVo = listBuildingMeta(pageRequestVo);
        List<BuildingMetaListVO> rows = responseVo.getRows();

        try {
            return EasyExcelUtils.createExcel("sheet1", rows, null, false);
        } catch (Exception e) {
            log.error("数据导出错误:", e);
            throw new BusinessException("数据导出错误");
        }
    }

    @Override
    public void refreshForbiddenIndustry(RefreshForbiddenIndustryParam param) {
        buildingMetaService.updateForbiddenIndustry(param.getForbiddenCodes(), param.getBuildingRatingNo());

    }

    @Override
    public void updateBaseMessage(UpdateBaseMessageParam param) {
        String buildingRatingNo = param.getBuildingRatingNo();

        BuildingMetaEntity buildingMetaEntity = new BuildingMetaEntity();
        buildingMetaEntity.setBuildingRatingNo(buildingRatingNo);
        if (StringUtils.isNotBlank(param.getCoordinate())) {
            String[] coordinates = param.getCoordinate().split(",");
            if (coordinates.length == 2) {
                String latitude = coordinates[1].trim();
                String longitude = coordinates[0].trim();
                buildingMetaEntity.setMapLatitude(rsaExample.encryptByPublic(latitude));
                buildingMetaEntity.setMapLongitude(rsaExample.encryptByPublic(longitude));
            }
        }


        if (StringUtils.isNotBlank(param.getDistrict())) {
            buildingMetaEntity.setMapRegion(param.getDistrict());
        }
        if (StringUtils.isNotBlank(param.getAddress())) {
            buildingMetaEntity.setMapAddress(rsaExample.encryptByPublic(param.getAddress()));
        }
        buildingMetaService.updateByBuildingRatingNo(buildingMetaEntity);
        BuildingRatingEntity buildingRating = buildingMetaConvert.toBuildingRatingEntity(buildingMetaEntity);
        buildingRatingService.updateByBuildingRatingNo(buildingRating);
    }

    @Override
    public void updateMetaMessage(List<Long> ids) {
        List<BuildingMetaEntity> buildingMetaList;
        if (CollectionUtils.isEmpty(ids)) {
            buildingMetaList = buildingMetaService.list();
        } else {
            buildingMetaList = buildingMetaService.listByIds(ids);
        }
        if (CollectionUtils.isNotEmpty(buildingMetaList)) {
            buildingMetaList.parallelStream().forEach(e -> {
                try {
                    BaiduApiService.ApiResponse r = baiduApiService.getDetailByBaidu(e.getMapNo());
                    BaiduApiService.Result result = r.getResult();
                    String province = result.getProvince();

                    BuildingMetaEntity update = new BuildingMetaEntity();
                    update.setMapProvince(province);
                    update.setMapCity(result.getCity());
                    //  update.setMapAdCode(result.get);
                    if (StringUtils.isBlank(e.getMapLongitude())) {
                        update.setMapLongitude(rsaExample.encryptByPublic(result.getLocation().getLng()));
                    }
                    if (StringUtils.isBlank(e.getMapLatitude())) {
                        update.setMapLatitude(rsaExample.encryptByPublic(result.getLocation().getLat()));
                    }

                    if (StringUtils.isBlank(e.getMapAddress())) {
                        update.setMapAddress(rsaExample.encryptByPublic(result.getAddress()));
                    }
                    if (StringUtils.isBlank(e.getMapRegion())) {
                        update.setMapRegion(result.getArea());
                    }
                    update.setId(e.getId());
                    buildingMetaService.updateById(update);
                } catch (Exception ex) {
                    log.error("刷新经纬度报错:", ex);
                }

            });
        }
    }

    @Override
    public void processAiMeta(Long startId) {
        List<BuildingMetaEntity> list = new ArrayList<>();
        if (ObjectUtil.isNull(startId)) {
            list = buildingMetaService.list();
        } else {
            list = buildingMetaService.lambdaQuery().gt(BuildingMetaEntity::getId, startId).list();
        }
        int size = list.size();
        AtomicInteger i = new AtomicInteger();
        list.forEach(e -> {
            Long id = e.getId();
            log.info("共{}条数据，正在处理第{}条数据,id={}", size, i.incrementAndGet(), id);
            try {
                // 计算评分
                TaskScoreDetailEntity taskScoreDetailEntity = new TaskScoreDetailEntity();
                taskScoreDetailEntity.setCityName(e.getMapCity());
                taskScoreDetailEntity.setBuildingName(e.getBuildingNameAi());
                taskScoreDetailEntity.setBuildingType(e.getBuildingTypeAi());

                tcTaskService.computeScore(taskScoreDetailEntity);

                BuildingMetaEntity entity = new BuildingMetaEntity();
                entity.setId(id);
                entity.setBuildingAiScore(taskScoreDetailEntity.getBuildingScore());
                entity.setProjectLevelAi(taskScoreDetailEntity.getBuildingLevel());
                buildingMetaService.updateById(entity);
            } catch (Exception ex) {
                log.error("处理ai评分报错实体:{}", JSON.toJSONString(e));
                log.error("处理ai评分报错:", ex);
            }
        });
    }


    /**
     * 填充禁忌行业
     *
     * @param entities 数据库实体
     * @param vos      vo集合
     */
    private void fillingForbiddenIndustry(List<BuildingMetaEntity> entities, List<BuildingMetaListVO> vos) {
        if (CollectionUtils.isEmpty(entities) || CollectionUtils.isEmpty(vos)) {
            return;
        }

        // 转换禁忌行业
        List<String> industryCodes = entities.stream()
                .map(BuildingMetaEntity::getForbiddenIndustry)
                .filter(StringUtils::isNotBlank)
                .flatMap(codes -> Arrays.stream(StringUtils.split(codes, ",")))
                .toList();
        if (CollectionUtils.isEmpty(industryCodes)) {
            return;
        }

        Map<String, String> industryMapping = codeNameHelper.getIndustryMapping(industryCodes);
        if (MapUtils.isEmpty(industryMapping)) {
            return;
        }


        // 翻译禁忌行业名称
        Map<Long, String> buildingIndustryNameMapping = Maps.newHashMapWithExpectedSize(entities.size());
        for (BuildingMetaEntity meta : entities) {
            if (StringUtils.isBlank(meta.getForbiddenIndustry())) {
                continue;
            }

            StringJoiner joinerJoiner = new StringJoiner(",");
            for (String code : StringUtils.split(meta.getForbiddenIndustry(), ",")) {
                String name = industryMapping.get(code);
                if (StringUtils.isNotBlank(name)) {
                    joinerJoiner.add(name);
                }
            }
            String industryNames = joinerJoiner.toString();
            if (StringUtils.isBlank(industryNames)) {
                continue;
            }
            buildingIndustryNameMapping.put(meta.getId(), industryNames);
        }
        if (MapUtils.isEmpty(buildingIndustryNameMapping)) {
            return;
        }

        // 将禁忌行业设置到VO中
        vos.forEach(vo -> vo.setForbiddenIndustryName(buildingIndustryNameMapping.getOrDefault(vo.getId(), StringUtils.EMPTY)));
    }

    @Override
    public void rent() {
        List<BuildingMetaEntity> list = buildingMetaService.listNoRent();
        if (CollectionUtils.isNotEmpty(list)) {
            log.info("需要跑租金的数据量:{}", list.size());
            list.parallelStream().forEach(e -> {
                String mapCity = e.getMapCity();
                if (StringUtils.isBlank(mapCity)) {
                    String mapNo = e.getMapNo();
                    if (StringUtils.isNotBlank(mapNo)) {
                        List<BuildingRatingEntity> buildingRatingEntities = buildingRatingService.listByMapNos(Lists.newArrayList(mapNo));
                        if (CollectionUtils.isNotEmpty(buildingRatingEntities)) {
                            mapCity = buildingRatingEntities.get(0).getMapCity();
                        }
                    }
                }
                if (StringUtils.isNotBlank(mapCity)) {
                    BuildingMetaEntity buildingMetaEntity = new BuildingMetaEntity();
                    buildingMetaEntity.setId(e.getId());
                    BigDecimal rent = getRent(e.getMapCity(), e.getBuildingNameAi(), 5);
                    if (rent.compareTo(new BigDecimal(BASE_RENT)) > 0 || rent.compareTo(BigDecimal.ZERO) == 0) {
                        log.info("租金数据有误:{}:{}:{}", e.getBuildingNameAi(), e.getMapNo(), rent);
                        String msg = String.format("城市:%s:楼盘:%s,租金:%s", mapCity, e.getBuildingNameAi(), rent);
                        authorityRpcService.sendDefaultChatMessage("租金数据出错", msg);
                        return;
                    }
                    buildingMetaEntity.setAverageRent(rent);
                    buildingMetaService.updateById(buildingMetaEntity);

                }

            });
        }
    }

    private BigDecimal getRent(String city, String projectName, int times) {
        BigDecimal rent = rentAiService.getRent(city, projectName);
        if ((rent.equals(BigDecimal.ZERO) || rent.compareTo(new BigDecimal(BASE_RENT)) > 0) && times > 0) {
            log.warn("查到的租金价格:{}:{}:rent:{}:times:{},", city, projectName, rent, times);
            times--;
            return getRent(city, projectName, times);
        }
        return rent;
    }


    @Override
    public void doubao(AiThirdBuildingParam param) {
        // 1. 查询楼宇数据
        List<BuildingRatingEntity> buildingMetaEntityList = queryBuildingData(param, "doubao");
        if (CollectionUtils.isEmpty(buildingMetaEntityList)) {
            log.info("无待处理的楼宇数据");
            return;
        }
        log.info("待处理楼宇数据量: {}", buildingMetaEntityList.size());

        // 2. 创建线程池
        // 获取CPU核心数
        int cpuCores = Runtime.getRuntime().availableProcessors();
        // 设置核心线程数为CPU核心数×2
        int corePoolSize = cpuCores * 2;
        log.info("核心线程数: {}", corePoolSize);
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                corePoolSize,
                20,
                60,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1000),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );

        // 3. 使用线程安全的集合记录处理结果
        Set<String> successBuildingNos = Collections.synchronizedSet(new HashSet<>());
        Set<String> failedBuildingNos = Collections.synchronizedSet(new HashSet<>());

        // 4. 使用CountDownLatch同步任务
        CountDownLatch latch = new CountDownLatch(buildingMetaEntityList.size());

        // 5. 提交任务到线程池
        for (BuildingRatingEntity buildingMetaEntity : buildingMetaEntityList) {
            executor.execute(() -> {
                String buildingNo = buildingMetaEntity.getBuildingNo();
                try {
                    log.debug("开始处理楼宇: {}", buildingNo);

                    // 5.1 调用AI服务获取数据
                    Map<String, Object> aiData = aiService.getBuildingAppraiser(
                            buildingMetaEntity.getMapCity() + buildingMetaEntity.getBuildingName()
                    );
                    if (aiData == null) {
                        failedBuildingNos.add(buildingNo);
                        // 跳出本次循环
                        return;
                    }
                    // 5.2 转换数据
                    DBThirdBuildingEntity dbThirdBuildingEntity = dbThirdBuildingService.lambdaQuery()
                            .eq(DBThirdBuildingEntity::getBuildingNo, buildingNo)
                            .last("limit 1")
                            .one();
                    if (dbThirdBuildingEntity == null) {
                        dbThirdBuildingEntity = new DBThirdBuildingEntity();
                        dbThirdBuildingEntity.setBuildingNo(buildingNo);
                        dbThirdBuildingEntity.setBuildingName(buildingMetaEntity.getBuildingName());
                    }
                    buildDouBao(aiData, dbThirdBuildingEntity);
                    dbThirdBuildingService.saveOrUpdate(dbThirdBuildingEntity);
                    successBuildingNos.add(buildingNo);
                    log.debug("保存成功: {}", buildingNo);
                } catch (Exception e) {
                    failedBuildingNos.add(buildingNo);
                    log.error("处理楼宇失败: {}，错误原因: {}", buildingNo, e.getMessage(), e);
                } finally {
                    latch.countDown();  // 任务计数器减1
                }
            });
        }

        // 6. 等待所有任务完成（超时时间30分钟）
        try {
            boolean allDone = latch.await(30, TimeUnit.MINUTES);
            if (!allDone) {
                log.warn("警告: 部分任务未在超时时间内完成，剩余任务数: {}", latch.getCount());
            }
        } catch (InterruptedException e) {
            log.error("任务等待被中断", e);
            Thread.currentThread().interrupt();
        } finally {
            // 7. 关闭线程池（优雅终止）
            executor.shutdown();
            try {
                if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        // 8. 打印最终统计结果
        log.info("===== 豆包任务执行结果 =====");
        log.info("总任务数: {}", buildingMetaEntityList.size());
        log.info("成功数: {}", successBuildingNos.size());
        log.info("失败数: {}", failedBuildingNos.size());
        log.info("可能丢失的数据: {}", failedBuildingNos);
    }

    /**
     * 查询楼宇数据
     */
    private List<BuildingRatingEntity> queryBuildingData(AiThirdBuildingParam param, String type) {
        List<BuildingRatingEntity> buildingRatingEntityList = new ArrayList<>();
        LambdaQueryWrapper<BuildingRatingEntity> queryWrapper = new LambdaQueryWrapper<BuildingRatingEntity>()
                .eq(BuildingRatingEntity::getDeleted, 0)
                .in(BuildingRatingEntity::getStatus, Arrays.asList(0, 1, 2));

        if (CollectionUtils.isNotEmpty(param.getBuildingNos())) {
            queryWrapper.in(BuildingRatingEntity::getBuildingNo, param.getBuildingNos());
        }

        if (param.getStartTime() != null && param.getEndTime() != null) {
            queryWrapper.between(BuildingRatingEntity::getCreateTime, param.getStartTime(), param.getEndTime());
        }
        List<BuildingRatingEntity> queryList = buildingRatingService.list(queryWrapper);
        if (CollectionUtils.isEmpty(queryList)) {
            return Collections.emptyList();
        }
        if (!param.isFilter()) {
            return queryList;
        }
        List<String> aiBuildingNos;
        if ("doubao".equals(type)) {
            aiBuildingNos = dbThirdBuildingService.lambdaQuery()
                    .select(DBThirdBuildingEntity::getBuildingNo)
                    .list()
                    .stream()
                    .map(DBThirdBuildingEntity::getBuildingNo)
                    .collect(Collectors.toList());
        } else if ("deepseek".equals(type)) {
            aiBuildingNos = deepSeekThirdBuildingService.lambdaQuery()
                    .select(DeepSeekThirdBuildingEntity::getBuildingNo)
                    .list()
                    .stream()
                    .map(DeepSeekThirdBuildingEntity::getBuildingNo)
                    .collect(Collectors.toList());
        } else if ("openAi".equals(type)) {
            aiBuildingNos = thirdBuildingOpenAiService.lambdaQuery()
                    .select(ThirdBuildingOpenAi::getBuildingNo)
                    .list()
                    .stream()
                    .map(ThirdBuildingOpenAi::getBuildingNo)
                    .collect(Collectors.toList());
        } else {
            aiBuildingNos = qwThirdBuildingService.lambdaQuery()
                    .select(QWThirdBuildingEntity::getBuildingNo)
                    .list()
                    .stream()
                    .map(QWThirdBuildingEntity::getBuildingNo)
                    .collect(Collectors.toList());
        }
        for (BuildingRatingEntity buildingRatingEntity : queryList) {
            if (!aiBuildingNos.contains(buildingRatingEntity.getBuildingNo())) {
                buildingRatingEntityList.add(buildingRatingEntity);
            }
        }
        return buildingRatingEntityList;
    }

    @Override
    public void deepseek(AiThirdBuildingParam param) {
        // 1. 查询楼宇数据
        List<BuildingRatingEntity> buildingMetaEntityList = queryBuildingData(param, "deepseek");

        if (CollectionUtils.isEmpty(buildingMetaEntityList)) {
            log.info("无待处理的楼宇数据");
            return;
        }
        log.info("待处理楼宇数据量: {}", buildingMetaEntityList.size());

        // 2. 创建线程池
        // 获取CPU核心数
        int cpuCores = Runtime.getRuntime().availableProcessors();
        // 设置核心线程数为CPU核心数×2
        int corePoolSize = cpuCores * 2;
        log.info("核心线程数: {}", corePoolSize);
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                corePoolSize,
                20,
                60,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(10000),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );

        // 3. 使用线程安全的集合记录处理结果
        Set<String> successBuildingNos = Collections.synchronizedSet(new HashSet<>());
        Set<String> failedBuildingNos = Collections.synchronizedSet(new HashSet<>());

        // 4. 使用CountDownLatch同步任务
        CountDownLatch latch = new CountDownLatch(buildingMetaEntityList.size());

        // 5. 提交任务到线程池
        for (BuildingRatingEntity buildingMetaEntity : buildingMetaEntityList) {
            executor.execute(() -> {
                String buildingNo = buildingMetaEntity.getBuildingNo();
                try {
                    log.debug("开始处理楼宇: {}", buildingNo);

                    // 5.1 调用AI服务获取数据
                    Map<String, Object> aiData = aiService.getDeepSeekBuildingAppraiser(
                            buildingMetaEntity.getMapCity() + buildingMetaEntity.getBuildingName()
                    );
                    if (aiData == null) {
                        failedBuildingNos.add(buildingNo);
                        // 跳出本次循环
                        return;
                    }
                    // 5.2 转换数据
                    DeepSeekThirdBuildingEntity deepSeekThirdBuildingEntity = deepSeekThirdBuildingService.lambdaQuery()
                            .eq(DeepSeekThirdBuildingEntity::getBuildingNo, buildingNo)
                            .last("limit 1")
                            .one();
                    if (deepSeekThirdBuildingEntity == null) {
                        deepSeekThirdBuildingEntity = new DeepSeekThirdBuildingEntity();
                        deepSeekThirdBuildingEntity.setBuildingNo(buildingNo);
                        deepSeekThirdBuildingEntity.setBuildingName(buildingMetaEntity.getBuildingName());
                    }
                    buildDeepSeek(aiData, deepSeekThirdBuildingEntity);
                    deepSeekThirdBuildingService.saveOrUpdate(deepSeekThirdBuildingEntity);
                    successBuildingNos.add(buildingNo);
                    log.debug("保存成功: {}", buildingNo);
                } catch (Exception e) {
                    failedBuildingNos.add(buildingNo);
                    log.error("处理楼宇失败: {}，错误原因: {}", buildingNo, e.getMessage(), e);
                } finally {
                    latch.countDown();
                }
            });
        }

        // 6. 等待所有任务完成（超时时间30分钟）
        try {
            boolean allDone = latch.await(30, TimeUnit.MINUTES);
            if (!allDone) {
                log.warn("警告: 部分任务未在超时时间内完成，剩余任务数: {}", latch.getCount());
            }
        } catch (InterruptedException e) {
            log.error("任务等待被中断", e);
            Thread.currentThread().interrupt();
        } finally {
            // 7. 关闭线程池（优雅终止）
            executor.shutdown();
            try {
                if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        // 8. 打印最终统计结果
        log.info("===== deepseek任务执行结果 =====");
        log.info("总任务数: {}", buildingMetaEntityList.size());
        log.info("成功数: {}", successBuildingNos.size());
        log.info("失败数: {}", failedBuildingNos.size());
        log.info("可能丢失的数据: {}", failedBuildingNos);
    }


    @Override
    public void tyqw(AiThirdBuildingParam param) {
        // 1. 查询楼宇数据
        List<BuildingRatingEntity> buildingMetaEntityList = queryBuildingData(param, "tyqw");
        if (CollectionUtils.isEmpty(buildingMetaEntityList)) {
            log.info("无待处理的楼宇数据");
            return;
        }
        log.info("待处理楼宇数据量: {}", buildingMetaEntityList.size());

        // 2. 创建线程池
        // 获取CPU核心数
        int cpuCores = Runtime.getRuntime().availableProcessors();
        // 设置核心线程数为CPU核心数×2
        int corePoolSize = cpuCores * 2;
        log.info("核心线程数: {}", corePoolSize);
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                corePoolSize,
                20,
                60,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(10000),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );

        // 3. 使用线程安全的集合记录处理结果
        Set<String> successBuildingNos = Collections.synchronizedSet(new HashSet<>());
        Set<String> failedBuildingNos = Collections.synchronizedSet(new HashSet<>());

        // 4. 使用CountDownLatch同步任务
        CountDownLatch latch = new CountDownLatch(buildingMetaEntityList.size());

        // 5. 提交任务到线程池
        for (BuildingRatingEntity buildingMetaEntity : buildingMetaEntityList) {
            executor.execute(() -> {
                String buildingNo = buildingMetaEntity.getBuildingNo();
                try {
                    log.debug("开始处理楼宇: {}", buildingNo);

                    // 5.1 调用AI服务获取数据
                    Map<String, Object> aiData = aiService.getQWBuildingAppraiser(
                            buildingMetaEntity.getMapCity() + buildingMetaEntity.getBuildingName()
                    );
                    if (aiData == null) {
                        failedBuildingNos.add(buildingNo);
                        // 跳出本次循环
                        return;
                    }
                    // 5.2 转换数据
                    QWThirdBuildingEntity qwThirdBuildingEntity = qwThirdBuildingService.lambdaQuery()
                            .eq(QWThirdBuildingEntity::getBuildingNo, buildingNo)
                            .last("limit 1")
                            .one();
                    if (qwThirdBuildingEntity == null) {
                        qwThirdBuildingEntity = new QWThirdBuildingEntity();
                        qwThirdBuildingEntity.setBuildingNo(buildingNo);
                        qwThirdBuildingEntity.setBuildingName(buildingMetaEntity.getBuildingName());
                    }
                    buildTYQW(aiData, qwThirdBuildingEntity);
                    qwThirdBuildingService.saveOrUpdate(qwThirdBuildingEntity);
                    successBuildingNos.add(buildingNo);
                    log.debug("保存成功: {}", buildingNo);
                } catch (Exception e) {
                    failedBuildingNos.add(buildingNo);
                    log.error("处理楼宇失败: {}，错误原因: {}", buildingNo, e.getMessage(), e);
                } finally {
                    latch.countDown();
                }
            });
        }

        // 6. 等待所有任务完成（超时时间30分钟）
        try {
            boolean allDone = latch.await(30, TimeUnit.MINUTES);
            if (!allDone) {
                log.warn("警告: 部分任务未在超时时间内完成，剩余任务数: {}", latch.getCount());
            }
        } catch (InterruptedException e) {
            log.error("任务等待被中断", e);
            Thread.currentThread().interrupt();
        } finally {
            // 7. 关闭线程池（优雅终止）
            executor.shutdown();
            try {
                if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        // 8. 打印最终统计结果
        log.info("===== 通义千问任务执行结果 =====");
        log.info("总任务数: {}", buildingMetaEntityList.size());
        log.info("成功数: {}", successBuildingNos.size());
        log.info("失败数: {}", failedBuildingNos.size());
        log.info("可能丢失的数据: {}", failedBuildingNos);
    }

    @Override
    public void openAi(AiThirdBuildingParam param) {
        // 1. 查询楼宇数据
        List<BuildingRatingEntity> buildingMetaEntityList = queryBuildingData(param, "openAi");
        if (CollectionUtils.isEmpty(buildingMetaEntityList)) {
            log.info("无待处理的楼宇数据");
            return;
        }
        log.info("待处理楼宇数据量: {}", buildingMetaEntityList.size());

        // 2. 创建线程池
        // 获取CPU核心数
        int cpuCores = Runtime.getRuntime().availableProcessors();
        // 设置核心线程数为CPU核心数×2
        int corePoolSize = cpuCores * 2;
        log.info("核心线程数: {}", corePoolSize);
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                corePoolSize,
                20,
                60,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(10000),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );

        // 3. 使用线程安全的集合记录处理结果
        Set<String> successBuildingNos = Collections.synchronizedSet(new HashSet<>());
        Set<String> failedBuildingNos = Collections.synchronizedSet(new HashSet<>());

        // 4. 使用CountDownLatch同步任务
        CountDownLatch latch = new CountDownLatch(buildingMetaEntityList.size());

        // 5. 提交任务到线程池
        for (BuildingRatingEntity buildingMetaEntity : buildingMetaEntityList) {
            executor.execute(() -> {
                String buildingNo = buildingMetaEntity.getBuildingNo();
                try {
                    log.debug("开始处理楼宇: {}", buildingNo);

                    // 5.1 调用AI服务获取数据
                    Map<String, Object> aiData = aiService.getOpenAIBuildingAppraiser(
                            buildingMetaEntity.getMapCity() + buildingMetaEntity.getBuildingName()
                    );
                    if (aiData == null || aiData.isEmpty()) {
                        failedBuildingNos.add(buildingNo);
                        // 跳出本次循环
                        return;
                    }
                    // 5.2 转换数据
                    ThirdBuildingOpenAi thirdBuildingOpenAi = thirdBuildingOpenAiService.lambdaQuery()
                            .eq(ThirdBuildingOpenAi::getBuildingNo, buildingNo)
                            .last("limit 1")
                            .one();
                    if (thirdBuildingOpenAi == null) {
                        thirdBuildingOpenAi = new ThirdBuildingOpenAi();
                        thirdBuildingOpenAi.setBuildingNo(buildingNo);
                        thirdBuildingOpenAi.setBuildingName(buildingMetaEntity.getBuildingName());
                    }
                    buildOpenAi(aiData, thirdBuildingOpenAi);
                    thirdBuildingOpenAiService.saveOrUpdate(thirdBuildingOpenAi);
                    successBuildingNos.add(buildingNo);
                    log.debug("保存成功: {}", buildingNo);
                } catch (Exception e) {
                    failedBuildingNos.add(buildingNo);
                    log.error("处理楼宇失败: {}，错误原因: {}", buildingNo, e.getMessage(), e);
                } finally {
                    latch.countDown();
                }
            });
        }

        // 6. 等待所有任务完成（超时时间30分钟）
        try {
            boolean allDone = latch.await(30, TimeUnit.MINUTES);
            if (!allDone) {
                log.warn("警告: 部分任务未在超时时间内完成，剩余任务数: {}", latch.getCount());
            }
        } catch (InterruptedException e) {
            log.error("任务等待被中断", e);
            Thread.currentThread().interrupt();
        } finally {
            // 7. 关闭线程池（优雅终止）
            executor.shutdown();
            try {
                if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        // 8. 打印最终统计结果
        log.info("===== openAI任务执行结果 =====");
        log.info("总任务数: {}", buildingMetaEntityList.size());
        log.info("成功数: {}", successBuildingNos.size());
        log.info("失败数: {}", failedBuildingNos.size());
        log.info("可能丢失的数据: {}", failedBuildingNos);

    }

    private void buildOpenAi(Map<String, Object> map, ThirdBuildingOpenAi entity) {
        // 通用处理方法
        BiConsumer<String, Consumer<String>> setField = (key, setter) -> {
            if (map.containsKey(key)) {
                String value = truncateString(safeCastToString(map.get(key)), 50);
                setter.accept(value);
            }
        };

        // 处理所有字段
        setField.accept("thirdBuildingPrice", entity::setThirdBuildingPrice);
        setField.accept("thirdBuildingGrade", entity::setThirdBuildingGrade);
        setField.accept("thirdBuildingLocation", entity::setThirdBuildingLocation);
        setField.accept("thirdBuildingAge", entity::setThirdBuildingAge);
        setField.accept("thirdBuildingExterior", entity::setThirdBuildingExterior);
        setField.accept("thirdBuildingRate", entity::setThirdBuildingRate);
        setField.accept("thirdBuildingNumber", entity::setThirdBuildingNumber);
        setField.accept("thirdBuildingGarage", entity::setThirdBuildingGarage);
        setField.accept("thirdBuildingBrand", entity::setThirdBuildingBrand);
        setField.accept("thirdBuildingLobby", entity::setThirdBuildingLobby);
        setField.accept("thirdBuildingType", entity::setThirdBuildingType);
    }

    private void buildTYQW(Map<String, Object> map, QWThirdBuildingEntity entity) {
        // 通用处理方法
        BiConsumer<String, Consumer<String>> setField = (key, setter) -> {
            if (map.containsKey(key)) {
                String value = truncateString(safeCastToString(map.get(key)), 50);
                setter.accept(value);
            }
        };

        // 处理所有字段
        setField.accept("thirdBuildingPrice", entity::setThirdBuildingPrice);
        setField.accept("thirdBuildingGrade", entity::setThirdBuildingGrade);
        setField.accept("thirdBuildingLocation", entity::setThirdBuildingLocation);
        setField.accept("thirdBuildingAge", entity::setThirdBuildingAge);
        setField.accept("thirdBuildingExterior", entity::setThirdBuildingExterior);
        setField.accept("thirdBuildingRate", entity::setThirdBuildingRate);
        setField.accept("thirdBuildingNumber", entity::setThirdBuildingNumber);
        setField.accept("thirdBuildingGarage", entity::setThirdBuildingGarage);
        setField.accept("thirdBuildingBrand", entity::setThirdBuildingBrand);
        setField.accept("thirdBuildingLobby", entity::setThirdBuildingLobby);
        setField.accept("thirdBuildingType", entity::setThirdBuildingType);
    }


    private void buildDouBao(Map<String, Object> map, DBThirdBuildingEntity entity) {
        // 通用处理方法
        BiConsumer<String, Consumer<String>> setField = (key, setter) -> {
            if (map.containsKey(key)) {
                String value = truncateString(safeCastToString(map.get(key)), 50);
                setter.accept(value);
            }
        };

        // 处理所有字段
        setField.accept("thirdBuildingPrice", entity::setThirdBuildingPrice);
        setField.accept("thirdBuildingGrade", entity::setThirdBuildingGrade);
        setField.accept("thirdBuildingLocation", entity::setThirdBuildingLocation);
        setField.accept("thirdBuildingAge", entity::setThirdBuildingAge);
        setField.accept("thirdBuildingExterior", entity::setThirdBuildingExterior);
        setField.accept("thirdBuildingRate", entity::setThirdBuildingRate);
        setField.accept("thirdBuildingNumber", entity::setThirdBuildingNumber);
        setField.accept("thirdBuildingGarage", entity::setThirdBuildingGarage);
        setField.accept("thirdBuildingBrand", entity::setThirdBuildingBrand);
        setField.accept("thirdBuildingLobby", entity::setThirdBuildingLobby);
        setField.accept("thirdBuildingType", entity::setThirdBuildingType);
    }

    /**
     * 字符串截断方法
     *
     * @param input     原始字符串
     * @param maxLength 最大长度
     * @return 截断后的字符串
     */
    private String truncateString(String input, int maxLength) {
        if (input == null) {
            return null;
        }
        return input.length() > maxLength ? input.substring(0, maxLength) : input;
    }


    private void buildDeepSeek(Map<String, Object> map, DeepSeekThirdBuildingEntity entity) {
        // 通用处理方法
        BiConsumer<String, Consumer<String>> setField = (key, setter) -> {
            if (map.containsKey(key)) {
                String value = truncateString(safeCastToString(map.get(key)), 50);
                setter.accept(value);
            }
        };

        // 处理所有字段
        setField.accept("thirdBuildingPrice", entity::setThirdBuildingPrice);
        setField.accept("thirdBuildingGrade", entity::setThirdBuildingGrade);
        setField.accept("thirdBuildingLocation", entity::setThirdBuildingLocation);
        setField.accept("thirdBuildingAge", entity::setThirdBuildingAge);
        setField.accept("thirdBuildingExterior", entity::setThirdBuildingExterior);
        setField.accept("thirdBuildingRate", entity::setThirdBuildingRate);
        setField.accept("thirdBuildingNumber", entity::setThirdBuildingNumber);
        setField.accept("thirdBuildingGarage", entity::setThirdBuildingGarage);
        setField.accept("thirdBuildingBrand", entity::setThirdBuildingBrand);
        setField.accept("thirdBuildingLobby", entity::setThirdBuildingLobby);
        setField.accept("thirdBuildingType", entity::setThirdBuildingType);
    }


    private static String safeCastToString(Object obj) {
        return obj != null ? obj.toString() : null;
    }

}