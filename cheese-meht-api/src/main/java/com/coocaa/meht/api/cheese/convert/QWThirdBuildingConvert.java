package com.coocaa.meht.api.cheese.convert;

import com.coocaa.meht.api.cheese.common.db.dto.ThirdBuildingDTO;
import com.coocaa.meht.api.cheese.common.db.entity.QWThirdBuildingEntity;
import org.mapstruct.Mapper;
import org.mapstruct.control.DeepClone;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2025/5/27
 */
@Mapper(componentModel = "spring", mappingControl = DeepClone.class)
public interface QWThirdBuildingConvert extends PageableConvert<QWThirdBuildingEntity, ThirdBuildingDTO>{
    QWThirdBuildingConvert INSTANCE = Mappers.getMapper(QWThirdBuildingConvert.class);

    QWThirdBuildingEntity toEntity(ThirdBuildingDTO ThirdBuildingDTO);
}
