package com.coocaa.meht.api.cheese.service.tctask;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.meht.api.cheese.listener.TaskScoreFileListener;
import com.coocaa.meht.api.cheese.bean.tctask.CreateTCTaskParams;
import com.coocaa.meht.api.cheese.bean.tctask.ExcelTemplateDTO;
import com.coocaa.meht.api.cheese.bean.tctask.ExecuteTaskParams;
import com.coocaa.meht.api.cheese.bean.tctask.TCTaskQueryParams;
import com.coocaa.meht.api.cheese.common.db.entity.BuildingParameterEntity;
import com.coocaa.meht.api.cheese.common.db.entity.CityCoefficientEntity;
import com.coocaa.meht.api.cheese.common.db.entity.CityRentEntity;
import com.coocaa.meht.api.cheese.common.db.entity.SysConfigEntity;
import com.coocaa.meht.api.cheese.common.db.entity.TaskAttachmentEntity;
import com.coocaa.meht.api.cheese.common.db.entity.TaskMainEntity;
import com.coocaa.meht.api.cheese.common.db.entity.TaskScoreDetailEntity;
import com.coocaa.meht.api.cheese.common.db.service.ITaskMainService;
import com.coocaa.meht.api.cheese.common.db.service.ITaskScoreDetailService;
import com.coocaa.meht.api.cheese.common.tools.cos.ObjectUtils;
import com.coocaa.meht.api.cheese.common.tools.easyexcel.EasyExcelUtils;
import com.coocaa.meht.api.cheese.common.tools.enums.BuildingTypeEnum;
import com.coocaa.meht.api.cheese.common.tools.result.PageRequestVo;
import com.coocaa.meht.api.cheese.common.tools.result.PageResponseVo;
import com.coocaa.meht.api.cheese.common.tools.result.ResultTemplate;
import com.coocaa.meht.api.cheese.common.tools.utils.UserIdUtils;
import com.coocaa.meht.api.cheese.exception.BusinessException;
import com.coocaa.meht.api.cheese.rpc.FeignAuthorityRpc;
import com.coocaa.meht.api.cheese.rpc.FeignMehtRpc;
import com.coocaa.meht.api.cheese.common.db.service.IBuildingParameterService;
import com.coocaa.meht.api.cheese.common.db.service.ICityCoefficientService;
import com.coocaa.meht.api.cheese.common.db.service.ICityRentService;
import com.coocaa.meht.api.cheese.common.db.service.ISysConfigService;
import com.coocaa.meht.api.cheese.common.db.service.ITaskAttachmentService;
import com.coocaa.meht.api.cheese.vo.UserVO;
import com.coocaa.meht.api.cheese.vo.tctask.TCTaskVO;
import com.coocaa.meht.api.cheese.vo.tctask.TaskScoreExportVO;
import com.qcloud.cos.exception.CosClientException;
import com.qcloud.cos.transfer.Download;

import jakarta.annotation.PostConstruct;

import com.coocaa.meht.api.cheese.convert.TCTaskConvert;
import com.coocaa.meht.api.cheese.enums.tctask.TCTaskStatus;
import com.coocaa.meht.api.cheese.utils.HttpUtils;
import com.coocaa.meht.api.cheese.utils.WordUtils;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.function.BiPredicate;
import java.util.function.Function;

/**
 * 试算任务相关服务
 */
@Service
@Slf4j
public class TCTaskService {

    @Value("${building.score.params.expire.time:10}")
    private Long buildingScoreParamsExpireTime;

    @Value("${mapak:default}")
    private String baiduMapAk;

    @Autowired
    private ITaskMainService taskMainService;

    @Autowired
    private ITaskAttachmentService taskAttachmentService;

    @Autowired
    private FeignAuthorityRpc feignAuthorityRpc;

    @Autowired
    private ITaskScoreDetailService taskScoreDetailService;

    @Autowired
    private FeignMehtRpc feignMehtRpc;

    @Autowired
    private TCTaskConvert tcTaskConvert;

    @Autowired
    private Executor taskExecutor;

    @Autowired
    private AiService aiService;

    @Autowired
    private IBuildingParameterService buildingParameterService;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private ICityRentService cityRentService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private ICityCoefficientService cityCoefficientService;

    @Value("${tctask.template.url}")
    private String templateUrl;

    /**
     * 获取试算任务列表
     *
     * @param queryParams 查询参数
     * @return 试算任务列表
     */
    public PageResponseVo<TCTaskVO> getTCTaskList(PageRequestVo<TCTaskQueryParams> queryParams) {
        // 构建查询条件
        LambdaQueryWrapper<TaskMainEntity> wrapper = Wrappers.lambdaQuery();

        // 任务编号查询条件
        if (StringUtils.isNotBlank(queryParams.getQuery().getTaskNo())) {
            wrapper.like(TaskMainEntity::getTaskNo, queryParams.getQuery().getTaskNo());
        }

        // 试算任务名称条件
        if (StringUtils.isNotBlank(queryParams.getQuery().getTaskName())) {
            wrapper.like(TaskMainEntity::getTaskName, queryParams.getQuery().getTaskName());
        }

        // 创建人工号查询条件
        if (StringUtils.isNotBlank(queryParams.getQuery().getCreateByNo())) {
            wrapper.eq(TaskMainEntity::getCreateByNo, queryParams.getQuery().getCreateByNo());
        }

        // 按创建时间倒序排序
        wrapper.orderByDesc(TaskMainEntity::getCreateTime);

        // 分页查询
        Page<TaskMainEntity> page = new Page<>(queryParams.getCurrentPage(), queryParams.getPageSize());
        Page<TaskMainEntity> pagedTasks = taskMainService.page(page, wrapper);

        PageResponseVo<TCTaskVO> pageResponse = tcTaskConvert.toPageResponse(pagedTasks);

        // 收集所有的createBy
        List<Integer> createByList = pageResponse.getRows().stream().map(item -> item.getCreateBy()).collect(Collectors.toList());
        Map<Integer, UserVO> userInfoMap = getUserInfoMap(createByList);

        // 转换状态名称
        pageResponse.getRows().forEach(item -> {
            item.setTaskStatusName(TCTaskStatus.getByCode(item.getTaskStatus()) != null ? TCTaskStatus.getByCode(item.getTaskStatus()).getDesc() : "");
            item.setCreateByName(userInfoMap.get(item.getCreateBy()) != null ? userInfoMap.get(item.getCreateBy()).getName() : "");
            item.setCreateByNo(userInfoMap.get(item.getCreateBy()) != null ? userInfoMap.get(item.getCreateBy()).getWno() : "");
        });

        return pageResponse;
    }

    /**
     * 创建试算任务
     *
     * @param request 创建任务请求参数
     * @return 任务编号
     */
    @Transactional(rollbackFor = Exception.class)
    public String createTCTask(CreateTCTaskParams request) {
        Map<Integer, UserVO> userInfoMap = getUserInfoMap(Arrays.asList(UserIdUtils.getUserId()));
        // 1. 创建任务主表记录
        TaskMainEntity taskMain = new TaskMainEntity();
        taskMain.setTaskName(request.getTaskName());
        taskMain.setTaskStatus(1); // 1:待执行
        taskMain.setTaskNo(generateTaskNo());
        taskMain.setCreateByNo(userInfoMap.get(UserIdUtils.getUserId()).getWno());
        taskMain.setCreateBy(UserIdUtils.getUserId());
        taskMain.setCreateTime(LocalDateTime.now());
        taskMain.setUpdateBy(UserIdUtils.getUserId());

        taskMainService.save(taskMain);

        // 2. 保存附件信息
        TaskAttachmentEntity attachment = new TaskAttachmentEntity();
        attachment.setTaskId(taskMain.getId());
        attachment.setCosUrl(request.getCosUrl());
        attachment.setCreateTime(LocalDateTime.now());

        taskAttachmentService.save(attachment);

        try {
            // 3. 解析Excel文件
            List<TaskScoreDetailEntity> detailList = parseExcel(request.getCosUrl());

            if (detailList.isEmpty()) {
                throw new BusinessException("Excel数据不能为空");
            }

            if (detailList.size() > 10000) {
                throw new BusinessException("Excel数据不能超过10000条");
            }

            // 4. 保存明细数据
            detailList.forEach(detail -> {
                if (detail.getBuildingType() == null) {
                    throw new BusinessException("楼宇类型编码只能为数字且不能为空");
                }
                // 判断楼宇类型编码是否为1，2，3，4
                if (detail.getBuildingType() != 1 && detail.getBuildingType() != 2 && detail.getBuildingType() != 3 && detail.getBuildingType() != 4) {
                    throw new BusinessException("楼宇类型编码只能为1写字楼，2商住楼，3综合体，4产业园区");
                }
                detail.setBuildingType(detail.getBuildingType()-1);
                detail.setTaskId(taskMain.getId());
                detail.setCreateTime(LocalDateTime.now());
            });

            taskScoreDetailService.saveBatch(detailList);

            return taskMain.getTaskNo();
        } catch (Exception e) {
            throw new BusinessException("处理Excel文件失败: " + e.getMessage());
        }
    }

    private Map<Integer, UserVO> getUserInfoMap(List<Integer> userIds) {

        ResultTemplate<List<UserVO>> re = feignAuthorityRpc.listUserDetailByIds(userIds);
        if (re.getSuccess()) {
            return re.getData().stream().collect(Collectors.toMap(UserVO::getId, Function.identity()));
        }
        return new HashMap<>();
    }

    /**
     * 生成任务编号
     * 格式: TC + 6位序号，例如: TC000001
     */
    private String generateTaskNo() {
        // 获取当前最大的任务编号
        LambdaQueryWrapper<TaskMainEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.orderByDesc(TaskMainEntity::getTaskNo);
        wrapper.last("LIMIT 1");

        TaskMainEntity lastTask = taskMainService.getOne(wrapper);

        int nextNumber = 1;
        if (lastTask != null && lastTask.getTaskNo() != null) {
            String currentNo = lastTask.getTaskNo().substring(2); // 去掉TC前缀
            nextNumber = Integer.parseInt(currentNo) + 1;
        }

        return String.format("TC%06d", nextNumber);
    }

    private List<TaskScoreDetailEntity> parseExcel(String cosUrl) {
        try {
            File tempFile = File.createTempFile("temp" + System.currentTimeMillis(), ".xlsx");
            Download download = ObjectUtils.downloadFile(cosUrl, tempFile);
            download.waitForCompletion();
            // 使用EASYEXCEL解析Excel文件
            TaskScoreFileListener listener = new TaskScoreFileListener();
            EasyExcel.read(tempFile, ExcelTemplateDTO.class, listener).sheet().doReadSync();
            List<ExcelTemplateDTO> dataList = listener.getDtoList();
            if (null == dataList || dataList.size() == 0) {
                throw new BusinessException("Excel数据不能为空");
            }
            List<TaskScoreDetailEntity> detailList = dataList.stream().map(item -> {
                TaskScoreDetailEntity detail = new TaskScoreDetailEntity();
                BeanUtils.copyProperties(item, detail);
                return detail;
            }).collect(Collectors.toList());
            return detailList;
        } catch (IOException | CosClientException | InterruptedException e) {
            throw new BusinessException("解析EXCEL文件失败");
        }
    }

    /**
     * 执行试算任务
     *
     * @param params 执行任务参数
     */
    public void executeTask(ExecuteTaskParams params) {
        // 1. 查询任务信息
        LambdaQueryWrapper<TaskMainEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(TaskMainEntity::getTaskNo, params.getTaskNo());
        TaskMainEntity taskMain = taskMainService.getOne(wrapper);

        if (taskMain == null) {
            throw new BusinessException("任务不存在");
        }

        // 2. 校验任务状态
        if (taskMain.getTaskStatus() != 1 && taskMain.getTaskStatus() != 4) {
            throw new BusinessException("只有待执行状态的任务才能执行");
        }

        // 3. 使用CompletableFuture异步执行
        CompletableFuture.runAsync(() -> {
            try {
                // 更新任务状态为测算中
                taskMain.setTaskStatus(TCTaskStatus.CALCULATING.getCode());
                taskMain.setUpdateTime(LocalDateTime.now());
                taskMain.setUpdateBy(UserIdUtils.getUserId());
                taskMainService.updateById(taskMain);

                // 执行计算任务
                executeTaskAsync(taskMain.getId());
            } catch (Exception e) {
                log.error("更新任务状态失败: taskId={}, error={}", taskMain.getId(), e.getMessage(), e);
                // 发生异常时更新任务状态为失败
                taskMain.setTaskStatus(TCTaskStatus.FAILED.getCode());
                taskMain.setUpdateTime(LocalDateTime.now());
                taskMainService.updateById(taskMain);
            }
        }, taskExecutor);
    }

    @Async("taskExecutor")
    public void executeTaskAsync(Integer taskId) {
        TaskMainEntity taskMain = taskMainService.getById(taskId);
        try {
            // 查询任务明细数据
            List<TaskScoreDetailEntity> detailList = taskScoreDetailService.list(
                    Wrappers.lambdaQuery(TaskScoreDetailEntity.class)
                            .eq(TaskScoreDetailEntity::getTaskId, taskId));

            if (detailList.isEmpty()) {
                throw new BusinessException("任务明细数据为空");
            }

            // 计算各项得分
            for (TaskScoreDetailEntity detail : detailList) {
                if (detail.getIsDone()) {
                    continue;
                }
                computeScore(detail);
                taskScoreDetailService.updateById(detail);
            }

            // 更新任务状态为已完成
            taskMain.setTaskStatus(TCTaskStatus.COMPLETED.getCode());
            taskMain.setUpdateTime(LocalDateTime.now());
            taskMainService.updateById(taskMain);

        } catch (Exception e) {
            log.error("执行试算任务失败: taskId={}, error={}", taskId, e.getMessage(), e);
            // 发生异常时更新任务状态为失败
            taskMain.setTaskStatus(TCTaskStatus.FAILED.getCode());
            taskMain.setUpdateTime(LocalDateTime.now());
            taskMainService.updateById(taskMain);
        }
    }

    /**
     * 检查楼宇是否存在
     * @param detail
     * @return
     */
    private boolean checkBuildingExistInBaiduMap(TaskScoreDetailEntity detail) {
        String address = detail.getCityName() + detail.getBuildingName();
        String url = String.format("https://api.map.baidu.com/geocoding/v3/?address=%s&output=json&ak=%s", address.replaceAll("\\s+", ""), baiduMapAk);
        String response = HttpUtils.get(url);
        if (StringUtils.isNotBlank(response)) {
            JSONObject jsonObject = JSON.parseObject(response);
            if (jsonObject.getInteger("status") == 0) {
                JSONObject result = jsonObject.getJSONObject("result");
                if (result.getInteger("confidence") >= 25) {
                    return true;
                }
            }
        }
        return false;
    }

    public void computeScore(TaskScoreDetailEntity detail) {
        // 先从百度地图API检查楼宇是否存在
        if (!checkBuildingExistInBaiduMap(detail)) {
            detail.setIsDone(true);
            return;
        }
        Map<String, Object> aiData = aiService.getBuildingAppraiser(detail.getCityName() + detail.getBuildingName());
        if (aiData != null) {
            buildTaskScoreDetail(detail, aiData);
            // 处理AI数据
            TaskScoreDetailEntity scoreDetails = processAIData(detail, detail.getBuildingType(), detail.getCityName());
            // 根据楼宇类型计算分数， 楼宇类型 0 写字楼 1 商住楼 2 综合体 3 产业园区
            switch (scoreDetails.getBuildingType()) {
                case 0:
                    // 根据写字楼规则进行评分
                    computeOfficeScore(scoreDetails);
                    break;
                case 1:
                    // 根据商住楼规则进行评分
                    computeCommercialScore(scoreDetails);
                    break;
                case 2:
                    // 根据综合体规则进行评分
                    computeComprehensiveScore(scoreDetails);
                    break;
                case 3:
                    // 根据产业园区规则进行评分
                    computeIndustrialScore(scoreDetails);
                    break;
                default:
                    break;
            }
            detail.setIsDone(true);
        } else {
//            throw new BusinessException("获取AI评分失败");
            log.error("获取AI评分失败");
        }
    }

    public void buildTaskScoreDetail(TaskScoreDetailEntity detail, Map<String, Object> data) {
        // 写字楼等级
        detail.setOfficeLevel(data.getOrDefault("thirdBuildingGrade", "").toString());
        // 地理等级
        detail.setLocationLevel(data.getOrDefault("thirdBuildingLocation", "").toString());
        // 层数
        String floorCount = data.get("thirdBuildingNumber").toString();
        if (StringUtils.isNotBlank(floorCount) && floorCount.matches("-?\\d+(\\.\\d+)?")) {
            detail.setFloorCount(Integer.parseInt(floorCount));
        } else {
            detail.setFloorCount(0);
        }
        // 楼龄
        String buildingAge = data.get("thirdBuildingAge").toString();
        if (StringUtils.isNotBlank(buildingAge) && buildingAge.matches("-?\\d+(\\.\\d+)?")) {
            detail.setBuildingAge(Integer.parseInt(buildingAge));
        } else {
            detail.setBuildingAge(0);
        }
        // 外墙
        detail.setFacadeMaterial(data.getOrDefault("thirdBuildingExterior", "").toString());
        // 大堂
        detail.setLobbyLevel(data.getOrDefault("thirdBuildingLobby", "").toString());
        // 地下车库
        detail.setParkingLevel(data.getOrDefault("thirdBuildingGarage", "").toString());
        // 大众点评评分
        detail.setDazhongScore(data.getOrDefault("hirdBuildingRate", 0).toString());
        // 综合体品牌
        detail.setComprehensiveBrand(data.getOrDefault("hirdBuildingBrand", "").toString());
        // 租金
        String monthRent = data.getOrDefault("thirdBuildingPrice", "0").toString().replace("元/平米", "");
        if (StringUtils.isNotBlank(monthRent)) {
            monthRent = monthRent.trim();
            log.info("租金信息：{}判断结果：{}", monthRent, monthRent.matches("-?\\d+(\\.\\d+)?"));
            if (monthRent.matches("-?\\d+(\\.\\d+)?")) {
                detail.setMonthRent(BigDecimal.valueOf(Double.parseDouble(monthRent)));
            } else {
                detail.setMonthRent(BigDecimal.ZERO);
            }
        } else {
            detail.setMonthRent(BigDecimal.ZERO);
        }
    }

    // 写字楼评分
    private void computeOfficeScore(TaskScoreDetailEntity details) {
        details.setComprehensiveBrandScore(BigDecimal.ZERO);
        details.setReviewScore(BigDecimal.ZERO);
        List<BigDecimal> scoreList = new ArrayList<>();
        scoreList.add(details.getOfficeScore());
        scoreList.add(details.getLocationScore());
        scoreList.add(details.getFloorScore());
        scoreList.add(details.getRentScore());
        scoreList.add(details.getAgeScore());
        scoreList.add(details.getFacadeScore());
        scoreList.add(details.getLobbyScore());
        scoreList.add(details.getParkingScore());
        BigDecimal totalScore = BigDecimal.ZERO;
        for (BigDecimal score : scoreList) {
            if (score==null){
                continue;
            }
            totalScore = totalScore.add(score);
        }
        CityCoefficientEntity cityCoefficientEntity = cityCoefficientService.getFirstNotDeletedByCity(details.getCityName());
        if (Objects.nonNull(cityCoefficientEntity)) {
            totalScore = totalScore.multiply(cityCoefficientEntity.getCoefficient());
        } else {
            totalScore = totalScore.multiply(BigDecimal.ONE);
        }
        details.setBuildingLevel(calculateAIProjectLevel(totalScore));
        details.setBuildingScore(totalScore);
    }

    // 商住楼评分
    private void computeCommercialScore(TaskScoreDetailEntity details) {
        details.setComprehensiveBrandScore(BigDecimal.ZERO);
        details.setReviewScore(BigDecimal.ZERO);
        details.setOfficeScore(BigDecimal.ZERO);
        List<BigDecimal> scoreList = new ArrayList<>();
        scoreList.add(details.getLocationScore());
        scoreList.add(details.getFloorScore());
        scoreList.add(details.getRentScore());
        scoreList.add(details.getAgeScore());
        scoreList.add(details.getFacadeScore());
        scoreList.add(details.getLobbyScore());
        scoreList.add(details.getParkingScore());
        scoreList.add(details.getComprehensiveBrandScore());
        scoreList.add(details.getReviewScore());
        BigDecimal totalScore = BigDecimal.ZERO;
        for (BigDecimal score : scoreList) {
            if (score==null){
                continue;
            }
            totalScore = totalScore.add(score);
        }
        CityCoefficientEntity cityCoefficientEntity = cityCoefficientService.getFirstNotDeletedByCity(details.getCityName());
        if (Objects.nonNull(cityCoefficientEntity)) {
            totalScore.multiply(cityCoefficientEntity.getCoefficient());
        }
        totalScore.multiply(BigDecimal.ONE);
        details.setBuildingLevel(calculateAIProjectLevel(totalScore));
        details.setBuildingScore(totalScore);
    }

    // 综合体评分
    private void computeComprehensiveScore(TaskScoreDetailEntity details) {
        details.setOfficeScore(BigDecimal.ZERO);
        details.setFloorScore(BigDecimal.ZERO);
        details.setRentScore(BigDecimal.ZERO);
        details.setAgeScore(BigDecimal.ZERO);
        details.setFacadeScore(BigDecimal.ZERO);
        details.setLobbyScore(BigDecimal.ZERO);
        details.setParkingScore(BigDecimal.ZERO);
        List<BigDecimal> scoreList = new ArrayList<>();
        scoreList.add(details.getLocationScore());
        scoreList.add(details.getComprehensiveBrandScore());
        scoreList.add(details.getReviewScore());
        BigDecimal totalScore = BigDecimal.ZERO;
        for (BigDecimal score : scoreList) {
            if (score==null){
                continue;
            }
            totalScore = totalScore.add(score);
        }
        CityCoefficientEntity cityCoefficientEntity = cityCoefficientService.getFirstNotDeletedByCity(details.getCityName());
        if (Objects.nonNull(cityCoefficientEntity)) {
            totalScore.multiply(cityCoefficientEntity.getCoefficient());
        }
        totalScore.multiply(BigDecimal.ONE);
        details.setBuildingLevel(calculateAIProjectLevel(totalScore));
        details.setBuildingScore(totalScore);
    }

    // 产业园区评分
    private void computeIndustrialScore(TaskScoreDetailEntity details) {
        details.setOfficeScore(BigDecimal.ZERO);
        details.setRentScore(BigDecimal.ZERO);
        details.setParkingScore(BigDecimal.ZERO);
        details.setComprehensiveBrandScore(BigDecimal.ZERO);
        details.setReviewScore(BigDecimal.ZERO);
        List<BigDecimal> scoreList = new ArrayList<>();
        scoreList.add(details.getLocationScore());
        scoreList.add(details.getFloorScore());
        scoreList.add(details.getAgeScore());
        scoreList.add(details.getFacadeScore());
        scoreList.add(details.getLobbyScore());
        BigDecimal totalScore = BigDecimal.ZERO;
        for (BigDecimal score : scoreList) {
            if (score==null){
                log.warn("score is null");
                continue;
            }
            totalScore = totalScore.add(score);
        }
        CityCoefficientEntity cityCoefficientEntity = cityCoefficientService.getFirstNotDeletedByCity(details.getCityName());
        if (Objects.nonNull(cityCoefficientEntity)) {
            totalScore.multiply(cityCoefficientEntity.getCoefficient());
        }
        totalScore.multiply(BigDecimal.ONE);
        details.setBuildingLevel(calculateAIProjectLevel(totalScore));
        details.setBuildingScore(totalScore);
    }

    private String calculateAIProjectLevel(BigDecimal score) {
        if (score.compareTo(new BigDecimal(8.5)) >= 0) {
            return "AAA";
        } else if (score.compareTo(new BigDecimal(7)) >= 0)
            return "AA";
        else if (score.compareTo(new BigDecimal(6)) >= 0) {
            return "A";
        } else {
            return "未达到A级";
        }
    }

    private void processNumberMatch(TaskScoreDetailEntity details, String code,
        Map<String, List<BuildingParameterEntity>> ruleMap) {

        String value = details.getValue(code);
        if (StringUtils.isBlank(value)) {
            return;
        }

        List<BuildingParameterEntity> rules = ruleMap.get(code);
        if (CollectionUtils.isEmpty(rules)) {
            return;
        }

        try {
            double number = Double.parseDouble(value.replaceAll("[^0-9]", ""));
            rules.stream()
                .filter(rule -> matchNumberRule(rule.getParameterRule(), number))
                .findFirst()
                .ifPresent(rule -> details.setValueId(code + "Score", rule.getParameterScore().multiply(rule.getWeightValue().divide(new BigDecimal(100)))));
        } catch (NumberFormatException e) {
            log.warn("数值格式转换异常: {}", value);
        }
    }

    private boolean matchNumberRule(String rule, double value) {
        String[] rules = rule.split(",");
        for (String r : rules) {
            r = r.trim();
            if (!evaluateCondition(r, value)) {
                return false;
            }
        }
        return true;
    }

    private boolean evaluateCondition(String condition, double value) {
        if (condition.startsWith(">=")) {
            return value >= Double.parseDouble(condition.substring(2));
        } else if (condition.startsWith("<=")) {
            return value <= Double.parseDouble(condition.substring(2));
        } else if (condition.startsWith(">")) {
            return value > Double.parseDouble(condition.substring(1));
        } else if (condition.startsWith("<")) {
            return value < Double.parseDouble(condition.substring(1));
        } else if (condition.startsWith("=")) {
            return value == Double.parseDouble(condition.substring(1));
        }
        return false;
    }

    private boolean evaluateRentCondition(String condition, BigDecimal value, BigDecimal avgRent) {
        if (condition.startsWith(">=")) {
            return value.compareTo(avgRent.multiply(new BigDecimal(condition.substring(2)))) >= 0;
        } else if (condition.startsWith("<=")) {
            return value.compareTo(avgRent.multiply(new BigDecimal(condition.substring(2)))) <= 0;
        } else if (condition.startsWith(">")) {
            return value.compareTo(avgRent.multiply(new BigDecimal(condition.substring(1)))) > 0;
        } else if (condition.startsWith("<")) {
            return value.compareTo(avgRent.multiply(new BigDecimal(condition.substring(1)))) < 0;
        } else if (condition.startsWith("=")) {
            return value.compareTo(avgRent.multiply(new BigDecimal(condition.substring(1)))) == 0;
        }
        return false;
    }

    private void processRentMatch(TaskScoreDetailEntity details, String code,
        Map<String, List<BuildingParameterEntity>> ruleMap, String cityName) {

        String value = details.getValue(code);
        if (StringUtils.isBlank(value)) {
            return;
        }

        List<BuildingParameterEntity> rules = ruleMap.get(code);
        if (CollectionUtils.isEmpty(rules)) {
            return;
        }

        CityRentEntity cityRent = cityRentService.getFirstByCity(cityName);
        if (Objects.isNull(cityRent)) {
            rules.stream()
                .filter(rule -> rule.getParameterScore().compareTo(BigDecimal.valueOf(7)) == 0)
                .findFirst()
                .ifPresent(rule -> details.setValueId(code + "Score", rule.getParameterScore().multiply(rule.getWeightValue().divide(new BigDecimal(100)))));
            return;
        }

        try {
            BigDecimal price = new BigDecimal(value.replaceAll("[^0-9\\.]", ""));
            BigDecimal avgRent = cityRent.getOfficeRent();
            rules.stream()
                .filter(rule -> evaluateRentCondition(rule.getParameterRule(), price, avgRent))
                .findFirst()
                .ifPresent(rule -> details.setValueId(code + "Score", rule.getParameterScore().multiply(rule.getWeightValue().divide(new BigDecimal(100)))));
        } catch (NumberFormatException e) {
            log.warn("租金格式转换异常: {}", value);
        }
    }

    private Map<String, BigDecimal> getFloorScoreConfig() {
        Map<String, BigDecimal> configMap = new HashMap<>();
        List<SysConfigEntity> configs = sysConfigService.list();

        List<String> keys = Arrays.asList("firstFloorExclusive", "firstFloorShare", "negativeFirstFloor", "negativeTwoFloor", "twoFloorAbove", "thirdFloorBelow", "floor_unqualified_score");

        configs.forEach(config -> {
            if (StringUtils.isNotBlank(config.getValue())) {
                if (keys.contains(config.getKey())) {
                    configMap.put(config.getKey(), new BigDecimal(config.getValue()));
                }
            }
        });

        return configMap;
    }

    // 处理AI数据
    public TaskScoreDetailEntity processAIData(TaskScoreDetailEntity details, Integer buildingType, String cityName) {
        String key = "building_params"+buildingType;
        String jsonString = redisTemplate.opsForValue().get(key);
        if (StringUtils.isBlank(jsonString)) {
            List<BuildingParameterEntity> parameterList = buildingParameterService.list(
                Wrappers.<BuildingParameterEntity>lambdaQuery()
                    .eq(BuildingParameterEntity::getBuildingType, buildingType)
            );
            if (CollectionUtils.isEmpty(parameterList)) {
                throw new BusinessException("参数配置为空");
            }
            jsonString = JSON.toJSONString(parameterList);
            redisTemplate.opsForValue().set(key, jsonString, buildingScoreParamsExpireTime, TimeUnit.SECONDS);
        }

        List<BuildingParameterEntity> parameterList = JSON.parseArray(jsonString, BuildingParameterEntity.class);

        Map<String, List<BuildingParameterEntity>> ruleMap = parameterList.stream()
            .filter(e -> e.getParentId() != 0)
            .collect(Collectors.groupingBy(BuildingParameterEntity::getParameterCode));

        // 处理等级
        processSimpleMatch(details, "buildingGrade", ruleMap,
            (rule, value) -> rule.getParameterRule().equals(value.strip()));

        // 处理地理位置
        processSimpleMatch(details, "buildingLocation", ruleMap,
            (rule, value) -> rule.getParameterRule().equals(value.strip()));

        // 处理楼层数
        processNumberMatch(details, "buildingNumber", ruleMap);

        // 处理月租金
        processRentMatch(details, "buildingPrice", ruleMap, cityName);

        // 处理楼龄
        processNumberMatch(details, "buildingAge", ruleMap);

        // 处理外立面
        processSimilarityMatch(details, "buildingExterior", ruleMap, 0.2);

        // 处理大堂
        processSimilarityMatch(details, "buildingLobby", ruleMap, 0.2);

        // 处理品牌
        processSimpleMatch(details, "buildingBrand", ruleMap,
            (rule, value) -> rule.getParameterRule().equals(value.strip()));

        // 处理评分
        processNumberMatch(details, "buildingRating", ruleMap);

        // 处理地下车库
        processSimilarityMatch(details, "buildingGarage", ruleMap, 0.8);

        return details;
    }

    private void processSimpleMatch(TaskScoreDetailEntity details, String code,
        Map<String, List<BuildingParameterEntity>> ruleMap, BiPredicate<BuildingParameterEntity, String> matcher) {

        String value = details.getValue(code);
        if (StringUtils.isBlank(value)) {
            return;
        }

        List<BuildingParameterEntity> rules = ruleMap.get(code);
        if (CollectionUtils.isEmpty(rules)) {
            return;
        }

        rules.stream()
            .filter(rule -> matcher.test(rule, value))
            .findFirst()
            .ifPresent(rule -> details.setValueId(code + "Score", rule.getParameterScore().multiply(rule.getWeightValue().divide(new BigDecimal(100)))));
    }

    private void processSimilarityMatch(TaskScoreDetailEntity details, String code,
        Map<String, List<BuildingParameterEntity>> ruleMap, double threshold) {

        String value = details.getValue(code);
        if (StringUtils.isBlank(value)) {
            return;
        }

        List<BuildingParameterEntity> rules = ruleMap.get(code);
        if (CollectionUtils.isEmpty(rules)) {
            return;
        }

        value = StringUtils.strip(value);

        value = value.replaceAll(" ", "");

        final String finalValue = value;

        Optional<BuildingParameterEntity> matched = rules.stream()
            .filter(rule -> WordUtils.calculateCosineSimilarity(rule.getParameterRule(), finalValue) > threshold)
            .findFirst();

        if (matched.isPresent()) {
            details.setValueId(code + "Score", matched.get().getParameterScore().multiply(matched.get().getWeightValue().divide(new BigDecimal(100))));
        } else {
            rules.stream()
                .filter(rule -> rule.getParameterScore().compareTo(BigDecimal.valueOf(0.7)) == 0)
                .findFirst()
                .ifPresent(rule -> details.setValueId(code + "Score", rule.getParameterScore().multiply(rule.getWeightValue().divide(new BigDecimal(100)))));
        }
    }

    /**
     * 导出试算任务结果
     *
     * @param taskId 任务ID
     * @return Excel文件COS URL
     */
    public String exportTaskResult(String taskNo) {
        // 1. 查询任务信息
        TaskMainEntity taskMain = taskMainService.getOne(Wrappers.lambdaQuery(TaskMainEntity.class).eq(TaskMainEntity::getTaskNo, taskNo));
        if (taskMain == null) {
            throw new BusinessException("任务不存在");
        }

        // 2. 查询任务明细数据
        List<TaskScoreDetailEntity> detailList = taskScoreDetailService.list(
            Wrappers.lambdaQuery(TaskScoreDetailEntity.class)
                .eq(TaskScoreDetailEntity::getTaskId, taskMain.getId())
        );

        if (detailList.isEmpty()) {
            throw new BusinessException("任务明细数据为空");
        }

        // 3. 转换为导出VO
        List<TaskScoreExportVO> exportList = detailList.stream()
            .map(detail -> {
                TaskScoreExportVO vo = new TaskScoreExportVO();
                BeanUtils.copyProperties(detail, vo);
                vo.setBuildingTypeStr(BuildingTypeEnum.getByCode(vo.getBuildingType()).getDesc());
                vo.setBuildingType(vo.getBuildingType()+1);
                return vo;
            })
            .collect(Collectors.toList());

        // 4. 生成Excel
        try {
            return EasyExcelUtils.createExcelToCos("试算结果", exportList, null, String.format("试算结果-%s.xlsx", taskNo), "tctask");
        } catch (IOException e) {
            throw new BusinessException("生成Excel文件失败");
        }
    }

    public String getTemplateUrl() {
        return templateUrl;
    }

    @PostConstruct
    public void initUnfinishedTasks() {
        log.info("开始处理未完成的测算任务...");
        try {
            // 查询所有状态为"测算中"的任务
            List<TaskMainEntity> unfinishedTasks = taskMainService.list(
                Wrappers.lambdaQuery(TaskMainEntity.class)
                    .eq(TaskMainEntity::getTaskStatus, TCTaskStatus.CALCULATING.getCode())
            );

            if (CollectionUtils.isEmpty(unfinishedTasks)) {
                log.info("没有需要继续处理的测算任务");
                return;
            }

            log.info("发现{}个未完成的测算任务", unfinishedTasks.size());

            // 异步处理每个未完成的任务
            for (TaskMainEntity task : unfinishedTasks) {
                CompletableFuture.runAsync(() -> {
                    try {
                        log.info("开始处理未完成的测算任务: taskNo={}", task.getTaskNo());
                        executeTaskAsync(task.getId());
                    } catch (Exception e) {
                        log.error("处理未完成的测算任务失败: taskNo={}, error={}", task.getTaskNo(), e.getMessage(), e);
                    }
                }, taskExecutor);
            }
        } catch (Exception e) {
            log.error("初始化未完成测算任务失败", e);
        }
    }

}
