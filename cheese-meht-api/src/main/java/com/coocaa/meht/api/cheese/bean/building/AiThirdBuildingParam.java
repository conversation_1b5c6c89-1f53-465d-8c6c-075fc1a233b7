package com.coocaa.meht.api.cheese.bean.building;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * AI第三方楼宇数据查询参数
 * <AUTHOR>
 * @since 2025/5/28
 */
@Data
public class AiThirdBuildingParam {

    /**
     * 楼宇编号集合
     */
    private List<String> buildingNos;

    /**
     * 是否数据过滤(true为过滤，false不过滤)
     */
    private boolean filter;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

}
