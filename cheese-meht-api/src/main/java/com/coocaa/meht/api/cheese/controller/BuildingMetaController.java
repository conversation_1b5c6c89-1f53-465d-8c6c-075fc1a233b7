package com.coocaa.meht.api.cheese.controller;

import com.coocaa.meht.api.cheese.bean.building.*;
import com.coocaa.meht.api.cheese.common.tools.result.PageRequestVo;
import com.coocaa.meht.api.cheese.common.tools.result.PageResponseVo;
import com.coocaa.meht.api.cheese.common.tools.result.ResultTemplate;
import com.coocaa.meht.api.cheese.service.BuildingMetaApiService;
import com.coocaa.meht.api.cheese.vo.BuildingPropertyCompanyVO;
import com.coocaa.meht.api.cheese.vo.building.BuildingMetaListVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/16
 */

@Tag(name = "楼宇库相关功能")
@RestController
@RequestMapping("/building/meta")
public class BuildingMetaController {

    @Autowired
    private BuildingMetaApiService buildingMetaApiService;

    @Operation(summary = "楼宇库列表")
    @PostMapping("/list")
    public ResultTemplate<PageResponseVo<BuildingMetaListVO>> listBuildingMeta(@RequestBody PageRequestVo<BuildingMetaQueryDTO> pageRequestVo) {

        return ResultTemplate.success(buildingMetaApiService.listBuildingMeta(pageRequestVo));
    }

    @Operation(summary = "楼宇库详情")
    @GetMapping("/{id}")
    public ResultTemplate<BuildingMetaDetailVO> getMetaDetailById(@PathVariable("id") Long id) {

        return ResultTemplate.success(buildingMetaApiService.getMetaDetailById(id));
    }

    @Operation(summary = "楼宇库修改")
    @PutMapping("/update")
    public ResultTemplate<Void> updateMeta(@RequestBody @Validated BuildingMetaUpdateParam param) {
        buildingMetaApiService.updateMeta(param);
        return ResultTemplate.success();
    }

    @Operation(summary = "价格申请点位方案")
    @GetMapping("/snapshot/price/{pointPlanId}")
    public ResultTemplate<List<BuildingMetaPointDetailVO>> getPricePointDetailById(@PathVariable("pointPlanId") Integer pointPlanId) {

        return ResultTemplate.success(buildingMetaApiService.getPricePointDetailByPointPlanId(pointPlanId));
    }

    @Operation(summary = "合同申请点位方案")
    @GetMapping("/snapshot/contract/{pointPlanId}")
    public ResultTemplate<List<BuildingMetaPointDetailVO>> getContractPointDetailById(@PathVariable("pointPlanId") Integer pointPlanId) {

        return ResultTemplate.success(buildingMetaApiService.getContractPointDetailByPointPlanId(pointPlanId));
    }

    @Operation(summary = "同步数据")
    @GetMapping("/sync")
    public ResultTemplate<Void> syncBuildingMeta() {
        buildingMetaApiService.syncBuildingMeta();
        return ResultTemplate.success();
    }

    @Operation(summary = "保存ai楼宇数据,首次上线使用")
    @PostMapping("/save/first")
    public ResultTemplate<Void> firstSave(@RequestBody BuildingMetaImportParam param) {
        buildingMetaApiService.firstSave(param);
        return ResultTemplate.success();
    }

    @Operation(summary = "楼宇关联物业公司")
    @GetMapping("/property/list")
    public ResultTemplate<List<BuildingPropertyCompanyVO>> getPropertyPerson(@RequestParam(name = "buildingNo") String buildingNo) {
        return ResultTemplate.success(buildingMetaApiService.getPropertyPerson(buildingNo));
    }


    @Operation(summary = "根据名称查询楼宇供ssp使用")
    @PostMapping("/query/name")
    public ResultTemplate<List<SspBuildingVO>> queryByName(@RequestBody Collection<String> names) {
        return ResultTemplate.success(buildingMetaApiService.queryByName(names));
    }

    @Operation(summary = "从ssp的楼宇同步到楼宇")
    @PutMapping("/ssp/sync")
    public ResultTemplate<String> syncBySsp(@RequestBody SspSyncBuilding sspSyncBuilding) {
        return ResultTemplate.success(buildingMetaApiService.syncBySsp(sspSyncBuilding));
    }

    @Operation(summary = "通过状态查询楼宇数据")
    @GetMapping("/query/status/{status}")
    public ResultTemplate<List<SspBuildingVO>> queryByStatus(@PathVariable("status") Integer status) {
        return ResultTemplate.success(buildingMetaApiService.queryByStatus(status));
    }

    @Operation(summary = "刷新线上地图楼宇名称")
    @PostMapping("/update/name-ai")
    public ResultTemplate<Void> updateBuildingNameAi(@RequestBody BuildingMetaImportParam param) {
        buildingMetaApiService.updateBuildingNameAi(param);
        return ResultTemplate.success();
    }


    @Operation(summary = "刷经纬度")
    @PostMapping("/update/latitude")
    public ResultTemplate<Void> updateLatitude(@RequestBody List<Long> ids) {
        buildingMetaApiService.updateLatitude(ids);
        return ResultTemplate.success();
    }


    @PostMapping("/list/export")
    @Operation(summary = "导出楼宇数据")
    public void exportMeta(@RequestBody BuildingMetaQueryDTO dto, HttpServletResponse response) throws IOException {
        byte[] data = buildingMetaApiService.exportMeta(dto);
        
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("楼宇数据导出" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")), "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        
        try (OutputStream outputStream = response.getOutputStream()) {
            outputStream.write(data);
            outputStream.flush();
        }
    }

    @Operation(summary = "刷禁忌行业")
    @PostMapping("/refresh/forbidden-industry")
    public ResultTemplate<Void> refreshForbiddenIndustry(@RequestBody RefreshForbiddenIndustryParam param) {
        buildingMetaApiService.refreshForbiddenIndustry(param);
        return ResultTemplate.success();
    }

    @Operation(summary = "ssp同步修改数据")
    @PostMapping("/update/by-ssp")
    public ResultTemplate<Void> updateBaseMessage(@RequestBody UpdateBaseMessageParam param) {
        buildingMetaApiService.updateBaseMessage(param);
        return ResultTemplate.success();
    }

    @Operation(summary = "刷buildingMeta表的经纬度，地址")
    @PostMapping("/update/meta-message")
    public ResultTemplate<Void> updateMetaMessage(@RequestBody List<Long> ids) {
        buildingMetaApiService.updateMetaMessage(ids);
        return ResultTemplate.success();
    }

    @Operation(summary = "刷building rating表中的ai评级")
    @GetMapping("/ai")
    public ResultTemplate<Void> processAiMeta(@RequestParam(name = "id", required = false) Long id) {
        buildingMetaApiService.processAiMeta(id);
        return ResultTemplate.success();
    }

    @Operation(summary = "豆包楼宇信息")
    @PostMapping("/doubao")
    public ResultTemplate<Void> doubao(@RequestBody AiThirdBuildingParam param) {
        buildingMetaApiService.doubao(param);
        return ResultTemplate.success();
    }

    @Operation(summary = "deepseek楼宇信息")
    @PostMapping("/deepseek")
    public ResultTemplate<Void> deepseek(@RequestBody AiThirdBuildingParam param) {
        buildingMetaApiService.deepseek(param);
        return ResultTemplate.success();
    }

    @Operation(summary = "通义千问楼宇信息")
    @PostMapping("/tyqw")
    public ResultTemplate<Void> tyqw(@RequestBody AiThirdBuildingParam param) {
        buildingMetaApiService.tyqw(param);
        return ResultTemplate.success();
    }

    @Operation(summary = "openAi楼宇信息")
    @PostMapping("/openai")
    public ResultTemplate<Void> openAi(@RequestBody AiThirdBuildingParam param) {
        buildingMetaApiService.openAi(param);
        return ResultTemplate.success();
    }

    @Operation(summary = "刷租金数据")
    @GetMapping("/rent")
    public ResultTemplate<Void> rent() {
        buildingMetaApiService.rent();
        return ResultTemplate.success();
    }

}
