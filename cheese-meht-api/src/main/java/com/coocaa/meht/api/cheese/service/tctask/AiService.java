package com.coocaa.meht.api.cheese.service.tctask;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.coocaa.meht.api.cheese.utils.HttpUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Slf4j
@Component
public class AiService {


    @Value("${douAi:https://ark.cn-beijing.volces.com/}")
    private String doMain;

    @Value("${apiKey:c257efd9-0476-4911-b6aa-f67ae5699a57}")
    private String apiKey;

    @Value("${aiBot:bot-20241203094513-wntk6}")
    private String bot;

    @Value("${deepSeekBot:bot-20250421103351-bgtbb}")
    private String deepSeekBot;


    public Map<String,Object> getBuildingAppraiser(String address) {
        Map<String,Object> map = new HashMap<>();
        Map<String, String> header = new HashMap<>();
        header.put("Accept", "*/*");
        header.put("Connection", "keep-alive");
        header.put("Authorization", "Bearer " + apiKey);
        header.put("Content-Type", "application/json; charset=utf-8;");
        map.put("model", bot);
        map.put("object","chat.completion");

        Map<String,Object> streamOptions = new HashMap<>();
        streamOptions.put("include_usage",true);
        map.put("stream_options",streamOptions);

        Map<String,Object> messages = new HashMap<>();
        messages.put("role","user");
        messages.put("content", address);
        map.put("messages", Arrays.asList(messages));
        try {
            String body = JSON.toJSONString(map);
            log.debug("请求体JSON: {}", body);

            log.info("开始调用豆包AI评估API...");
            String response = HttpUtils.post(doMain + "api/v3/bots/chat/completions", header, body);
            log.info("豆包AI评估API响应: {}", response);

            Map<String, Object> objectMap = JSON.parseObject(response, new TypeReference<Map<String, Object>>(){}.getType());
            Object choices = objectMap.get("choices");
            List<Map<String, Object>> choicesList = JSON.parseArray(JSON.toJSONString(choices), new TypeReference<Map<String, Object>>(){}.getType());

            if (CollectionUtils.isNotEmpty(choicesList)){
                Map<String, Object> stringObjectMap = choicesList.get(0);
                Object message = stringObjectMap.get("message");
                Object content = JSON.parseObject(JSON.toJSONString(message)).get("content");
                String contentStr = content.toString();
                log.debug("原始响应内容: {}", contentStr);

                try {
                    Map<String, Object> result = JSON.parseObject(contentStr, new TypeReference<Map<String, Object>>(){}.getType());
                    log.info("成功解析API响应为JSON");
                    return result;
                } catch (Exception e){
                    log.error("解析内容失败: {}", contentStr, e);

                    Map<String, Object> contentResult = null;
                    int retryCount = 0;
                    int maxRetries = 3;

                    while (retryCount <= maxRetries) {
                        try {
                            log.info("尝试第{}次重试解析...", retryCount + 1);
                            contentResult = getContentResult(content.toString());
                            log.info("第{}次重试解析成功", retryCount + 1);
                            break;
                        } catch (Exception ex) {
                            retryCount++;
                            log.warn("第{}次重试解析失败", retryCount, ex);
                        }
                    }

                    if (contentResult != null) {
                        return contentResult;
                    } else {
                        log.error("所有重试解析均失败");
                    }
                }
            } else {
                log.warn("API返回的选择列表为空，返回原始响应");
                return null;
            }
        } catch (Exception e) {
            log.error("获取楼宇评估数据时发生异常", e);
        }

        log.warn("获取楼宇评估数据失败，返回null");
        return null;
    }

    private Map<String, Object> getContentResult(String content) {
        return JSONObject.parseObject(content.toString(), Map.class);
    }


    public Map<String,Object> getDeepSeekBuildingAppraiser(String address) {
        log.info("开始获取DeepSeek商务楼宇评估数据，地址: {}", address);

        Map<String,Object> map = new HashMap<>();
        Map<String, String> header = new HashMap<>();
        header.put("Accept", "*/*");
        header.put("Connection", "keep-alive");
        header.put("Authorization", "Bearer " + apiKey);
        header.put("Content-Type", "application/json; charset=utf-8;");
        log.debug("请求头设置完成: {}", header);

        map.put("model", deepSeekBot);
        map.put("object", "chat.completion");

        Map<String,Object> streamOptions = new HashMap<>();
        streamOptions.put("include_usage", true);
        map.put("stream_options", streamOptions);

        Map<String,Object> messages = new HashMap<>();
        messages.put("role", "user");
        messages.put("content", address);
        map.put("messages", Arrays.asList(messages));
        log.debug("请求参数设置完成");

        try {
            String body = JSON.toJSONString(map);
            log.debug("请求体JSON: {}", body);

            log.info("开始调用DeepSeek API...");
            String response = HttpUtils.post(doMain + "api/v3/bots/chat/completions", header, body);
            log.info("DeepSeek API响应: {}", response);

            Map<String, Object> objectMap = JSON.parseObject(response, new TypeReference<Map<String, Object>>(){}.getType());
            Object choices = objectMap.get("choices");
            List<Map<String, Object>> choicesList = JSON.parseArray(JSON.toJSONString(choices), new TypeReference<Map<String, Object>>(){}.getType());

            if (CollectionUtils.isNotEmpty(choicesList)) {
                Map<String, Object> stringObjectMap = choicesList.get(0);
                Object message = stringObjectMap.get("message");
                Object content = JSON.parseObject(JSON.toJSONString(message)).get("content");
                String contentStr = content.toString();
                log.debug("原始响应内容: {}", contentStr);

                // 截取第一个{之后和第一个}之前的字符串
                int startIndex = contentStr.indexOf("{");
                int endIndex = contentStr.indexOf("}", startIndex);
                if (startIndex >= 0 && endIndex > startIndex) {
                    String extractedStr = contentStr.substring(startIndex, endIndex + 1);
                    log.debug("提取后的JSON内容: {}", extractedStr);
                    if (StringUtils.isBlank(extractedStr)) {
                        log.warn("提取的JSON内容为空");
                        return null;
                    }

                    try {
                        Map<String, Object> result = JSON.parseObject(extractedStr, new TypeReference<Map<String, Object>>(){}.getType());
                        log.info("成功解析API响应为JSON");
                        return result;
                    } catch (Exception e) {
                        log.error("解析内容失败: {}", extractedStr, e);
                    }
                } else {
                    log.error("未找到有效的JSON结构");
                }
            } else {
                log.warn("API返回的选择列表为空，返回原始响应");
                return null;
            }
        } catch (Exception e) {
            log.error("DeepSeek API调用失败", e);
        }

        log.warn("获取DeepSeek评估数据失败，返回null");
        return null;
    }


    public Map<String,Object> getQWBuildingAppraiser(String address) {

        Map<String, String> header = new HashMap<>();
        header.put("Authorization", "Bearer " + "sk-775b1ac273224b4b92ea8c32699a24b3");
        header.put("Content-Type", "application/json");
        header.put("X-DashScope-SSE", "");

        Map<String,Object> requestBody = new HashMap<>();
        requestBody.put("model", "qwen-plus");
        requestBody.put("task_group", "aigc");
        requestBody.put("task", "text-generation");
        requestBody.put("function", "generation");

        Map<String,Object> systemMessage = new HashMap<>();
        systemMessage.put("role", "system");

        Map<String,String> systemContent = new HashMap<>();
        systemContent.put("text", "# 角色：你是一位专业的商务楼宇数据提取工程师，专注于从互联网精准抽取指定格式的数据，并以JSON格式输出。# 输出要求：1.只返回json，字段值不做任何说明、描述和注释，也不需要加括号说明2.如果单个数据未匹配，请返回空字段3.不要描述数据来源和字段说明4.数据都返回字符串格式5.除上述总体约束条件外，必须严格按照第三部分的限制条件来输出，不输出限制条件以外的字段。例如：匹配出来为地下 1 层，必须转换为地下车库有1层；例如：匹配出来的楼龄为约16年，必须转换为16年；例如：匹配出来为6 层（商业部分），必须转换为6；例如匹配出来为约 120 元 / 平米，必须转换为120 元 / 平米；例如：匹配出来为地下车库有 3 层，必须转换为地下车库有2层及以上；例如：匹配出来为约 16 年，必须转换为16 6.互联网信息作为最重要的参照，请严格互联网信息的信息提取精确数据，不要进行推断或假设提取的内容和格式必须严格按照如下的json格式输出： {         \\\"thirdBuildingType\\\":\\\"类型  写字楼 商住楼 综合体 产业园区 哪一个\\\"        \\\"thirdBuildingGrade\\\": \\\"楼宇等级， 例如：超甲级、甲级、乙级或者其它\\\",        \\\"thirdBuildingLocation\\\": \\\"地理位置是否为城市核心区 例如：城市核心区\\\",        \\\"thirdBuildingNumber\\\": \\\"楼层数，例如：32\\\",         \\\"thirdBuildingPrice\\\": \\\"月租金，例如：80元/平米\\\",        \\\"thirdBuildingAge\\\":\\\"楼龄，以年为单位，例如：13\\\",        \\\"thirdBuildingExterior:\\\"堂高度及装修情况 例如：7米以上的精装修(石材、木材、玻璃装饰、陶瓷、不锈钢)\\\"       \\\"thirdBuildingLobby\\\": \\\"外墙是否有脱落、裂痕，例如：脱落\\\",         \\\"thirdBuildingGarage\\\": \\\"地下车库的层数，例如：地下车库有2层及以上\\\",        \\\"thirdBuildingRate\\\": \\\"大众点评的分数，例如：4.5\\\",        \\\"thirdBuildingBrand\\\": \\\"综合体品牌，例如：全国连锁品牌\\\"}# 限制条件： thirdBuildingType:  写字楼、商住楼、综合体、产业园区thirdBuildingGrade：超甲级、甲级、乙级或者其它thirdBuildingLocation：城市核心区、非城市核心区thirdBuildingNumber：1-600thirdBuildingPrice：XX元/平米thirdBuildingAge：1-100thirdBuildingExterior：铝合金、优质石材、高档陶瓷、玻璃幕墙、涂料，砖，马赛克thirdBuildingLobby：大堂高度2-10米，精装修，简装，无 （不了解的情况下定义为无）thirdBuildingGarage：地下车库有2层及以上、地下车库有1层、无地下车库thirdBuildingRate：0-5thirdBuildingBrand：全国连锁品牌、区域连锁品牌、其他#楼宇等级（thirdBuildingGrade）的判定标准如下：超甲级写字楼（Grade A+ Office Buildings）地理位置：位于城市的核心商务区，交通便利，周边商业配套成熟。建筑设计：由国际知名建筑事务所设计，外观现代、高端，具有标志性。建筑质量：使用高标准的建筑材料，内部装修豪华，设施先进。内部设施：配备高速电梯、先进的空调系统、智能化安防系统、高端会议室等。服务配套：提供国际标准的物业管理服务，包括24小时安保、清洁、维修等。租赁客户：主要吸引世界500强企业、跨国公司和高端金融服务企业。租金水平：租金水平远高于市场平均水平。甲级写字楼（Grade A Office Buildings）地理位置：位于城市的主要商务区或次核心区域，交通便利。建筑设计：外观设计现代，内部装修精致。建筑质量：使用较好的建筑材料，内部设施完善。内部设施：配备多部电梯、中央空调、基本的安防系统、会议室等。服务配套：提供专业的物业管理服务，包括安保、清洁、维修等。租赁客户：主要吸引中大型企业、国内上市公司和一些外资企业。租金水平：租金水平高于市场平均水平。乙级写字楼（Grade B Office Buildings）地理位置：位于城市的非核心商务区，交通相对便利。建筑设计：外观设计较为普通，内部装修一般。建筑质量：使用标准的建筑材料，内部设施基本齐全。内部设施：配备基本的电梯、空调、安防系统等。服务配套：提供基本的物业管理服务，包括安保、清洁等。租赁客户：主要吸引中小企业、创业公司和一些预算有限的企业。租金水平：租金水平接近或略低于市场平均水平。#输入要求：1、城市2、商务楼宇名称"); // 简略显示
        systemMessage.put("content", Arrays.asList(systemContent));

        Map<String,Object> userMessage = new HashMap<>();
        userMessage.put("role", "user");

        Map<String,String> userContent = new HashMap<>();
        userContent.put("text", address);
        userMessage.put("content", Arrays.asList(userContent));

        Map<String,Object> input = new HashMap<>();
        input.put("messages", Arrays.asList(systemMessage, userMessage));
        requestBody.put("input", input);

        Map<String,Object> parameters = new HashMap<>();
        parameters.put("result_format", "message");
        parameters.put("enable_search", true);

        Map<String,Object> searchOptions = new HashMap<>();
        searchOptions.put("enable_source", true);
        searchOptions.put("enable_citation", true);
        searchOptions.put("citation_format", "[<number>]");
        searchOptions.put("search_strategy", "pro_max");
        searchOptions.put("forced_search", true);
        searchOptions.put("prepend_search_result", false);
        searchOptions.put("enable_search_extension", true);

        parameters.put("search_options", searchOptions);
        requestBody.put("parameters", parameters);

        try {
            String body = JSON.toJSONString(requestBody);
            log.debug("请求体JSON: {}", body);

            log.info("开始调用通义千问API...");
            String response = HttpUtils.post("https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation", header, body);
            log.info("通义千问API响应: {}", response);

            Map<String, Object> objectMap = JSON.parseObject(response, new TypeReference<Map<String, Object>>(){}.getType());
            Object output = objectMap.get("output");

            if (output != null) {
                Map<String, Object> outPutMap = JSON.parseObject(JSON.toJSONString(output), new TypeReference<Map<String, Object>>(){}.getType());
                Object choices = outPutMap.get("choices");
                List<Map<String, Object>> choicesList = JSON.parseArray(JSON.toJSONString(choices), new TypeReference<Map<String, Object>>(){}.getType());

                if (CollectionUtils.isNotEmpty(choicesList)) {
                    Map<String, Object> stringObjectMap = choicesList.get(0);
                    Object message = stringObjectMap.get("message");
                    Object content = JSON.parseObject(JSON.toJSONString(message)).get("content");
                    String contentStr = content.toString();
                    log.debug("原始响应内容: {}", contentStr);
                    // 截取第一个{之后和第一个}之前的字符串，包括这两个字符。
                    int startIndex = contentStr.indexOf("{");
                    int endIndex = contentStr.indexOf("}", startIndex);
                    String extractedStr = contentStr.substring(startIndex, endIndex + 1);
                    if (StringUtils.isBlank(extractedStr)) {
                        log.warn("提取的字符串为空，返回原始响应");
                        return null;
                    }
                    try {
                        Map<String, Object> result = JSON.parseObject(extractedStr, new TypeReference<Map<String, Object>>(){}.getType());
                        log.info("成功解析API响应为JSON");
                        return result;
                    } catch (Exception e) {
                        log.error("解析内容失败: {}", contentStr, e);

                        Map<String, Object> contentResult = null;
                        int retryCount = 0;
                        int maxRetries = 3;

                        while (retryCount <= maxRetries) {
                            try {
                                log.info("尝试第{}次重试解析...", retryCount + 1);
                                contentResult = getContentResult(content.toString());
                                log.info("第{}次重试解析成功", retryCount + 1);
                                break;
                            } catch (Exception ex) {
                                retryCount++;
                                log.warn("第{}次重试解析失败", retryCount, ex);
                            }
                        }

                        if (contentResult != null) {
                            return contentResult;
                        } else {
                            log.error("所有重试解析均失败");
                        }
                    }
                } else {
                    log.warn("API返回的选择列表为空，返回原始响应");
                    return null;
                }
            } else {
                log.error("API响应中没有output字段");
            }
        } catch (Exception e) {
            log.error("获取商务楼宇评估数据时发生异常", e);
        }

        log.warn("获取商务楼宇评估数据失败，返回null");
        return null;
    }

    public Map<String, Object> getOpenAIBuildingAppraiser(String address) {

        log.info("开始获取OpenAI商务楼宇评估数据，地址: {}", address);

        Map<String,Object> map = new HashMap<>();
        Map<String, String> header = new HashMap<>();
        header.put("Accept", "*/*");
        header.put("Connection", "keep-alive");
        header.put("Authorization", "Bearer " + "sk-04whOXb8GIYYwcdYahlc0SSM34rPWyNtxhAr2hrikyaqZQvX");
        header.put("Content-Type", "application/json; charset=utf-8;");
        log.debug("请求头设置完成: {}", header);

        map.put("model", "moonshot-v1-8k");

        map.put("temperature", 0.3);

        Map<String,Object> userMessage = new HashMap<>();
        userMessage.put("role", "user");
        userMessage.put("content", address);
        Map<String,Object> systemMessage = new HashMap<>();
        systemMessage.put("role", "system");
        systemMessage.put("content", "# 角色：你是一位专业的商务楼宇数据提取工程师，专注于从互联网精准抽取指定格式的数据，并以JSON格式输出。# 输出要求：1.只返回json，字段值不做任何说明、描述和注释，也不需要加括号说明2.如果单个数据未匹配，请返回空字段3.不要描述数据来源和字段说明4.数据都返回字符串格式5.除上述总体约束条件外，必须严格按照第三部分的限制条件来输出，不输出限制条件以外的字段。例如：匹配出来为地下 1 层，必须转换为地下车库有1层；例如：匹配出来的楼龄为约16年，必须转换为16年；例如：匹配出来为6 层（商业部分），必须转换为6；例如匹配出来为约 120 元 / 平米，必须转换为120 元 / 平米；例如：匹配出来为地下车库有 3 层，必须转换为地下车库有2层及以上；例如：匹配出来为约 16 年，必须转换为16 6.互联网信息作为最重要的参照，请严格互联网信息的信息提取精确数据，不要进行推断或假设提取的内容和格式必须严格按照如下的json格式输出： {         \\\"thirdBuildingType\\\":\\\"类型  写字楼 商住楼 综合体 产业园区 哪一个\\\"        \\\"thirdBuildingGrade\\\": \\\"楼宇等级， 例如：超甲级、甲级、乙级或者其它\\\",        \\\"thirdBuildingLocation\\\": \\\"地理位置是否为城市核心区 例如：城市核心区\\\",        \\\"thirdBuildingNumber\\\": \\\"楼层数，例如：32\\\",         \\\"thirdBuildingPrice\\\": \\\"月租金，例如：80元/平米\\\",        \\\"thirdBuildingAge\\\":\\\"楼龄，以年为单位，例如：13\\\",        \\\"thirdBuildingExterior:\\\"堂高度及装修情况 例如：7米以上的精装修(石材、木材、玻璃装饰、陶瓷、不锈钢)\\\"       \\\"thirdBuildingLobby\\\": \\\"外墙是否有脱落、裂痕，例如：脱落\\\",         \\\"thirdBuildingGarage\\\": \\\"地下车库的层数，例如：地下车库有2层及以上\\\",        \\\"thirdBuildingRate\\\": \\\"大众点评的分数，例如：4.5\\\",        \\\"thirdBuildingBrand\\\": \\\"综合体品牌，例如：全国连锁品牌\\\"}# 限制条件： thirdBuildingType:  写字楼、商住楼、综合体、产业园区thirdBuildingGrade：超甲级、甲级、乙级或者其它thirdBuildingLocation：城市核心区、非城市核心区thirdBuildingNumber：1-600thirdBuildingPrice：XX元/平米thirdBuildingAge：1-100thirdBuildingExterior：铝合金、优质石材、高档陶瓷、玻璃幕墙、涂料，砖，马赛克thirdBuildingLobby：大堂高度2-10米，精装修，简装，无 （不了解的情况下定义为无）thirdBuildingGarage：地下车库有2层及以上、地下车库有1层、无地下车库thirdBuildingRate：0-5thirdBuildingBrand：全国连锁品牌、区域连锁品牌、其他#楼宇等级（thirdBuildingGrade）的判定标准如下：超甲级写字楼（Grade A+ Office Buildings）地理位置：位于城市的核心商务区，交通便利，周边商业配套成熟。建筑设计：由国际知名建筑事务所设计，外观现代、高端，具有标志性。建筑质量：使用高标准的建筑材料，内部装修豪华，设施先进。内部设施：配备高速电梯、先进的空调系统、智能化安防系统、高端会议室等。服务配套：提供国际标准的物业管理服务，包括24小时安保、清洁、维修等。租赁客户：主要吸引世界500强企业、跨国公司和高端金融服务企业。租金水平：租金水平远高于市场平均水平。甲级写字楼（Grade A Office Buildings）地理位置：位于城市的主要商务区或次核心区域，交通便利。建筑设计：外观设计现代，内部装修精致。建筑质量：使用较好的建筑材料，内部设施完善。内部设施：配备多部电梯、中央空调、基本的安防系统、会议室等。服务配套：提供专业的物业管理服务，包括安保、清洁、维修等。租赁客户：主要吸引中大型企业、国内上市公司和一些外资企业。租金水平：租金水平高于市场平均水平。乙级写字楼（Grade B Office Buildings）地理位置：位于城市的非核心商务区，交通相对便利。建筑设计：外观设计较为普通，内部装修一般。建筑质量：使用标准的建筑材料，内部设施基本齐全。内部设施：配备基本的电梯、空调、安防系统等。服务配套：提供基本的物业管理服务，包括安保、清洁等。租赁客户：主要吸引中小企业、创业公司和一些预算有限的企业。租金水平：租金水平接近或略低于市场平均水平。#输入要求：1、城市2、商务楼宇名称"); // 简略显示
        map.put("messages", Arrays.asList(systemMessage, userMessage));
        log.debug("请求参数设置完成");

        try {
            String body = JSON.toJSONString(map);
            log.debug("请求体JSON: {}", body);

            log.info("开始调用OpenAI API...");
            String response = HttpUtils.post("https://api.moonshot.cn/v1/chat/completions", header, body);
            log.info("OpenAI API响应: {}", response);

            Map<String, Object> objectMap = JSON.parseObject(response, new TypeReference<Map<String, Object>>(){}.getType());
            Object choices = objectMap.get("choices");
            List<Map<String, Object>> choicesList = JSON.parseArray(JSON.toJSONString(choices), new TypeReference<Map<String, Object>>(){}.getType());

            if (CollectionUtils.isNotEmpty(choicesList)) {
                Map<String, Object> stringObjectMap = choicesList.get(0);
                Object message = stringObjectMap.get("message");
                Object content = JSON.parseObject(JSON.toJSONString(message)).get("content");
                String contentStr = content.toString();
                log.debug("原始响应内容: {}", contentStr);

                // 截取第一个{之后和第一个}之前的字符串
                int startIndex = contentStr.indexOf("{");
                int endIndex = contentStr.indexOf("}", startIndex);
                if (startIndex >= 0 && endIndex > startIndex) {
                    String extractedStr = contentStr.substring(startIndex, endIndex + 1);
                    log.debug("提取后的JSON内容: {}", extractedStr);
                    if (StringUtils.isBlank(extractedStr)) {
                        return null;
                    }
                    try {
                        Map<String, Object> result = JSON.parseObject(extractedStr, new TypeReference<Map<String, Object>>(){}.getType());
                        log.info("成功解析API响应为JSON");
                        return result;
                    } catch (Exception e) {
                        log.error("解析内容失败: {}", extractedStr, e);
                    }
                } else {
                    log.error("未找到有效的JSON结构");
                }
            } else {
                log.warn("API返回的选择列表为空，返回原始响应");
                return null;
            }
        } catch (Exception e) {
            log.error("OpenAI API调用失败", e);
        }

        log.warn("获取OpenAI评估数据失败，返回null");
        return null;
    }
}
