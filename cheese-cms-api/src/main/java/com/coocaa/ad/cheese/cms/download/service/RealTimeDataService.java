/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.coocaa.ad.cheese.cms.download.service;

import com.coocaa.ad.cheese.cms.download.enums.DownLoadTypeEnum;
import com.coocaa.ad.cheese.cms.download.handler.CmsSysTypeHandlerService;
import com.coocaa.ad.cheese.cms.venue.service.KanbanService;
import com.coocaa.ad.common.user.UserCacheHelper;
import com.coocaa.ad.downloader.bean.dto.AttachmentDTO;
import com.coocaa.ad.downloader.core.AbstractDownloaderProcessor;
import com.coocaa.ad.downloader.enums.IDownloaderType;
import com.coocaa.ad.downloader.rpc.FeignAttachmentRpc;
import com.coocaa.ad.downloader.rpc.FeignTaskRpc;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 * 实时数据导出
 * <AUTHOR>
 * @since 1.1.0
 */
@Slf4j
@Service
public class RealTimeDataService extends AbstractDownloaderProcessor {

    private final KanbanService kanbanService;

    public RealTimeDataService(RedissonClient redissonClient, FeignTaskRpc taskRpc,
                               FeignAttachmentRpc attachmentRpc,
                               UserCacheHelper userCacheHelper,
                               CmsSysTypeHandlerService cmsSysTypeHandlerService,
                               KanbanService kanbanService) {
        super(redissonClient, taskRpc, attachmentRpc, userCacheHelper, cmsSysTypeHandlerService);
        this.kanbanService = kanbanService;
    }

    @Override
    protected IDownloaderType getProcessorType() {
        return DownLoadTypeEnum.SSSJ;
    }

    @Override
    protected List<AttachmentDTO> doProcess(String executeParams) {
        String url = kanbanService.exportDataForContract(1, null, DownLoadTypeEnum.SSSJ.getDesc());
        return List.of(new AttachmentDTO().setName(DownLoadTypeEnum.SSSJ.getDesc()).setUrl(url));
    }


}
