package com.coocaa.ad.cheese.cms.venue.controller.contract;

import com.coocaa.ad.cheese.cms.common.config.annotation.OperationLog;
import com.coocaa.ad.cheese.cms.common.config.annotation.StatusChangeLog;
import com.coocaa.ad.cheese.cms.common.tools.common.result.PageRequestVo;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.BusinessBelongEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.StatusChangeTypeEnum;
import com.coocaa.ad.cheese.cms.venue.bean.contract.AbContractApprovalParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.AbContractOperateParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.AbContractQueryParam;
import com.coocaa.ad.cheese.cms.venue.service.AbContractApprovalService;
import com.coocaa.ad.cheese.cms.venue.vo.contract.AbContractApprovalDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.AbContractApprovalVO;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.common.result.ResultTemplate;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

/**
 * 异常合同审批中心
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-24
 */
@Slf4j
@RestController
@RequestMapping("/venue/contract/abnormal/approval")
@Tag(name = "异常合同审批中心", description = "异常合同审批中心")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class AbContractApprovalController {
    private final AbContractApprovalService abContractApprovalService;

    /**
     * 异常合同列表查询
     */
    @Operation(summary = "异常合同列表")
    @PostMapping("/list")
    public ResultTemplate<PageResponseVO<AbContractApprovalVO>> pageListAbContract(@RequestBody PageRequestVo<AbContractQueryParam> pageRequest) {
        pageRequest.setQuery(Optional.ofNullable(pageRequest.getQuery()).orElseGet(AbContractQueryParam::new));
        return ResultTemplate.success(abContractApprovalService.pageListAbContract(pageRequest));
    }

    /**
     * 撤回合同
     */
    @Operation(summary = "撤回合同")
    @OperationLog(type = BusinessBelongEnum.ABNORMAL_CONTRACT, bizId = "#id", operateType = "0103-3")
    @PutMapping("/{id}/withdraw")
    public ResultTemplate<Boolean> withdrawContract(@PathVariable(name = "id") Integer id) {
        return ResultTemplate.success(abContractApprovalService.withdrawContract(id));
    }

    /**
     * 填写处理结果
     */
    @Operation(summary = "填写处理结果")
    @StatusChangeLog(type = StatusChangeTypeEnum.ABNORMAL_CONTRACT, bizId = "#id", status = "0102-6")
    @PutMapping("/{id}/write-handle-result")
    public ResultTemplate<Boolean> writeHandleResult(@PathVariable(name = "id") Integer id,
                                                     @Validated @RequestBody AbContractOperateParam param) {
        return ResultTemplate.success(abContractApprovalService.writeHandleResult(id, param));
    }

    /**
     * 余总驳回合同
     */
    @Operation(summary = "余总驳回合同")
    @OperationLog(type = BusinessBelongEnum.ABNORMAL_CONTRACT, bizId = "#id", operateType = "0103-8")
    @StatusChangeLog(type = StatusChangeTypeEnum.ABNORMAL_CONTRACT, bizId = "#id", status = "0102-6")
    @PutMapping("/{id}/finalReject")
    public ResultTemplate<Boolean> finalReject(@PathVariable(name = "id") Integer id) {
        return ResultTemplate.success(abContractApprovalService.finalReject(id));
    }

    /**
     * 查询某异常合同详情
     */
    @Operation(summary = "查询某异常合同详情")
    @Parameter(name = "id", description = "异常合同ID", required = true, in = ParameterIn.PATH, schema = @Schema(type = "int"))
    @GetMapping("/{id}")
    public ResultTemplate<AbContractApprovalDetailVO> queryAbContractDetail(@PathVariable("id") Integer id) {
        return ResultTemplate.success(abContractApprovalService.queryAbContractDetail(id));
    }

    /**
     * 审批异常合同
     */
    @Operation(summary = "审批异常合同")
    @PostMapping("/{id}/approval")
    public ResultTemplate<Boolean> approvalAbContract(@PathVariable("id") Integer id, @Validated @RequestBody AbContractApprovalParam abContractApprovalParam) {
        return ResultTemplate.success(abContractApprovalService.approvalAbContract(id, abContractApprovalParam));
    }
}
