package com.coocaa.ad.cheese.cms.venue.rpc;

import com.coocaa.ad.cheese.cms.common.tools.common.result.ResultTemplate;
import com.coocaa.ad.cheese.cms.venue.vo.kanban.SspPointStatusDto;
import com.coocaa.ad.common.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * SSP点位信息
 */
@FeignClient(value = "cheese-ssp-api", configuration = FeignConfig.class)
public interface FeignSspRpc {

    /**
     * 根据点位编码获取点位详情
     *
     * @return 区粒度的点位状态统计
     */
    @GetMapping("/ssp/point/countPointStatus")
    ResultTemplate<List<SspPointStatusDto>> countPointStatus();


    /**
     * 根据点位编码获取点位详情
     *
     * @return 区粒度的点位状态统计
     */
    @PostMapping("/ssp/point/point-count/by-point-code")
    ResultTemplate<Integer> countPointStatusByPointCode(List<String> codes);
}
