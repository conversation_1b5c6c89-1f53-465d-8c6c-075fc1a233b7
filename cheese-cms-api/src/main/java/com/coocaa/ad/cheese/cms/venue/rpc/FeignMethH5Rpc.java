package com.coocaa.ad.cheese.cms.venue.rpc;

import com.alibaba.nacos.api.model.v2.Result;
import com.coocaa.ad.cheese.cms.common.tools.common.result.ResultTemplate;
import com.coocaa.ad.cheese.cms.venue.bean.building.CmsBusinessReq;
import com.coocaa.ad.cheese.cms.venue.bean.building.CmsPriceApplyReq;
import com.coocaa.ad.cheese.cms.venue.rpc.vo.BuildingVO;
import com.coocaa.ad.cheese.cms.venue.rpc.vo.PriceApplyDevicePointJoinVO;
import com.coocaa.ad.cheese.cms.venue.rpc.vo.VerifyRatingDTO;
import com.coocaa.ad.cheese.cms.venue.vo.building.BuildingGeneVO;
import com.coocaa.ad.cheese.cms.venue.vo.building.CmsResult;
import com.coocaa.ad.cheese.cms.venue.vo.building.PointDetail;
import com.coocaa.ad.common.config.FeignConfig;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * Meth H5 端提供的RPC接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-16
 */
@FeignClient(value = "meht", configuration = FeignConfig.class)
public interface FeignMethH5Rpc {
    /**
     * 查询当前登陆用户的商机列表
     *
     * @param crmReq 请求参数
     * @return 商机列表
     */
    @PostMapping("/crm/business/cms/list")
    String pageListBusinesses(@RequestBody CmsBusinessReq crmReq);


    /**
     * 查询当前登陆用户的价格申请列表
     *
     * @param crmReq 请求参数
     * @return 价格申请列表
     */
    @PostMapping("/price/apply/cms")
    String pageListPriceApplies(@RequestBody CmsPriceApplyReq crmReq);

    /**
     * 计算价格申请的总价
     *
     * @param applyCodes 价格申请编码
     * @return 总价
     */
    @PostMapping("/price/apply/amount")
    CmsResult<BigDecimal> calculateTotalAmount(@RequestBody Collection<String> applyCodes);

    /**
     * 楼宇top等级值
     *
     * @param projectCode 项目编码
     */
    @GetMapping("/buildRating/top-level")
    CmsResult<String> topLevel(@RequestParam(name = "projectCode") String projectCode);


    @GetMapping("/price/apply/business/points")
    CmsResult<List<PointDetail>> pointByBusinessCode(@RequestParam(value = "businessCode") String businessCode);

    /**
     * 商机下ssp点位
     */
    @GetMapping("/price/apply/business/ssp/points")
    CmsResult<List<PointDetail>> sspPointByBusinessCode(@RequestParam(value = "businessCode") String businessCode);

    /**
     * 根据楼宇编码查询基因信息
     */
    @PostMapping("/building-gene/by-no")
    Result<List<BuildingGeneVO>> buildingGeneByNo(@RequestBody @NotEmpty(message = "楼宇编码列表不能为空") List<String> buildingRatingNos);

    @GetMapping("/buildRating/detail/{buildingNo}")
    CmsResult<BuildingVO> getBuildingLocation(@PathVariable String buildingNo);

    /**
     * 根据点位code获取设备激励金数据
     */
    @PostMapping("/price/apply/device/incentive-price")
    Result<List<PriceApplyDevicePointJoinVO>> getIncentivePrice(@RequestBody @NotEmpty(message = "点位code列表不能为空") Collection<String> pointCodes);

    /**
     * "校验商机是否完成评级 [0 数据异常  1成功 2未完成大屏 3未完成小屏]
     */
    @PostMapping("/buildRating/verify")
    Result<Integer> isFinishRating(@RequestBody @Validated List<VerifyRatingDTO> dto);

    /**
     * 查询商机是否被指定用户负责
     */
    @PostMapping("/high-sea-customer/responsibility/check/{userWno}")
    ResultTemplate<List<String>> responsibilityCheck(@PathVariable String userWno, @RequestBody List<String> businessCodes);

}
