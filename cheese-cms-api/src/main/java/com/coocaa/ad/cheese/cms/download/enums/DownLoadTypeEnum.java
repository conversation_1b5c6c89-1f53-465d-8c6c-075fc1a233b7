package com.coocaa.ad.cheese.cms.download.enums;

import com.coocaa.ad.downloader.enums.IDownloaderType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 下载类型
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-28
 */
@Getter
@AllArgsConstructor
public enum DownLoadTypeEnum implements IDownloaderType {

    YXHT_LB("yxht_lb", "有效合同-按列表数据导出"),
    YXHT_XM_FK("yxht_xm_fk", "有效合同-按项目+付款维度导出"),
    YXHT_LB_HIS("yxht_lb_his", "历史有效合同-按列表数据导出"),
    YXHT_XM_FK_HIS("yxht_xm_fk_his", "历史有效合同-按项目+付款维度导出"),
    HTSQD_LB("htsqd_lb", "合同申请单-按列表数据导出"),
    HTSQD_XM_FK("htsqd_xm_fk", "合同申请单-按项目+付款维度导出"),
    BGHT_SQD_LB("bght_sqd_lb", "变更合同申请单-按列表数据导出"),
    BGHT_SQD_XM_FK("bght_sqd_xm_fk", "变更合同申请单-按项目+付款维度导出"),
    YCHT("ycht", "异常合同导出"),
    YFTZ("yftz", "应付台账导出"),
    YFTZ_HIS("yftz_his", "应付台账-历史导出"),
    SSSJ("sssj", "实时数据导出"),
    HZSJ("HZSJ", "合作数据导出"),
    BCXY_LB("bcxy_lb", "补充协议-按列表数据导出"),
    BCXY_XM_FK("bcxy_xm_fk", "补充协议-按项目+付款维度导出");

    public final static String SYS_TYPE = "0155-2";
    private final static Map<String, DownLoadTypeEnum> BY_CODE_MAP =
            Arrays.stream(DownLoadTypeEnum.values())
                    .collect(Collectors.toMap(DownLoadTypeEnum::getCode, item -> item));
    private final String code;
    private final String desc;

    /**
     * 将代码转成枚举
     */
    public static DownLoadTypeEnum parse(String code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     *
     * @param code         代码
     * @param defaultValue 默认值
     * @return 对应的枚举值，如果没有找到则返回默认值
     */
    public static DownLoadTypeEnum parse(String code, DownLoadTypeEnum defaultValue) {
        return BY_CODE_MAP.getOrDefault(code, defaultValue);
    }

    /**
     * 根据代码获取描述
     *
     * @param code 代码
     * @return 对应的描述，如果没有找到则返回空字符串
     */
    public static String getDesc(String code) {
        return Optional.ofNullable(parse(code)).map(DownLoadTypeEnum::getDesc).orElse(StringUtils.EMPTY);
    }
}