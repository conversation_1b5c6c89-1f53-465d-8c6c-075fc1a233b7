package com.coocaa.ad.cheese.cms.venue.validation.contract;

import com.coocaa.ad.cheese.cms.common.config.annotation.ProjectsLevelNotBlank;
import com.coocaa.ad.cheese.cms.common.tools.common.enums.BooleFlagEnum;
import com.coocaa.ad.cheese.cms.common.tools.common.util.StringUtils;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractAgreementAddParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractApplyParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractProjectParam;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Objects;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/29
 */
@Slf4j
public class ProjectsLevelValidator implements ConstraintValidator<ProjectsLevelNotBlank, ContractApplyParam> {
    @Override
    public boolean isValid(ContractApplyParam contractApplyParam, ConstraintValidatorContext context) {
        if (Objects.isNull(contractApplyParam)) {
            return true;
        }

        if (contractApplyParam instanceof ContractAgreementAddParam contractAgreementAddParam) {
            if (Objects.nonNull(contractAgreementAddParam.getStopFlag()) &&
                    contractAgreementAddParam.getStopFlag().intValue() == BooleFlagEnum.YES.getCode()) {
                return true;
            }
        }

        if (CollectionUtils.isEmpty(contractApplyParam.getProjects())) {
            return true;
        }

        StringJoiner errorMsg = new StringJoiner(";");
        for (ContractProjectParam project : contractApplyParam.getProjects()) {
            if (Objects.isNull(project)) {
                log.warn("项目为空");
                continue;
            }
            if (StringUtils.isBlank(project.getLevel())) {
                log.warn("{} 项目人工评级为空", project.getProjectName());
                // 动态构建错误信息
                errorMsg.add("[" + project.getProjectName() + "] 项目尚未进行人工评级，请前往移动端客户管理-完善评级");
            }

        }
        if (errorMsg.length() > 0) {
            context.disableDefaultConstraintViolation();
            context.buildConstraintViolationWithTemplate(errorMsg.toString())
                    .addConstraintViolation();
            return false;
        }
        return true;
    }
}
