package com.coocaa.ad.cheese.cms.venue.convert.contract;

import com.coocaa.ad.cheese.cms.common.convert.PageableConvert;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractAmendmentChangeTypeEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractAmendmentTerminationEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractAttachmentEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractDeviceEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractDevicePointEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractLogEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractPaymentPeriodEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractPriceApplyEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractPricePeriodEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractProjectEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractSnapshotEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractSubEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractSupplierEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.LedgerEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.SupplierBankEntity;
import com.coocaa.ad.cheese.cms.common.tools.venue.bean.ContractPageDTO;
import com.coocaa.ad.cheese.cms.common.tools.venue.bean.ContractQueryDTO;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractAgreementAddParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractAmendmentApplyParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractAmendmentTerminationParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractApplyParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractAttachmentParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractDeviceParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractDevicePointParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractEditParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractPageWithPayPeriodDTO;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractPageWithoutPayPeriodDTO;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractPaymentPeriodPaidDTO;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractPaymentPeriodParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractPriceApplyParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractPricePeriodParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractProjectDepositPaidDTO;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractProjectParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractQueryParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractSupplierAndBankDTO;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractSupplierParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.SubContractParam;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ApplyContractExportVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractAgreementExportVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractAmendmentChangeTypeVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractAmendmentExportVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractAmendmentTerminationVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractApplyExportVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractAttachmentVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractDetailSimplifyVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractDeviceDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractDevicePointDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractEditDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractEntityConvertDTO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractExportVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractLogVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractPageVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractPaymentPeriodDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractPriceApplyDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractPricePeriodDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractProjectDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractProjectEntityConvertDTO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractSnapshotRecordVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractSubEntityConvertDTO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractSupplierBankDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractSupplierDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ProjectExportByAgreementVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ProjectExportByAmendmentVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ProjectExportByApplyVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ProjectExportVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.SubContractDetailVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.control.DeepClone;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-04
 */
@Mapper(componentModel = "spring", mappingControl = DeepClone.class)
public interface ContractConvert extends PageableConvert<ContractEntity, ContractDetailVO> {
    String SIGN_PARTY_NAME = "广东创视科技广告有限公司";
    String CONTACT_PERSON = "王有纲";
    String CONTACT_PHONE = "0755-27357001";
    String CONTACT_EMAIL = "<EMAIL>";
    String CONTACT_ADDRESS = "深圳市宝安区石岩街道塘头一号创维创新谷2号楼B栋10楼创视广告";

    Set<LocalDateTime> INVALID_DATETIME = Set.of(
            LocalDateTime.of(1900, 1, 1, 0, 0, 0),
            LocalDateTime.of(1000, 1, 1, 0, 0, 0)
    );

    @Named("replaceEpochWithNull")
    default LocalDateTime replaceEpochWithNull(LocalDateTime dateTime) {
        if (dateTime != null && INVALID_DATETIME.contains(dateTime)) {
            return null;
        }
        return dateTime;
    }

    /**
     * 转换成查询参数
     */
    ContractQueryDTO toQueryDto(ContractQueryParam param);


    /**
     * VO 转 Entity
     */
    @Mapping(target = "cooperateType", constant = "1")
    @Mapping(target = "signPartyId", constant = "1")
    @Mapping(target = "signPartyName", constant = SIGN_PARTY_NAME)
    @Mapping(target = "contactPerson", constant = CONTACT_PERSON)
    @Mapping(target = "contactPhone", constant = CONTACT_PHONE)
    @Mapping(target = "contactEmail", constant = CONTACT_EMAIL)
    @Mapping(target = "contactAddress", constant = CONTACT_ADDRESS)
    ContractEntity toEntity(ContractApplyParam param);

    /**
     * VO 转 Entity
     */
    @Mapping(target = "cooperateType", constant = "1")
    @Mapping(target = "signPartyId", constant = "1")
    @Mapping(target = "signPartyName", constant = SIGN_PARTY_NAME)
    @Mapping(target = "contactPerson", constant = CONTACT_PERSON)
    @Mapping(target = "contactPhone", constant = CONTACT_PHONE)
    @Mapping(target = "contactEmail", constant = CONTACT_EMAIL)
    @Mapping(target = "contactAddress", constant = CONTACT_ADDRESS)
    @Mapping(target = "effectFlag", constant = "0")
    @Mapping(target = "contractType", constant = "4")
    ContractEntity toEntity(ContractAgreementAddParam param);

    /**
     * VO 转 Entity
     */
    @Mapping(target = "cooperateType", constant = "1")
    @Mapping(target = "signPartyId", constant = "0")
    @Mapping(target = "signPartyName", constant = SIGN_PARTY_NAME)
    @Mapping(target = "contactPerson", constant = CONTACT_PERSON)
    @Mapping(target = "contactPhone", constant = CONTACT_PHONE)
    @Mapping(target = "contactEmail", constant = CONTACT_EMAIL)
    @Mapping(target = "contactAddress", constant = CONTACT_ADDRESS)
    @Mapping(target = "effectFlag", constant = "0")
    @Mapping(target = "contractType", constant = "5")
    ContractEntity toEntity(ContractAmendmentApplyParam param);


    @Mapping(target = "id", ignore = true)
    ContractEntity toEntity(ContractEditParam param, @MappingTarget ContractEntity contractEntity);

    /**
     * 供应商转换成实体
     */
    ContractSupplierEntity toEntity(ContractSupplierParam param);

    /**
     * 项目转换成实体
     */
    @Mapping(target = "depositFlag", source = "depositFlag", defaultValue = "0")
    ContractProjectEntity toEntity(ContractProjectParam param);

    /**
     * 价格申请转换成实体
     */
    @Mapping(target = "deviceCount", source = "count")
    ContractPriceApplyEntity toEntity(ContractPriceApplyParam param);

    /**
     * 设备转换成实体
     */
    @Mapping(target = "signCount", source = "count", defaultValue = "0")
    ContractDeviceEntity toEntity(ContractDeviceParam param);

    /**
     * 终端点位转换成实体
     */
    ContractDevicePointEntity toEntity(ContractDevicePointParam param);

    /**
     * 单价周期转换成实体
     */
    ContractPricePeriodEntity toEntity(ContractPricePeriodParam param);

    /**
     * 付款周期转换成实体
     */
    ContractPaymentPeriodEntity toEntity(ContractPaymentPeriodParam param);

    /**
     * 子合同转换成实体
     */
    ContractSubEntity toEntity(SubContractParam param);

    /**
     * 附件转换成实体
     */
    ContractAttachmentEntity toEntity(ContractAttachmentParam param);


    // ===================================== 转VO =====================================

    /**
     * 列表行转VO
     */
    @Mapping(target = "partyAStampStatus", source = "sealParty1Flag")
    @Mapping(target = "partyBStampStatus", source = "sealParty2Flag")
    @Mapping(target = "partyCStampStatus", source = "sealParty3Flag")
    @Mapping(target = "stampOrderName", expression = "java(com.coocaa.ad.cheese.cms.common.tools.venue.enums.StampOrderEnum.getDesc(dto.getStampOrder()))")
    @Mapping(target = "changeFlagName", expression = "java(com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractChangeFlagEnum.getDesc(dto.getChangeFlag()))")
    ContractPageVO toVO(ContractPageDTO dto);

    /**
     * 附件转VO
     */
    ContractAttachmentVO toVO(ContractAttachmentEntity entity);

    /**
     * 变更类型转VO
     */
    ContractAmendmentChangeTypeVO toVO(ContractAmendmentChangeTypeEntity entity);

    /**
     * 操作日志转VO
     */
    ContractLogVO toVO(ContractLogEntity entity);

    /**
     * 合同快照转记录VO
     */
    ContractSnapshotRecordVO toVO(ContractSnapshotEntity entity);

    /**
     * 导出Excel列表
     */
    @Mapping(target = "partyAStampStatus", source = "sealParty1Flag")
    @Mapping(target = "partyBStampStatus", source = "sealParty2Flag")
    @Mapping(target = "partyCStampStatus", source = "sealParty3Flag")
    ContractExportVO toExportVO(ContractPageDTO dto);

    @Mapping(target = "applyTime", source = "applyTime", qualifiedByName = "replaceEpochWithNull")
    ContractAmendmentExportVO toAmendmentExportVO(ContractPageDTO dto);

    ContractApplyExportVO toApplyExportVO(ContractPageDTO dto);

    ApplyContractExportVO toApplyContractExportVO(ContractApplyExportVO dto);


    /**
     * 转换为展示详情
     */
    @Mapping(target = "cooperateTypeName", expression = "java(com.coocaa.ad.cheese.cms.common.tools.venue.enums.CooperateTypeEnum.getDesc(entity.getCooperateType()))")
    @Mapping(target = "businessTypeName", expression = "java(com.coocaa.ad.cheese.cms.common.tools.venue.enums.BusinessTypeEnum.getDesc(entity.getBusinessType()))")
    @Mapping(target = "contractTypeName", expression = "java(com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractTypeEnum.getDesc(entity.getContractType()))")
    @Mapping(target = "claimName", expression = "java(com.coocaa.ad.cheese.cms.common.tools.venue.enums.ClaimEnum.getDesc(entity.getClaim()))")
    @Mapping(target = "stampOrderName", expression = "java(com.coocaa.ad.cheese.cms.common.tools.venue.enums.StampOrderEnum.getDesc(entity.getStampOrder()))")
    ContractDetailVO toDetailVO(ContractEntity entity);

    @Mapping(target = "accountCode", source = "accountNo")
    ContractSupplierBankDetailVO toDetailVO(SupplierBankEntity entity);

    ContractDetailSimplifyVO toDetailSimplifyVO(ContractEntity entity);

    /**
     * 转换为编辑详情
     */
    ContractEditDetailVO toEditDetailVO(ContractEntity contractEntity);

    /**
     * 转换供应商为编辑详情
     */
    ContractSupplierDetailVO toEditDetailVO(ContractSupplierEntity entity);


    /**
     * 转换项目为编辑详情
     */
    ContractProjectDetailVO toEditDetailVO(ContractProjectEntity entity);

    /**
     * 转换子合同为编辑详情
     */
    SubContractDetailVO toEditDetailVO(ContractSubEntity entity);

    /**
     * 转换价格申请为编辑详情
     */
    @Mapping(source = "deviceCount", target = "count")
    ContractPriceApplyDetailVO toEditDetailVO(ContractPriceApplyEntity entity);

    /**
     * 转换终端为编辑详情
     */
    @Mapping(source = "signCount", target = "count", defaultValue = "0")
    ContractDeviceDetailVO toEditDetailVO(ContractDeviceEntity entity);

    /**
     * 转换点位为编辑详情
     */
    ContractDevicePointDetailVO toEditDetailVO(ContractDevicePointEntity entity);

    /**
     * 转换单价周期为编辑详情
     */
    ContractPricePeriodDetailVO toEditDetailVO(ContractPricePeriodEntity entity);

    /**
     * 转换付款周期为编辑详情
     */
    ContractPaymentPeriodDetailVO toEditDetailVO(ContractPaymentPeriodEntity entity);

    /**
     * 转换为复制详情
     */
    @Mapping(target = "cooperateTypeName", expression = "java(com.coocaa.ad.cheese.cms.common.tools.venue.enums.CooperateTypeEnum.getDesc(entity.getCooperateType()))")
    @Mapping(target = "businessTypeName", expression = "java(com.coocaa.ad.cheese.cms.common.tools.venue.enums.BusinessTypeEnum.getDesc(entity.getBusinessType()))")
    ContractEntityConvertDTO toCloneEntityDTO(ContractEntity entity);

    /**
     * 转换项目为复制详情
     */
    ContractProjectEntityConvertDTO toCloneEntityDTO(ContractProjectEntity entity);

    /**
     * 转换子合同为复制详情
     */
    ContractSubEntityConvertDTO toCloneEntityDTO(ContractSubEntity entity);

    /**
     * 导出Excel列表包含付款周期
     */
    @Mapping(target = "partyAStampStatus", source = "sealParty1Flag")
    @Mapping(target = "partyBStampStatus", source = "sealParty2Flag")
    @Mapping(target = "partyCStampStatus", source = "sealParty3Flag")
    ProjectExportVO toProjectExportVO(ContractPageWithPayPeriodDTO dto);

    /**
     * 转换为合同终止信息详情
     */
    @Mapping(target = "terminationTypeName", expression = "java(com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractTerminationTypeEnum.getDesc(entity.getTerminationType()))")
    ContractAmendmentTerminationVO toDetailVO(ContractAmendmentTerminationEntity entity);

    ContractPageWithPayPeriodDTO toContractPageWithPayPeriodDTO(LedgerEntity ledgerInfo);

    void toContractPageWithPayPeriodDTOCopy(ContractPageWithoutPayPeriodDTO contractPageWithoutPayPeriodDTO, @MappingTarget ContractPageWithPayPeriodDTO contractPageWithPayPeriodDTO);

    void toContractPageWithPayPeriodDTOBySupplierAndBank(ContractSupplierAndBankDTO contractSupplierAndBankDto, @MappingTarget ContractPageWithPayPeriodDTO contractPageWithPayPeriodDTO);

    ContractProjectDepositPaidDTO toContractProjectDepositPaidDTO(ContractProjectParam contractProjectParam);

    ContractPaymentPeriodPaidDTO toContractPaymentPeriodPaidDTO(ContractPaymentPeriodParam contractPaymentPeriodParam);

    ContractAmendmentTerminationEntity toEntity(ContractAmendmentTerminationParam terminationParam);

    ContractAgreementExportVO toContractAgreementExportVO(ContractApplyExportVO contractApplyExportVO);

    ProjectExportByAmendmentVO toProjectExportByAmendmentVO(ProjectExportVO projectExportVO);

    ProjectExportByAgreementVO toProjectExportByAgreementVO(ProjectExportVO projectExportVO);

    ProjectExportByApplyVO toProjectExportByApplyVO(ProjectExportVO projectExportVO);
}
