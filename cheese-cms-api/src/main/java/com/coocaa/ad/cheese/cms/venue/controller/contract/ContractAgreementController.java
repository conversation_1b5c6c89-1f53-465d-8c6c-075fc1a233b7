package com.coocaa.ad.cheese.cms.venue.controller.contract;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.nacos.common.utils.NumberUtils;
import com.coocaa.ad.cheese.cms.common.tools.common.exception.CommonException;
import com.coocaa.ad.cheese.cms.common.tools.common.result.PageRequestVo;
import com.coocaa.ad.cheese.cms.common.tools.common.result.PageResponseVo;
import com.coocaa.ad.cheese.cms.common.tools.common.result.ResultTemplate;
import com.coocaa.ad.cheese.cms.common.tools.common.util.AesUtils;
import com.coocaa.ad.cheese.cms.common.tools.common.util.StringUtils;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractTypeEnum;
import com.coocaa.ad.cheese.cms.common.validation.ValidationGroup;
import com.coocaa.ad.cheese.cms.download.service.SupplementaryAgreementProjectPayService;
import com.coocaa.ad.cheese.cms.download.service.SupplementaryAgreementService;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractAgreementAddParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractAttachmentParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractCancelParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractQueryParam;
import com.coocaa.ad.cheese.cms.venue.controller.BaseController;
import com.coocaa.ad.cheese.cms.venue.controller.contract.helper.ContractValidateHelper;
import com.coocaa.ad.cheese.cms.venue.service.ContractAttachmentService;
import com.coocaa.ad.cheese.cms.venue.service.ContractExportService;
import com.coocaa.ad.cheese.cms.venue.service.ContractReadService;
import com.coocaa.ad.cheese.cms.venue.service.ContractWriteService;
import com.coocaa.ad.cheese.cms.venue.validation.contract.ApplyValidatorFactory;
import com.coocaa.ad.cheese.cms.venue.validation.contract.BusinessAvailableValidator;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractDetailDiffVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractDevicePointDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractEditDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractPageVO;
import com.coocaa.ad.common.anno.AuthIgnore;
import com.coocaa.ad.downloader.bean.dto.TaskDTO;
import com.google.common.collect.Sets;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;
import java.util.concurrent.TimeUnit;

/**
 * 补充协议管理
 *
 * <AUTHOR>
 * @since 2025-02-10
 */
@Slf4j
@RestController
@RequestMapping("/venue/contract/agreement")
@Tag(name = "补充协议管理", description = "补充协议管理")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ContractAgreementController extends BaseController {
    private final Validator validator;
    private final StringRedisTemplate stringRedisTemplate;
    private final ApplyValidatorFactory validatorFactory;
    private final BusinessAvailableValidator businessAvailableValidator;
    private final ContractReadService contractReadService;
    private final ContractWriteService contractWriteService;
    private final ContractExportService contractExportService;
    private final ContractAttachmentService attachmentService;
    private final ContractValidateHelper contractValidateHelper;
    private final SupplementaryAgreementProjectPayService supplementaryAgreementProjectPayService;
    private final SupplementaryAgreementService supplementaryAgreementService;

    private static final String AGREEMENT_NAME_ZH = "补充协议";

    // 合同类型，补充协议
    private static final List<Integer> CONTRACT_TYPES = List.of(ContractTypeEnum.AGREEMENT.getCode());

    /**
     * 补充协议列表(分页)
     */
    @Operation(summary = "补充协议列表(分页)")
    @PostMapping("/list")
    public ResultTemplate<PageResponseVo<ContractPageVO>> pageListAgreements(@RequestBody PageRequestVo<ContractQueryParam> pageRequest) {
        pageRequest.setQuery(Optional.ofNullable(pageRequest.getQuery()).orElseGet(ContractQueryParam::new));
        pageRequest.getQuery().setContractTypes(CONTRACT_TYPES);
        return ResultTemplate.success(contractReadService.pageListSupplements(pageRequest));
    }

    /**
     * 补充协议导出
     */
    @Operation(summary = "补充协议导出")
    @PostMapping("/export")
    public ResultTemplate<Long> exportAgreements(@RequestBody ContractQueryParam queryParam) {
        queryParam.setContractTypes(CONTRACT_TYPES);
        TaskDTO taskDTO = new TaskDTO().setExecuteParams(JSON.toJSONString(queryParam));
        return ResultTemplate.success(this.supplementaryAgreementService.downloader(taskDTO));
    }

    @Operation(summary = "补充协议导出-项目+付款周期维度")
    @PostMapping("/project-pay/export")
    public ResultTemplate<Long> exportAgreementsWithPayPeriod(@RequestBody ContractQueryParam queryParam) {
        queryParam.setContractTypes(CONTRACT_TYPES);
        TaskDTO taskDTO = new TaskDTO().setExecuteParams(JSON.toJSONString(queryParam));
        return ResultTemplate.success(this.supplementaryAgreementProjectPayService.downloader(taskDTO));
    }

    /**
     * 补充协议详情
     */
    @Operation(summary = "获取补充协议详情")
    @Parameter(name = "id", description = "补充协议ID", required = true, example = "1")
    @GetMapping("/{id}")
    public ResultTemplate<ContractDetailVO> getSupplementDetail(@PathVariable(name = "id") Integer id) {
        return ResultTemplate.success(contractReadService.getSupplementDetail(id));
    }

    /**
     * 补充协议H5详情
     */
    @AuthIgnore
    @Operation(summary = "获取补充协议H5详情")
    @Parameter(name = "id", description = "补充协议ID", required = true, example = "1")
    @GetMapping("/detail")
    public ResultTemplate<ContractDetailDiffVO> getSupplementDetailWithoutAuth(@RequestParam(name = "id") String encryptedId) {
        String plainText = AesUtils.decryptStr(encryptedId);
        Integer id = StringUtils.isBlank(plainText) ? null : NumberUtils.toInt(plainText);
        if (id == null || id <= 0) throw new CommonException("补充协议ID不正确");
        return ResultTemplate.success(contractReadService.contractDetailDiffWrapper(contractReadService.getSupplementDetail(id, true)));
    }

    /**
     * 补充协议申请单详情
     */
    @Operation(summary = "获取补充协议申请单详情")
    @Parameter(name = "id", description = "补充协议ID", required = true, example = "1")
    @GetMapping("/apply/{id}")
    public ResultTemplate<ContractEditDetailVO> getSupplementApplyDetail(
            @PathVariable(name = "id") Integer id,
            @RequestParam(name = "force", required = false, defaultValue = "false") boolean force) {
        return ResultTemplate.success(contractReadService.getSupplementApplyDetail(id, force));
    }

    @Operation(summary = "新增补充协议")
    @Parameter(name = "Token", description = "Token", in = ParameterIn.HEADER, required = true)
    @Parameter(name = "submit", description = "暂存还是提交", in = ParameterIn.QUERY)
    @PostMapping
    public ResultTemplate<Integer> create(@RequestParam(name = "submit", required = false, defaultValue = "false") boolean submit,
                                          @RequestBody ContractAgreementAddParam contractAgreementAddParam) {
        if (Objects.nonNull(contractAgreementAddParam.getId()) && contractAgreementAddParam.getId() > 0) {
            log.info("新增补充协议id不正确---{}", contractAgreementAddParam);
            return ResultTemplate.fail("id不正确");
        }
        // 特殊处理合同ID
        if (Objects.nonNull(contractAgreementAddParam.getId())) {
            contractAgreementAddParam.setId(null);
        }

        String validateResult = paramCheck(contractAgreementAddParam, submit);
        if (StringUtils.isNotBlank(validateResult)) {
            log.info("新增补充协议参数校验不通过---{},{}", contractAgreementAddParam, validateResult);
            return ResultTemplate.fail(validateResult);
        }

        // 供应商有效性验证
        String supplierCheckRet = contractValidateHelper.doValidateForSupplier(contractAgreementAddParam);
        if (StringUtils.isNotBlank(supplierCheckRet)) {
            return ResultTemplate.fail(supplierCheckRet);
        }

        String redisKey = String.format("contract:agreement:%s", contractAgreementAddParam.getParentId());
        log.info("新增补充协议 redisKey---{}", redisKey);
        try {
            Boolean locked = stringRedisTemplate.opsForValue().setIfAbsent(redisKey, "1", 2, TimeUnit.SECONDS);
            if (BooleanUtil.isTrue(locked)) {
                log.info("新增补充协议 redisKey 加锁成功---{},有效期2s", redisKey);
                if (contractReadService.canCreate(contractAgreementAddParam.getParentId(), null)) {
                    log.info("新增补充协议 redisKey 加锁成功---{},有效期2s", redisKey);
                    Integer id = contractWriteService.createOrUpdateContractAgreement(submit, contractAgreementAddParam);
                    return ResultTemplate.success(id);
                } else {
                    log.info("新增补充协议 存在在途补充协议---{}", contractAgreementAddParam.getParentId());
                    return ResultTemplate.fail("存在在途补充协议");
                }
            } else {
                log.info("新增补充协议 redisKey {} 加锁失败,重复提交", redisKey);
                return ResultTemplate.fail("请勿重复提交");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            stringRedisTemplate.delete(redisKey);
            return ResultTemplate.fail("操作失败");
        }
    }

    @Operation(summary = "编辑补充协议")
    @Parameter(name = "Token", description = "Token", in = ParameterIn.HEADER, required = true)
    @Parameter(name = "submit", description = "暂存还是提交", in = ParameterIn.QUERY)
    @PutMapping
    public ResultTemplate<Integer> edit(@RequestParam(name = "submit", required = false, defaultValue = "false") boolean submit,
                                        @RequestBody ContractAgreementAddParam contractAgreementAddParam) {
        // 特殊处理合同ID
        if (Objects.isNull(contractAgreementAddParam.getId()) || contractAgreementAddParam.getId() <= 0) {
            log.info("编辑补充协议 id不正确---{}", contractAgreementAddParam);
            return ResultTemplate.fail("id不正确");
        }

        String validateResult = paramCheck(contractAgreementAddParam, submit);
        if (StringUtils.isNotBlank(validateResult)) {
            log.info("编辑补充协议 参数校验不通过---{},{}", contractAgreementAddParam, validateResult);
            return ResultTemplate.fail(validateResult);
        }

        // 供应商有效性验证
        String supplierCheckRet = contractValidateHelper.doValidateForSupplier(contractAgreementAddParam);
        if (StringUtils.isNotBlank(supplierCheckRet)) {
            return ResultTemplate.fail(supplierCheckRet);
        }

        String redisKey = String.format("contract:agreement:%s", contractAgreementAddParam.getParentId());
        log.info("编辑补充协议 redisKey---{}", redisKey);
        try {
            Boolean locked = stringRedisTemplate.opsForValue().setIfAbsent(redisKey, "1", 2, TimeUnit.SECONDS);
            if (BooleanUtil.isTrue(locked)) {
                log.info("编辑补充协议 redisKey 加锁成功---{},有效期2s", redisKey);
                if (contractReadService.canCreate(contractAgreementAddParam.getParentId(), contractAgreementAddParam.getId())) {
                    Integer id = contractWriteService.createOrUpdateContractAgreement(submit, contractAgreementAddParam);
                    return ResultTemplate.success(id);
                } else {
                    log.info("编辑补充协议 存在在途补充协议---{}", contractAgreementAddParam.getParentId());
                    return ResultTemplate.fail("存在在途补充协议");
                }
            } else {
                log.info("编辑补充协议 redisKey {} 加锁失败,重复提交", redisKey);
                return ResultTemplate.fail("请勿重复提交");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            stringRedisTemplate.delete(redisKey);
            return ResultTemplate.fail("操作失败");
        }
    }

    @Operation(summary = "查询是否可以发起补充协议")
    @Parameter(name = "Token", description = "Token", in = ParameterIn.HEADER, required = true)
    @GetMapping("{contractId}/can-create")
    public ResultTemplate<Boolean> canCreate(@PathVariable("contractId") Integer contractId) {
        boolean canCreate = contractReadService.canCreate(contractId, null);
        return ResultTemplate.success(canCreate);
    }

    @Operation(summary = "删除草稿")
    @Parameter(name = "Token", description = "Token", in = ParameterIn.HEADER, required = true)
    @DeleteMapping("{agreementId}")
    public ResultTemplate<Boolean> deleteContractAgreement(@PathVariable("agreementId") Integer agreementId) {
        try {
            contractWriteService.deleteContractAgreementById(agreementId);
            return ResultTemplate.success(true);
        } catch (Exception e) {
            log.error("删除补充协议异常:{}", agreementId, e);
            return ResultTemplate.fail(e.getMessage());
        }

    }

    /**
     * 检验参数
     *
     * @param contractAgreementAddParam
     * @param submit
     * @return
     */
    private String paramCheck(ContractAgreementAddParam contractAgreementAddParam, Boolean submit) {
        if (ObjectUtil.isAllNotEmpty(contractAgreementAddParam, submit) && submit) {
            List<String> validateResults;
            StringJoiner errorMessage = new StringJoiner(", ");
            Set<ConstraintViolation<ContractAgreementAddParam>> violations = validator.validate(contractAgreementAddParam, ValidationGroup.ContractAgreement.class);
            if (!violations.isEmpty()) {
                for (ConstraintViolation<ContractAgreementAddParam> violation : violations) {
                    errorMessage.add(violation.getMessage());
                }
            }

            // 如果有错误信息，则返回错误信息
            if (errorMessage.length() > 0) {
                return errorMessage.toString();
            }

            // 参数检查结果
            validateResults = validatorFactory.validate(contractAgreementAddParam, submit, true,
                    Sets.newHashSet(businessAvailableValidator));
            if (CollectionUtil.isNotEmpty(validateResults)) {
                log.error("补充协议参数校验失败: \n{}", String.join("\n", validateResults));
                return String.join(", ", validateResults);
            }
        }
        return null;
    }

    @Operation(summary = "查询项目点位")
    @Parameter(name = "Token", description = "Token", in = ParameterIn.HEADER, required = true)
    @GetMapping("{parentId}/{projectName}/points")
    public ResultTemplate<List<ContractDevicePointDetailVO>> getProjectPoints(@PathVariable("parentId") Integer parentId, @PathVariable("projectName") String projectName) {
        List<ContractDevicePointDetailVO> contractDevicePointDetailVOS = contractReadService.listProjectPoints(parentId, projectName);
        return ResultTemplate.success(contractDevicePointDetailVOS);
    }

    /**
     * 上传补充协议附件
     */
    @Operation(summary = "上传补充协议附件")
    @Parameter(name = "id", description = "补充协议ID", required = true, in = ParameterIn.QUERY, example = "1")
    @PostMapping("/attachments")
    public ResultTemplate<Boolean> uploadAttachment(@RequestParam(name = "id") Integer contractId,
                                                    @RequestBody @Validated List<ContractAttachmentParam> attachments) {
        if (CollectionUtils.isEmpty(attachments)) {
            return ResultTemplate.fail("附件列表不能为空");
        }

        // 修正附件类型
        attachments.forEach(attachment -> {
            if (StringUtils.isBlank(attachment.getFileType()) || attachment.getFileType().length() > 10) {
                attachment.setFileType(FilenameUtils.getExtension(attachment.getUrl()));
            }
        });

        return ResultTemplate.success(attachmentService.uploadAgreementAttachments(contractId, attachments));
    }

    /**
     * 补充协议作废
     */
    @Operation(summary = "补充协议作废")
    @Parameter(name = "id", description = "协议ID", required = true, example = "1")
    @PutMapping("/{id}/cancel")
    public ResultTemplate<Boolean> cancelAgreement(@PathVariable(name = "id") Integer id,
                                                   @Validated @RequestBody ContractCancelParam archiveParam) {
        return ResultTemplate.success(contractWriteService.cancelAgreement(id, archiveParam));
    }
}
