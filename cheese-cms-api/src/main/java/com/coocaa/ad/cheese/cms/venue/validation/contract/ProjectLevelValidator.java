package com.coocaa.ad.cheese.cms.venue.validation.contract;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.coocaa.ad.cheese.cms.common.config.annotation.ProjectLevelNotBlank;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractProjectParam;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/14
 */
public class ProjectLevelValidator implements ConstraintValidator<ProjectLevelNotBlank, ContractProjectParam> {
    @Override
    public boolean isValid(ContractProjectParam contractProjectParam, ConstraintValidatorContext context) {
        if (StringUtils.isNotBlank(contractProjectParam.getLevel())) {
            return true;
        }
        // 动态构建错误信息
        context.disableDefaultConstraintViolation();
        context.buildConstraintViolationWithTemplate("[" + contractProjectParam.getProjectName() + "] 项目尚未进行人工评级，请前往移动端客户管理-完善评级")
                .addConstraintViolation();
        return false;
    }
}
