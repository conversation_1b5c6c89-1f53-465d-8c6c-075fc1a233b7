package com.coocaa.ad.cheese.cms.venue.vo.contract;

import com.coocaa.ad.translate.anno.TransField;
import com.coocaa.ad.translate.constant.TransTypes;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/3/24
 */
@Data
public class RegionListVO {
    @Schema(description = "大区Id")
    private Integer id;

    @Schema(description = "业务负责人Id")
    @TransField(type = TransTypes.USER)
    private Integer businessHead;

    @Schema(description = "业务负责人名称")
    private String businessHeadName;

    @Schema(description = "法务bp Id")
    @TransField(type = TransTypes.USER)
    private Integer legalBp;

    @Schema(description = "法务bp名称")
    private String legalBpName;

    @Schema(description = "财务bp Id")
    @TransField(type = TransTypes.USER)
    private Integer financeBp;

    @Schema(description = "财务bp名称")
    private String financeBpName;

    @Schema(description = "优先级")
    private Integer priority;

    @Schema(description = "大区名称")
    private String name;

    @Schema(description = "管辖城市名称")
    private List<String> cityNames;
}
