package com.coocaa.ad.cheese.cms.venue.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.CityEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractAbnormalEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractAbnormalProjectEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractAttachmentEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractDevicePointEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractProjectEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractSupplierEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.service.ICityService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractAbnormalDealService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractAbnormalService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractAttachmentService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractDevicePointService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractProjectService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractSupplierService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.supplier.IContractAbnormalProjectService;
import com.coocaa.ad.cheese.cms.common.exception.BusinessException;
import com.coocaa.ad.cheese.cms.common.tools.common.enums.BooleFlagEnum;
import com.coocaa.ad.cheese.cms.common.tools.common.exception.CommonException;
import com.coocaa.ad.cheese.cms.common.tools.common.result.PageRequestVo;
import com.coocaa.ad.cheese.cms.common.tools.common.result.PageResponseVo;
import com.coocaa.ad.cheese.cms.common.tools.common.result.ResultTemplate;
import com.coocaa.ad.cheese.cms.common.tools.common.util.EasyExcelUtils;
import com.coocaa.ad.cheese.cms.common.tools.venue.bean.ContractAbnormalBaseDTO;
import com.coocaa.ad.cheese.cms.common.tools.venue.bean.ContractAbnormalDealUserDTO;
import com.coocaa.ad.cheese.cms.common.tools.venue.bean.ContractAbnormalPageByProjectDTO;
import com.coocaa.ad.cheese.cms.common.tools.venue.bean.ContractAbnormalPageDTO;
import com.coocaa.ad.cheese.cms.common.tools.venue.bean.ContractAbnormalQueryDTO;
import com.coocaa.ad.cheese.cms.common.tools.venue.bean.UserDataAccessV2DTO;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.AbContractDealTypeEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ApplyStatusEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.AttachmentBelongEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.AttachmentTypeEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractAbnormalFlagEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.DealTypeEnum;
import com.coocaa.ad.cheese.cms.common.util.converter.ConverterFactory;
import com.coocaa.ad.cheese.cms.download.enums.DownLoadTypeEnum;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractAbnormalAddParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractAbnormalAttachmentParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractAbnormalExportParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractAbnormalProjectAddParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractAbnormalQueryParam;
import com.coocaa.ad.cheese.cms.venue.convert.contract.ContractAbnormalConvert;
import com.coocaa.ad.cheese.cms.venue.convert.contract.ContractAbnormalDealConvert;
import com.coocaa.ad.cheese.cms.venue.convert.contract.ContractAbnormalProjectConvert;
import com.coocaa.ad.cheese.cms.venue.convert.contract.ContractAttachmentConvert;
import com.coocaa.ad.cheese.cms.venue.convert.contract.ContractConvert;
import com.coocaa.ad.cheese.cms.venue.rpc.FeignSspRpc;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractAbnormalExportByProjectVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractAbnormalExportVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractAbnormalInfoVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractAbnormalInfoWithPointVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractAbnormalProjectBackFillVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractAbnormalProjectVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractAbnormalProjectWithPointVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractAbnormalVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractAttachmentVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractSimpleAbnormalVO;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 异常合同service
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-24
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ContractAbnormalService {

    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    private final StringRedisTemplate stringRedisTemplate;

    private final FeignSspRpc feignSspRpc;

    private final ConverterFactory converterFactory;

    private final IContractAbnormalService contractAbnormalService;
    private final IContractDevicePointService contractDevicePointService;
    private final IContractAbnormalProjectService contractAbnormalProjectService;
    private final IContractAttachmentService attachmentService;
    private final IContractService contractService;
    private final IContractSupplierService contractSupplierService;
    private final IContractProjectService contractProjectService;
    private final IContractAbnormalDealService contractAbnormalDealService;
    private final ICityService cityService;

    private final AbContractApprovalService contractApprovalService;
    private final ContractAbnormalDealService abnormalDealService;

    private final ContractAbnormalConvert contractAbnormalConvert;
    private final ContractAbnormalProjectConvert contractAbnormalProjectConvert;
    private final ContractConvert contractConvert;
    private final ContractAbnormalDealConvert contractAbnormalDealConvert;
    private final ContractAttachmentConvert contractAttachmentConvert;
    private final DataAccessService dataAccessService;


    /**
     * 保存异常合同
     *
     * @param addParam
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer save(ContractAbnormalAddParam addParam) {
        ContractEntity contract = contractService.lambdaQuery()
                .eq(ContractEntity::getId, addParam.getContractId())
                .eq(ContractEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .one();
        if (!checkCondition(addParam, contract)) {
            return null;
        }
        if (null == addParam.getId()) {
            return add(addParam, contract);
        }
        ContractAbnormalEntity abnormalEntity = contractAbnormalService.getById(addParam.getId());
        if (StringUtils.isBlank(abnormalEntity.getApplyCode())) {
            return updateAndSubmit(addParam, contract);
        }
        return update(addParam, contract);
    }

    /**
     * 更新但是是首次提交
     * @param addParam
     * @param contract
     * @return
     */
    private Integer updateAndSubmit(ContractAbnormalAddParam addParam, ContractEntity contract) {
        // 修改主表信息
        Integer userId = UserThreadLocal.getUserId();
        ContractAbnormalEntity contractAbnormal = toUpdateAbnormalEntity(addParam, userId, contract.getCityId());
        boolean result = contractAbnormalService.updateById(contractAbnormal);

        // 添加项目信息
        result &= saveProjects(contractAbnormal.getId(), userId, contract.getId(), addParam.getProjectList());

        // 保存附件
        result &= saveAttachments(addParam.getContractId(), contractAbnormal.getId(), userId, addParam.getAttachments());

        // 发起审批
        try {
            result &= contractApprovalService.notifyOnApproval(contractAbnormal.getId(), contractAbnormal.getApplyCode(), contract.getCityId(), addParam.getAbnormalDeal());
        } catch (BusinessException e) {
            log.error("发起审批失败", e);
            throw new CommonException("发起审批失败，" + e.getMessage());
        } catch (Exception e) {
            log.error("发起审批失败", e);
            throw new CommonException("发起审批失败");
        }

        // 返回异常合同ID
        if (result) {
            return addParam.getId();
        }

        // 抛出异常, 回滚数据
        throw new CommonException("修改异常合同信息失败");
    }

    /**
     * 列表查询
     *
     * @param pageRequest
     * @return
     */
    public PageResponseVo<ContractAbnormalVO> pageList(PageRequestVo<ContractAbnormalQueryParam> pageRequest) {
        // 分页查询合同列表，自定义统计SQL
        Page<ContractAbnormalPageDTO> page = new Page<>(
                Optional.ofNullable(pageRequest.getCurrentPage()).orElse(1L),
                Optional.ofNullable(pageRequest.getPageSize()).orElse(10));
        page.setCountId("pageListContractAbnormals_COUNT");

        // 转换查询参数
        ContractAbnormalQueryDTO queryDto = contractAbnormalConvert.toQueryDto(pageRequest.getQuery());
        UserDataAccessV2DTO userDataAccessV2 = dataAccessService.getUserDataAccessV2();
        if (Objects.nonNull(userDataAccessV2)) {
            queryDto.setUserIds(userDataAccessV2.getUserIds());
            queryDto.setCityIds(userDataAccessV2.getCityIds());
            queryDto.setAgentIds(userDataAccessV2.getAgentIds());
        }

        // 分页查询
        IPage<ContractAbnormalPageDTO> pagedContractAbnormal = contractAbnormalService.pageListContractAbnormal(page, queryDto);
        PageResponseVo<ContractAbnormalVO> pageResponse = new PageResponseVo<>();
        pageResponse.setCurrentPage(pagedContractAbnormal.getCurrent());
        pageResponse.setTotalRows(pagedContractAbnormal.getTotal());
        pageResponse.setTotal(pagedContractAbnormal.getTotal());
        pageResponse.setPageSize(pagedContractAbnormal.getSize());
        pageResponse.setTotalPages(pagedContractAbnormal.getPages());
        if (CollectionUtils.isNotEmpty(pagedContractAbnormal.getRecords())) {
            pageResponse.setRows(toVOs(pagedContractAbnormal.getRecords()));
        }
        return pageResponse;
    }

    /**
     * 查询异常信息（SSP）
     *
     * @param contractId
     */
    public ContractAbnormalInfoWithPointVO getInfoWithPointCount(Integer contractId, boolean pointFlag) {
        ContractAbnormalInfoWithPointVO contractAbnormalInfoWithPointVO = new ContractAbnormalInfoWithPointVO();
        // 查询合同
        ContractEntity contractEntity = contractService.getById(contractId);
        if (contractEntity == null) {
            throw new CommonException("合同不存在");
        }
        contractAbnormalInfoWithPointVO.setContractCode(contractEntity.getContractCode());
        contractAbnormalInfoWithPointVO.setPeriod(contractEntity.getPeriod());
        contractAbnormalInfoWithPointVO.setTotalAmount(contractEntity.getTotalAmount());

        // 查询供应商
        List<ContractSupplierEntity> contractSupplierList = contractSupplierService.lambdaQuery().eq(ContractSupplierEntity::getContractId, contractId).list();
        String supplierNames = contractSupplierList.stream().map(ContractSupplierEntity::getSupplierName).collect(Collectors.joining(", "));
        contractAbnormalInfoWithPointVO.setSupplierName(supplierNames);

        // 原合同附件(主合同-已签章合同原件)
        List<ContractAttachmentEntity> attachmentEntityList = attachmentService.lambdaQuery()
                .eq(ContractAttachmentEntity::getContractId, contractId)
                .eq(ContractAttachmentEntity::getType, AttachmentTypeEnum.MAIN_SEALED.getCode())
                .eq(ContractAttachmentEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .list();
        contractAbnormalInfoWithPointVO.setUploadFlag(BooleFlagEnum.NO.getCode());
        if (CollectionUtils.isNotEmpty(attachmentEntityList)) {
            contractAbnormalInfoWithPointVO.setUploadFlag(BooleFlagEnum.YES.getCode());
            contractAbnormalInfoWithPointVO.setContractAttachment(contractAttachmentConvert.toVo(attachmentEntityList.get(0)));
        }

        // 查询项目点位信息
        if (pointFlag) {
            contractAbnormalInfoWithPointVO.setProjects(getProjectWithPointVOs(contractId));
        }
        return contractAbnormalInfoWithPointVO;
    }

    /**
     * 查询项目点位信息
     *
     * @param contractId
     * @return
     */
    private List<ContractAbnormalProjectWithPointVO> getProjectWithPointVOs(Integer contractId) {
        // 查询项目
        List<ContractProjectEntity> projects = contractProjectService.lambdaQuery().eq(ContractProjectEntity::getContractId, contractId).list();

        List<ContractAbnormalProjectWithPointVO> projectWithPointVOs = new ArrayList<>();
        for (ContractProjectEntity contractProjectEntity : projects) {
            ContractAbnormalProjectWithPointVO contractAbnormalProjectWithPointVO = contractAbnormalProjectConvert
                    .toAbnormalProjectWithPointVO(contractProjectEntity);

            //查询项目下的点位
            List<ContractDevicePointEntity> points = contractDevicePointService.lambdaQuery().eq(ContractDevicePointEntity::getProjectId,
                    contractProjectEntity.getId()).list();
            contractAbnormalProjectWithPointVO.setSignCount(points.size());

            List<String> codes = points.stream().map(ContractDevicePointEntity::getCode).toList();

            //SSP查询点位信息
            if (CollectionUtils.isNotEmpty(codes)) {
                ResultTemplate<Integer> response = feignSspRpc.countPointStatusByPointCode(codes);
                Integer pointCount = Optional.ofNullable(response).filter(ResultTemplate::getSuccess)
                        .map(ResultTemplate::getData).orElseGet(() -> {
                            log.warn("获取SSP点位统计数失败");
                            return 0;
                        });
                contractAbnormalProjectWithPointVO.setInstallCount(pointCount);
                projectWithPointVOs.add(contractAbnormalProjectWithPointVO);
            }
        }
        return projectWithPointVOs;
    }

    /**
     * 查询详情
     *
     * @param contractId
     * @return
     */
    public ContractAbnormalInfoVO getInfo(Integer contractId, Integer id) {
        ContractAbnormalInfoVO contractAbnormalInfoVO = new ContractAbnormalInfoVO();
        List<ContractAbnormalProjectEntity> projectEntities = contractAbnormalProjectService.lambdaQuery()
                .eq(ContractAbnormalProjectEntity::getAbnormalId, id)
                .eq(ContractAbnormalProjectEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .list();
        if (CollectionUtils.isEmpty(projectEntities)) {
            ContractAbnormalInfoWithPointVO contractAbnormalInfoWithPointVO = getInfoWithPointCount(contractId, true);
            contractAbnormalInfoVO.setAbnormalBase(contractAbnormalInfoWithPointVO);
            contractAbnormalInfoVO.setAbnormalBackfill(contractAbnormalConvert.toAbnormalWithInfoWithPointVO(contractAbnormalInfoWithPointVO));
            contractAbnormalInfoVO.setProjects(toBackFillVOs2(contractAbnormalInfoWithPointVO.getProjects()));
        } else {
            // 查询异常合同主信息
            ContractAbnormalInfoWithPointVO contractAbnormalInfoWithPointVO = getInfoWithPointCount(contractId, false);
            contractAbnormalInfoVO.setAbnormalBase(contractAbnormalInfoWithPointVO);
            // 异常合同回填信息
            ContractAbnormalBaseDTO contractAbnormalWithInfoDTO = Optional.ofNullable(contractAbnormalService.getAbnormalByContractId(contractId, id,
                    AbContractDealTypeEnum.ABNORMAL_CONTRACT_ADD.getCode())).orElseThrow(() -> new CommonException("异常合同不存在"));
            contractAbnormalInfoVO.setAbnormalBackfill(contractAbnormalConvert.toAbnormalWithInfoVO(contractAbnormalWithInfoDTO));

            // 异常合同项目回填信息
            List<ContractAbnormalProjectEntity> projects = contractAbnormalProjectService.lambdaQuery().eq(ContractAbnormalProjectEntity::getAbnormalId, id).list();
            contractAbnormalInfoVO.setProjects(toBackFillVOs(projects));

            // 填充附件
            fillingAttachments(id, contractAbnormalInfoVO::setAttachments);
        }
        return contractAbnormalInfoVO;
    }

    private List<ContractAbnormalProjectBackFillVO> toBackFillVOs2(List<ContractAbnormalProjectWithPointVO> projects) {
        if (CollectionUtils.isEmpty(projects)) {
            return Collections.emptyList();
        }
        List<ContractAbnormalProjectBackFillVO> voList = new ArrayList<>();
        for (ContractAbnormalProjectWithPointVO projectWithPointVO : projects) {
            voList.add(contractAbnormalProjectConvert.toContractAbnormalProjectBackFillVO2(projectWithPointVO));
        }
        return voList;
    }

    /**
     * 异常合同申请单导出
     *
     * @param queryParam
     */
    public String export(ContractAbnormalExportParam queryParam) {
        List<ContractAbnormalExportVO> abnormalExportVOS = exportListByAbnormal(queryParam);
        List<ContractAbnormalExportByProjectVO> projectExportVOS = exportListByProject(queryParam);
        try {
            String date = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String fileName = String.format(DownLoadTypeEnum.YCHT.getDesc()+"_%s_%s.xlsx", date, System.currentTimeMillis());
            return EasyExcelUtils.createExcelMulSheetToCos("申请单维度", "申请单+项目维度", abnormalExportVOS, projectExportVOS,
                    Collections.emptyList(), fileName, "abnormal");
        } catch (Exception e) {
            log.warn("导出异常申请数据失败, 请求参数: {}", queryParam, e);
        }
        return StringUtils.EMPTY;
    }

    public List<ContractSimpleAbnormalVO> getByContract(Integer contractId) {
        List<ContractAbnormalEntity> abnormalEntities = contractAbnormalService.lambdaQuery()
                .select(ContractAbnormalEntity::getApplyCode,
                        ContractAbnormalEntity::getApplyStatus,
                        ContractAbnormalEntity::getApplyTime,
                        ContractAbnormalEntity::getId)
                .eq(ContractAbnormalEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .eq(ContractAbnormalEntity::getContractId, contractId)
                .orderByDesc(ContractAbnormalEntity::getApplyTime)
                .list();
        return abnormalEntities.stream().map(contractAbnormalConvert::toAbnormalSimpleVO).toList();
    }

    /**
     * 保存异常合同项目信息
     *
     * @param id
     * @param userId
     * @param projectList
     * @return
     */
    private boolean saveProjects(Integer id, Integer userId, Integer contractId, List<ContractAbnormalProjectAddParam> projectList) {
        LambdaQueryWrapper<ContractAbnormalProjectEntity> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(ContractAbnormalProjectEntity::getAbnormalId, id);
        contractAbnormalProjectService.remove(lambdaQuery);

        // 查询项目信息
        List<String> projectCodes = projectList.stream().map(ContractAbnormalProjectAddParam::getProjectCode).distinct().toList();
        List<ContractProjectEntity> contractProjectEntityList = contractProjectService.lambdaQuery()
                .eq(ContractProjectEntity::getContractId, contractId)
                .in(ContractProjectEntity::getProjectCode, projectCodes)
                .list();
        Map<String, ContractProjectEntity> projectMap = contractProjectEntityList.stream().collect(Collectors.toMap(ContractProjectEntity::getProjectCode,
                project -> project, (k1, k2) -> k1));

        // 查询城市信息
        for (ContractAbnormalProjectAddParam contractAbnormalProjectAddParam : projectList) {
            ContractProjectEntity contractProjectEntity = projectMap.get(contractAbnormalProjectAddParam.getProjectCode());
            if (contractProjectEntity != null) {
                contractAbnormalProjectAddParam.setCityId(contractProjectEntity.getCityId());
                CityEntity cityEntity = cityService.lambdaQuery().select(CityEntity::getId, CityEntity::getRegionId)
                        .eq(CityEntity::getId, contractProjectEntity.getCityId()).one();
                if (cityEntity != null) {
                    contractAbnormalProjectAddParam.setRegionId(cityEntity.getRegionId());
                }
            }
        }
        List<ContractAbnormalProjectEntity> entityList = toProjectEntitys(id, userId, projectList);
        return contractAbnormalProjectService.saveBatch(entityList);
    }

    /**
     * 保存合同附件信息
     */
    private boolean saveAttachments(Integer contractId, Integer id, Integer userId, List<ContractAbnormalAttachmentParam> attachments) {
        if (CollectionUtils.isEmpty(attachments)) {
            return true;
        }
        LambdaQueryWrapper<ContractAttachmentEntity> attachmentQuery = Wrappers.lambdaQuery();
        attachmentQuery.eq(ContractAttachmentEntity::getContractId, id)
                .eq(ContractAttachmentEntity::getBelongTo, AttachmentBelongEnum.ABNORMAL.getCode());
        attachmentService.remove(attachmentQuery);

        // 数据转换
        List<ContractAttachmentEntity> entities = new ArrayList<>();
        for (ContractAbnormalAttachmentParam abnormalAttachmentParam : attachments) {
            ContractAttachmentEntity attachment = contractAttachmentConvert.toEntity(abnormalAttachmentParam);
            attachment.setContractId(id);
            attachment.setBelongTo(AttachmentBelongEnum.ABNORMAL.getCode());
            attachment.setBizId(contractId);
            attachment.setSubType(0);
            attachment.setCreator(userId);
            attachment.setOperator(userId);
            attachment.setDeleteFlag(BooleFlagEnum.NO.getCode());
            entities.add(attachment);
        }
        return attachmentService.saveBatch(entities);
    }


    /**
     * DTO转VO
     *
     * @param pagedContractAbnormal
     * @return
     */
    private List<ContractAbnormalVO> toVOs(List<ContractAbnormalPageDTO> pagedContractAbnormal) {
        if (CollectionUtils.isEmpty(pagedContractAbnormal)) {
            return Collections.emptyList();
        }
        // 转换成VO
        List<ContractAbnormalVO> abnormalVOList = new ArrayList<>();
        for (ContractAbnormalPageDTO contractAbnormalPageDTO : pagedContractAbnormal) {
            contractAbnormalPageDTO.setHandleTime(getInvalidDate(contractAbnormalPageDTO.getHandleTime()));
            ContractAbnormalVO contractAbnormalVO = contractAbnormalConvert.toVO(contractAbnormalPageDTO);
            abnormalVOList.add(contractAbnormalVO);
        }
        // 翻译有些特定信息
        converterFactory.convert(abnormalVOList);
        return abnormalVOList;
    }

    /**
     * 异常合同项目Entity转VO
     *
     * @param projectEntityList
     * @return
     */
    private List<ContractAbnormalProjectVO> toProjectVOs(List<ContractAbnormalProjectEntity> projectEntityList) {
        if (CollectionUtils.isEmpty(projectEntityList)) {
            return Collections.emptyList();
        }
        // 转换成VO
        List<ContractAbnormalProjectVO> projectVOList = new ArrayList<>();
        for (ContractAbnormalProjectEntity contractAbnormalProjectEntity : projectEntityList) {
            projectVOList.add(contractAbnormalProjectConvert.toVo(contractAbnormalProjectEntity));
        }
        return projectVOList;
    }

    /**
     * 异常合同项目Param转Entity
     *
     * @param projectList
     * @return
     */
    private List<ContractAbnormalProjectEntity> toProjectEntitys(Integer abnormalId, Integer userId, List<ContractAbnormalProjectAddParam> projectList) {
        if (CollectionUtils.isEmpty(projectList)) {
            return Collections.emptyList();
        }
        // 转换成VO
        List<ContractAbnormalProjectEntity> projectEntityList = new ArrayList<>();
        for (ContractAbnormalProjectAddParam contractAbnormalProjectAddParam : projectList) {
            ContractAbnormalProjectEntity projectEntity = contractAbnormalProjectConvert.toEntity(contractAbnormalProjectAddParam);
            projectEntity.setAbnormalId(abnormalId);
            projectEntity.setCreator(userId);
            projectEntity.setOperator(userId);
            projectEntityList.add(projectEntity);
        }
        return projectEntityList;
    }

    /**
     * 项目回填信息转VO
     *
     * @param entityList
     * @return
     */
    private List<ContractAbnormalProjectBackFillVO> toBackFillVOs(List<ContractAbnormalProjectEntity> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return Collections.emptyList();
        }
        List<ContractAbnormalProjectBackFillVO> voList = new ArrayList<>();
        for (ContractAbnormalProjectEntity contractAbnormalProjectEntity : entityList) {
            voList.add(contractAbnormalProjectConvert.toContractAbnormalProjectBackFillVO(contractAbnormalProjectEntity));
        }
        return voList;
    }

    /**
     * 填充附件
     */
    private void fillingAttachments(Integer abnormalId, Consumer<List<ContractAttachmentVO>> attachmentConsumer) {
        // 异常合同
        List<ContractAttachmentEntity> abnormalAttachmentList = attachmentService.lambdaQuery()
                .eq(ContractAttachmentEntity::getContractId, abnormalId)
                .eq(ContractAttachmentEntity::getBelongTo, AttachmentBelongEnum.ABNORMAL.getCode())
                .eq(ContractAttachmentEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .list();
        if (CollectionUtils.isEmpty(abnormalAttachmentList)) {
            return;
        }

        attachmentConsumer.accept(abnormalAttachmentList.stream().map(contractConvert::toVO).toList());
    }

    /**
     * 生成异常合同申请单编号
     * YCHT+年月日+流水号（4位，从0001开始）
     * YCHT202412010001
     */
    private String getContractAbnormalApplyCode() {
        String today = DATE_FORMATTER.format(LocalDate.now());
        String cacheKey = "cms:venue:abnormal:apply:code:" + today;
        Long index = stringRedisTemplate.opsForValue().increment(cacheKey);
        stringRedisTemplate.expire(cacheKey, 24, TimeUnit.HOURS);
        return String.format("YCHT%s%04d", today, index);
    }

    /**
     * 异常合同申请单导出（申请单维度）
     *
     * @param queryParam
     * @return
     */
    private List<ContractAbnormalExportVO> exportListByAbnormal(ContractAbnormalExportParam queryParam) {
        // 转换查询参数
        ContractAbnormalQueryDTO queryDto = contractAbnormalConvert.toExportParam(queryParam);
        List<ContractAbnormalPageDTO> abnormalPageDTOS = contractAbnormalService.listContractAbnormal(queryDto);
        if (CollectionUtils.isEmpty(abnormalPageDTOS)) {
            return Collections.emptyList();
        }
        List<ContractAbnormalExportVO> abnormalExportVOS = new ArrayList<>();
        for (ContractAbnormalPageDTO contractAbnormalPageDTO : abnormalPageDTOS) {
            contractAbnormalPageDTO.setHandleTime(getInvalidDate(contractAbnormalPageDTO.getHandleTime()));
            contractAbnormalPageDTO.setOverDateFlagStr(BooleFlagEnum.getDesc(contractAbnormalPageDTO.getOverDateFlag()));
            abnormalExportVOS.add(contractAbnormalConvert.toExportVO(contractAbnormalPageDTO));
        }
        converterFactory.convert(abnormalExportVOS);
        return abnormalExportVOS;
    }

    /**
     * 异常合同申请单导出（项目维度）
     *
     * @param queryParam
     * @return
     */
    private List<ContractAbnormalExportByProjectVO> exportListByProject(ContractAbnormalExportParam queryParam) {
        // 转换查询参数
        ContractAbnormalQueryDTO queryDto = contractAbnormalConvert.toExportParam(queryParam);
        List<ContractAbnormalPageByProjectDTO> abnormalPageDTOS = contractAbnormalService.listContractAbnormalByProject(queryDto);
        if (CollectionUtils.isEmpty(abnormalPageDTOS)) {
            return Collections.emptyList();
        }
        List<ContractAbnormalExportByProjectVO> abnormalExportVOS = new ArrayList<>();
        for (ContractAbnormalPageByProjectDTO contractAbnormalPageByProjectDTO : abnormalPageDTOS) {
            contractAbnormalPageByProjectDTO.setHandleTime(getInvalidDate(contractAbnormalPageByProjectDTO.getHandleTime()));
            contractAbnormalPageByProjectDTO.setOverDateFlagStr(BooleFlagEnum.getDesc(contractAbnormalPageByProjectDTO.getOverDateFlag()));
            abnormalExportVOS.add(contractAbnormalConvert.toExportVO(contractAbnormalPageByProjectDTO));
        }
        // 翻译有些特定信息
        converterFactory.convert(abnormalExportVOS);
        return abnormalExportVOS;
    }

    /**
     * 判断当前操作人是否是填写处理结果的人
     *
     * @param abnormalId
     * @return
     */
    public Boolean canWriteResult(Integer abnormalId) {
        Integer userId = UserThreadLocal.getUserId();
        ContractAbnormalDealUserDTO contractAbnormalDealUserDTO = contractAbnormalService.getAbnormalDealUser(abnormalId);
        String dealType = abnormalDealService
                .getByTypeAndAbnormalId(AbContractDealTypeEnum.MEDIA.getCode(), abnormalId);
        if (DealTypeEnum.BUSINESS.getCode().equals(dealType) || DealTypeEnum.STOP.getCode().equals(dealType)) {
            //业务负责人
            return userId.equals(contractAbnormalDealUserDTO.getRegionBusinessHead());
        } else if (DealTypeEnum.LEGAL.getCode().equals(dealType)) {
            //法务负责人
            return userId.equals(contractAbnormalDealUserDTO.getRegionLegalBp());
        }
        return false;
    }

    /**
     * 新增异常合同
     *
     * @param addParam
     * @return
     */
    private Integer add(ContractAbnormalAddParam addParam, ContractEntity contract) {
        // 添加异常合同主表信息
        Integer userId = UserThreadLocal.getUserId();
        ContractAbnormalEntity contractAbnormal = toAddAbnormalEntity(addParam, userId, contract.getCityId());
        boolean result = contractAbnormalService.save(contractAbnormal);

        // 添加项目信息
        result &= saveProjects(contractAbnormal.getId(), userId, contract.getId(), addParam.getProjectList());

        // 保存附件
        result &= saveAttachments(addParam.getContractId(), contractAbnormal.getId(), userId, addParam.getAttachments());

        // 发起审批
        try {
            result &= contractApprovalService.notifyOnApproval(contractAbnormal.getId(), contractAbnormal.getApplyCode(), contract.getCityId(), addParam.getAbnormalDeal());
        } catch (BusinessException e) {
            log.error("发起审批失败", e);
            throw new CommonException("发起审批失败，" + e.getMessage());
        } catch (Exception e) {
            log.error("发起审批失败", e);
            throw new CommonException("发起审批失败");
        }

        // 返回合同ID
        if (result) {
            return contractAbnormal.getId();
        }

        // 抛出异常, 回滚数据
        throw new CommonException("保存异常合同信息失败");
    }


    /**
     * 修改异常合同
     *
     * @param addParam
     * @return
     */
    private Integer update(ContractAbnormalAddParam addParam, ContractEntity contract) {
        // 修改主表信息
        Integer userId = UserThreadLocal.getUserId();
        ContractAbnormalEntity contractAbnormal = toUpdateAbnormalEntity(addParam, userId, contract.getCityId());
        boolean result = contractAbnormalService.updateById(contractAbnormal);

        // 修改项目信息
        result &= saveProjects(contractAbnormal.getId(), userId, contract.getId(), addParam.getProjectList());

        // 修改附件
        result &= saveAttachments(addParam.getContractId(), contractAbnormal.getId(), userId, addParam.getAttachments());

        // 发起审批
        try {
            result &= contractApprovalService.resubmitContract(contractAbnormal.getId(), addParam.getAbnormalDeal());
        } catch (BusinessException e) {
            log.error("发起审批失败", e);
            throw new CommonException("发起审批失败，" + e.getMessage());
        } catch (Exception e) {
            log.error("发起审批失败", e);
            throw new CommonException("发起审批失败");
        }

        // 返回异常合同ID
        if (result) {
            return addParam.getId();
        }

        // 抛出异常, 回滚数据
        throw new CommonException("修改异常合同信息失败");
    }

    /**
     * 检查新增异常合同申请入参
     *
     * @param addParam
     * @param contract
     * @return
     */
    private boolean checkCondition(ContractAbnormalAddParam addParam, ContractEntity contract) {
        if (contract == null) {
            throw new CommonException("该合同不存在");
        }
        if (DealTypeEnum.BUSINESS.getCode().equals(addParam.getAbnormalDeal().getDealType()) && addParam.getAbnormalDeal().getDealDate() == null) {
            throw new CommonException("异常处理时间不能为空");
        }
        if (!ContractAbnormalFlagEnum.NONE.getCode().equals(contract.getAbnormalFlag())) {
            throw new CommonException("该合同在异常中，不支持再次发起");
        }
        return true;
    }

    private boolean updateAbnormalFlag(Integer contractId) {
        // 修改原合同标识
        ContractEntity contractEntity = new ContractEntity();
        contractEntity.setId(contractId);
        contractEntity.setAbnormalFlag(ContractAbnormalFlagEnum.SUSPECTED.getCode());
        return contractService.updateById(contractEntity);
    }

    /**
     * 过滤无效日期
     *
     * @param time2
     * @return
     */
    private LocalDateTime getInvalidDate(LocalDateTime time2) {
        if (time2 == null) {
            return null;
        }
        LocalDateTime time1 = LocalDateTime.of(1000, 1, 1, 0, 0);
        boolean isEqual = time1.getYear() == time2.getYear() &&
                time1.getMonth() == time2.getMonth() &&
                time1.getDayOfMonth() == time2.getDayOfMonth();

        if (isEqual) {
            return null;
        } else {
            return time2;
        }
    }

    /**
     * 入参转换异常合同主表entity
     *
     * @param addParam
     * @return
     */
    private ContractAbnormalEntity toAddAbnormalEntity(ContractAbnormalAddParam addParam, Integer userId, Integer cityId) {
        ContractAbnormalEntity contractAbnormal = contractAbnormalConvert.toEntity(addParam);
        contractAbnormal.setApplyCode(getContractAbnormalApplyCode());
        contractAbnormal.setApplyStatus(ApplyStatusEnum.CHECKING.getCode());
        contractAbnormal.setAbnormalRemark(addParam.getRemark());
        contractAbnormal.setCreator(userId);
        contractAbnormal.setOperator(userId);

        // 大区、城市id
        contractAbnormal.setCityId(cityId);
        CityEntity cityEntity = cityService.lambdaQuery().select(CityEntity::getId, CityEntity::getRegionId).eq(CityEntity::getId, cityId).one();
        if (cityEntity != null) {
            contractAbnormal.setRegionId(cityEntity.getRegionId());
        }
        return contractAbnormal;
    }

    /**
     * 入参转换异常合同主表entity
     *
     * @param addParam
     * @return
     */
    private ContractAbnormalEntity toUpdateAbnormalEntity(ContractAbnormalAddParam addParam, Integer userId, Integer cityId) {
        ContractAbnormalEntity contractAbnormal = contractAbnormalConvert.toEntity(addParam);
        contractAbnormal.setApplyCode(getContractAbnormalApplyCode());
        contractAbnormal.setAbnormalRemark(addParam.getRemark());
        contractAbnormal.setCreator(userId);
        contractAbnormal.setOperator(userId);

        // 大区、城市id
        contractAbnormal.setCityId(cityId);
        CityEntity cityEntity = cityService.lambdaQuery().select(CityEntity::getId, CityEntity::getRegionId).eq(CityEntity::getId, cityId).one();
        if (cityEntity != null) {
            contractAbnormal.setRegionId(cityEntity.getRegionId());
        }
        return contractAbnormal;
    }

    /**
     * 获取处理人
     *
     * @param abnormalId
     * @return
     */
    public Integer getDealUser(Integer abnormalId) {
        ContractAbnormalDealUserDTO contractAbnormalDealUserDTO = contractAbnormalService.getAbnormalDealUser(abnormalId);
        String dealType = abnormalDealService
                .getByTypeAndAbnormalId(AbContractDealTypeEnum.MEDIA.getCode(), abnormalId);
        if (DealTypeEnum.BUSINESS.getCode().equals(dealType) || DealTypeEnum.STOP.getCode().equals(dealType)) {
            //业务负责人
            return contractAbnormalDealUserDTO.getRegionBusinessHead();
        } else if (DealTypeEnum.LEGAL.getCode().equals(dealType)) {
            //法务负责人
            return contractAbnormalDealUserDTO.getRegionLegalBp();
        }
        return null;
    }
}
