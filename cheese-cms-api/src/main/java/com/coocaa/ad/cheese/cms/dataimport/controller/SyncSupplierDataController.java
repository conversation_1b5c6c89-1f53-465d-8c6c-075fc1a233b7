package com.coocaa.ad.cheese.cms.dataimport.controller;

import com.alibaba.excel.util.StringUtils;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractDepositSupplierEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractPaymentPeriodEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractSupplierBankEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.SupplierBankEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.SupplierEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractDepositSupplierService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractPaymentPeriodService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractSupplierBankService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.supplier.ISupplierBankService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.supplier.ISupplierService;
import com.coocaa.ad.cheese.cms.common.tools.common.exception.CommonException;
import com.coocaa.ad.cheese.cms.common.tools.common.result.ResultTemplate;
import com.coocaa.ad.cheese.cms.common.tools.common.util.AesUtils;
import com.coocaa.ad.cheese.cms.venue.util.LogWrapper;
import com.coocaa.ad.common.anno.AuthIgnore;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 冗余供应商数据
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-06 17:47
 */
@Slf4j
@RestController
@RequestMapping("/sync/supplier-data")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class SyncSupplierDataController {
    private final ISupplierService supplierService;
    private final ISupplierBankService supplierBankService;
    private final IContractPaymentPeriodService contractPaymentPeriodService;
    private final IContractDepositSupplierService contractDepositSupplierService;
    private final IContractSupplierBankService contractSupplierBankService;

    @Autowired
    private SyncSupplierDataController self;

    // 冗余数据
    @AuthIgnore
    @PostMapping
    public ResultTemplate<String> sync() {
        try {
            // 补全供应商信息
            self.syncSupplierData();
            // 补全供应商银行信息
            self.syncSupplierBankData();

            return ResultTemplate.success("success");
        } catch (Exception e) {
            throw new CommonException("同步供应商数据失败", e);
        }
    }

    /**
     * 冗余数据 - 付款周期
     */
    @LogWrapper(name = "补全供应商信息")
    public void syncSupplierData() {
        // 查询所有供应商信息
        Map<Integer, SupplierEntity> supplierMap = supplierService.list().stream().collect(Collectors.toMap(SupplierEntity::getId, e -> e));
        if (supplierMap.isEmpty()) {
            throw new RuntimeException("供应商信息为空");
        }
        // 根据“supplier_id”补全供应商信息
        AtomicInteger ct = new AtomicInteger(1);
        supplierMap.forEach((supplierId, supplierEntity) -> {
            log.info("开始补全供应商[{}_{}]的信息, 当前进度: {}/{}", supplierId, supplierEntity.getSupplierName(), ct.getAndIncrement(), supplierMap.size());
            if (Objects.equals(supplierId, 0)) {
                return;
            }
            // 付款周期
            contractPaymentPeriodService.lambdaUpdate()
                    .eq(ContractPaymentPeriodEntity::getSupplierId, supplierId)
                    .set(ContractPaymentPeriodEntity::getSupplierName, supplierEntity.getSupplierName())
                    .set(ContractPaymentPeriodEntity::getSupplierCode, supplierEntity.getSupplierCode())
                    .update();
            log.info("补全付款周期供应商[{}_{}]的信息成功", supplierId, supplierEntity.getSupplierName());
            // 押金供应商
            contractDepositSupplierService.lambdaUpdate()
                    .eq(ContractDepositSupplierEntity::getSupplierId, supplierId)
                    .set(ContractDepositSupplierEntity::getSupplierName, supplierEntity.getSupplierName())
                    .set(ContractDepositSupplierEntity::getSupplierCode, supplierEntity.getSupplierCode())
                    .update();
            log.info("补全押金供应商[{}_{}]的信息成功", supplierId, supplierEntity.getSupplierName());
            // 合同供应商银行
            contractSupplierBankService.lambdaUpdate()
                    .eq(ContractSupplierBankEntity::getSupplierId, supplierId)
                    .set(ContractSupplierBankEntity::getSupplierName, supplierEntity.getSupplierName())
                    .set(ContractSupplierBankEntity::getSupplierCode, supplierEntity.getSupplierCode())
                    .update();
        });
    }

    /**
     * 冗余数据 - 押金供应商
     */
    @LogWrapper(name = "补全银行信息")
    public void syncSupplierBankData() {
        Map<String, SupplierBankEntity> supplierBankMap = supplierBankService.list().stream().collect(
                Collectors.toMap(SupplierBankEntity::getAccountNo, e -> e, (e1, e2) -> e1));
        if (supplierBankMap.isEmpty()) {
            throw new RuntimeException("供应商银行信息为空");
        }
        // 根据“account_no”补全银行信息
        AtomicInteger ct = new AtomicInteger(1);
        supplierBankMap.forEach((accountNo, supplierBankEntity) -> {
            log.info("开始补全银行信息[{}_{}]的信息, 当前进度: {}/{}", accountNo, supplierBankEntity.getAccountName(), ct.getAndIncrement(), supplierBankMap.size());
            if (StringUtils.isBlank(accountNo)) {
                return;
            }
            // 付款周期
            String encryptAccountNo = AesUtils.encryptHex(accountNo);
            contractPaymentPeriodService.lambdaUpdate()
                    .eq(ContractPaymentPeriodEntity::getAccountNo, encryptAccountNo)
                    .set(ContractPaymentPeriodEntity::getAccountName, supplierBankEntity.getAccountName())
                    .set(ContractPaymentPeriodEntity::getBankName, supplierBankEntity.getBankName())
                    .set(ContractPaymentPeriodEntity::getBankCode, supplierBankEntity.getBankCode())
                    .update();
            log.info("补全付款周期银行信息[{}_{}]的信息成功", accountNo, supplierBankEntity.getAccountName());
            // 押金供应商
            contractDepositSupplierService.lambdaUpdate()
                    .eq(ContractDepositSupplierEntity::getAccountNo, encryptAccountNo)
                    .set(ContractDepositSupplierEntity::getAccountName, supplierBankEntity.getAccountName())
                    .set(ContractDepositSupplierEntity::getBankName, supplierBankEntity.getBankName())
                    .set(ContractDepositSupplierEntity::getBankCode, supplierBankEntity.getBankCode())
                    .update();
            log.info("补全押金供应商银行信息[{}_{}]的信息成功", accountNo, supplierBankEntity.getAccountName());
            // 合同供应商银行
            contractSupplierBankService.lambdaUpdate()
                    .eq(ContractSupplierBankEntity::getBankAccountCode, encryptAccountNo)
                    .set(ContractSupplierBankEntity::getAccountName, supplierBankEntity.getAccountName())
                    .set(ContractSupplierBankEntity::getBankName, supplierBankEntity.getBankName())
                    .set(ContractSupplierBankEntity::getBankCode, supplierBankEntity.getBankCode())
                    .update();
        });
    }
}
