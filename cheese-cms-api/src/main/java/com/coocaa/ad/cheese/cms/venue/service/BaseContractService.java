package com.coocaa.ad.cheese.cms.venue.service;

import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractService;
import com.coocaa.ad.cheese.cms.common.tools.common.enums.BooleFlagEnum;
import com.coocaa.ad.cheese.cms.common.tools.common.exception.CommonException;
import com.coocaa.ad.cheese.cms.common.tools.venue.bean.ContractQueryDTO;
import com.coocaa.ad.cheese.cms.common.tools.venue.bean.UserDataAccessV2DTO;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractApplyStatusEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractTypeEnum;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractQueryParam;
import com.coocaa.ad.cheese.cms.venue.convert.contract.ContractConvert;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.google.common.collect.Sets;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

/**
 * 合同查询基类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-12
 */
@Slf4j
public abstract class BaseContractService {
    @Resource
    private DataAccessService dataAccessService;

    @Resource
    private ContractConvert contractConvert;

    @Resource
    private IContractService contractService;

    /**
     * 校验申请单
     */
    protected void validateApplyContract(Integer contractId) throws CommonException {
        if (Objects.isNull(contractId) || contractId <= 0) return;
        ContractEntity contract = Optional.ofNullable(contractService.getById(contractId))
                .orElseThrow(() -> new CommonException("申请单不存在"));
        validateApplyContract(contract, false);
    }

    /**
     * 校验申请单
     */
    protected void validateApplyContract(ContractEntity contract, boolean force) throws CommonException {
        if (Objects.isNull(contract) || Objects.isNull(contract.getId()) || contract.getId() <= 0 || force) return;
        Set<String> allowStatus = Sets.newHashSet(ContractApplyStatusEnum.DRAFT.getCode(),ContractApplyStatusEnum.RETURN.getCode());
        if (BooleFlagEnum.isYes(contract.getFormalFlag()) || !allowStatus.contains(contract.getApplyStatus())) {
            throw new CommonException("非草稿或退回申请单，不允许操作");
        }
    }


    /**
     * 转换查询参数
     */
    protected ContractQueryDTO toQueryDto(ContractQueryParam queryParam, Boolean agentAudit) {
        // 构建查询参数
        ContractQueryDTO queryDto = contractConvert.toQueryDto(queryParam);

        // 检查用户数据范围权限
        if (!Optional.ofNullable(agentAudit).orElse(Boolean.FALSE)) {
            UserDataAccessV2DTO userDataAccess = dataAccessService.getUserDataAccessV2();
            if (Objects.isNull(userDataAccess)) {
                log.warn("用户:{}, 无数据访问权限", UserThreadLocal.getUserId());
                throw new CommonException("用户数据权限不足");
            }

            // 构建用户权限、城市权限、代理商权限
            if (CollectionUtils.isNotEmpty(userDataAccess.getUserIds())) {
                queryDto.setUserIds(userDataAccess.getUserIds());
            }

            if (CollectionUtils.isNotEmpty(userDataAccess.getCityIds())) {
                queryDto.setCityIds(userDataAccess.getCityIds());
            }

            if (CollectionUtils.isNotEmpty(userDataAccess.getAgentIds())) {
                queryDto.setAgentIds(userDataAccess.getAgentIds());
            }
        }

        //筛选覆盖
        if (CollectionUtils.isNotEmpty(queryParam.getCity())) {
            queryDto.setCityIds(queryParam.getCity());
        }

        // 处理盖章状态
        if (Objects.nonNull(queryParam.getStampStatus())) {
            Integer stampStatus = queryParam.getStampStatus();
            if (stampStatus.equals(0)) {
                queryDto.setSealParty0Flag(BooleFlagEnum.YES.getCode());
            }

            if (stampStatus.equals(1)) {
                queryDto.setSealParty1Flag(BooleFlagEnum.YES.getCode());
            }

            if (stampStatus.equals(2)) {
                queryDto.setSealParty2Flag(BooleFlagEnum.YES.getCode());
            }

            if (stampStatus.equals(3)) {
                queryDto.setSealParty3Flag(BooleFlagEnum.YES.getCode());
            }
        }

        // 处理开始结束日期 >= 开始日期 & < 结束日期 + 1天
        Optional.ofNullable(queryParam.getStartCreateDate()).ifPresent(date -> queryDto.setStartCreateTime(date.atStartOfDay()));
        Optional.ofNullable(queryParam.getEndCreateDate()).ifPresent(date -> queryDto.setEndCreateTime(date.atStartOfDay().plusDays(1)));

        // 查询合同类型
        if (CollectionUtils.isEmpty(queryParam.getContractTypes())) {
            queryDto.setContractTypes(List.of(ContractTypeEnum.NORMAL.getCode(), ContractTypeEnum.CHANGE.getCode(), ContractTypeEnum.AMENDMENT.getCode()));
        } else {
            queryDto.setContractTypes(queryParam.getContractTypes());
        }

        log.debug("合同查询参数: {}", queryDto);
        return queryDto;
    }
}
