package com.coocaa.ad.cheese.cms.venue.service;

import cn.hutool.core.date.DatePattern;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ConfigEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractAmendmentChangeTypeEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractApprovalDetailEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractApprovalEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractDevicePointEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractProjectEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractSnapshotEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IConfigService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractAmendmentChangeTypeService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractApprovalDetailService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractApprovalService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractDevicePointService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractProjectService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractSnapshotService;
import com.coocaa.ad.cheese.cms.common.rpc.FeignAuthorityRpc;
import com.coocaa.ad.cheese.cms.common.tools.common.bean.common.CodeNameVO;
import com.coocaa.ad.cheese.cms.common.tools.common.bean.common.UserFeishuVO;
import com.coocaa.ad.cheese.cms.common.tools.common.bean.common.UserVO;
import com.coocaa.ad.cheese.cms.common.tools.common.bean.feishu.BaseFormItem;
import com.coocaa.ad.cheese.cms.common.tools.common.bean.feishu.CurrencyFormItem;
import com.coocaa.ad.cheese.cms.common.tools.common.bean.feishu.DepartmentFormItem;
import com.coocaa.ad.cheese.cms.common.tools.common.bean.feishu.FlowNode;
import com.coocaa.ad.cheese.cms.common.tools.common.bean.feishu.NumberFormItem;
import com.coocaa.ad.cheese.cms.common.tools.common.bean.feishu.StringFormItem;
import com.coocaa.ad.cheese.cms.common.tools.common.cos.ObjectUtils;
import com.coocaa.ad.cheese.cms.common.tools.common.exception.CommonException;
import com.coocaa.ad.cheese.cms.common.tools.common.result.ResultTemplate;
import com.coocaa.ad.cheese.cms.common.tools.common.util.AesUtils;
import com.coocaa.ad.cheese.cms.common.tools.common.util.FeiShuApprovalUtil;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractApplyStatusEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractChangeTypeEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractTypeEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.FeiShuApprovalFormIdEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.FeiShuFormItemTypeEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.SnapshotSourceTypeEnum;
import com.coocaa.ad.cheese.cms.venue.rpc.FeignMethWebRpc;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ApprovalRejectVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.user.SysUserApproveVO;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.lark.oapi.service.approval.v4.model.ApprovalNodeInfo;
import com.lark.oapi.service.approval.v4.model.GetInstanceRespBody;
import com.lark.oapi.service.approval.v4.model.InstanceTask;
import com.lark.oapi.service.approval.v4.model.InstanceTimeline;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;


/**
 * 合同审批服务类
 * 处理合同审批流程相关业务逻辑
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ContractApprovalService {

    /**
     * 合同审批详情页面URL
     */
    private static final String CONTRACT_APPROVAL_DETAIL_NORMAL_URL = "/contract-detail?id=";

    /**
     * 补充协议详情页面URL
     */
    private static final String CONTRACT_APPROVAL_DETAIL_ADD_URL = "/replenishment-agreement?id=";

    /**
     * 合同变更详情页面URL
     */
    private static final String CONTRACT_APPROVAL_DETAIL_CHANGE_URL = "/contract-approval-detail?id=";

    private final FeiShuApprovalUtil feiShuApprovalUtil;
    private final IContractApprovalService contractApprovalService;
    private final IContractApprovalDetailService contractApprovalDetailService;
    private final FeignAuthorityRpc authorityRpc;
    private final IContractService contractService;
    private final IContractProjectService contractProjectService;
    private final IContractDevicePointService contractDevicePointService;
    private final FeignMethWebRpc feignMethWebRpc;
    private final FeignAuthorityRpc feignAuthorityRpc;
    private final IConfigService configService;
    private final IContractAmendmentChangeTypeService contractAmendmentChangeTypeService;
    private final IContractSnapshotService contractSnapshotService;

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper()
            // 注册模块
            .registerModule(new JavaTimeModule())
            // 禁用时间戳
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
            // 设置时间格式
            .setDateFormat(new SimpleDateFormat(DatePattern.NORM_DATETIME_PATTERN));

    @Value("${contract.approval.approvalCode}")
    private String approvalCode;

    @Value("${contract.approval.detail.prefix}")
    private String contractDetailPrefix;

    @Value("${contract.approval.radio.yes}")
    private String contractApprovalRadioYes;

    @Value("${contract.approval.radio.no}")
    private String contractApprovalRadioNo;

    @Value("${contract.approval.radio.normal.yes}")
    private String contractApprovalRadioNormalYes;

    @Value("${contract.approval.radio.normal.no}")
    private String contractApprovalRadioNormalNo;

    @Value("${contract.approval.default.department:西南大区}")
    private String defaultDepartment;


    /**
     * 创建合同审批实例
     *
     * @param contractId 合同ID
     */
    public void createContractApprovalInstance(Integer contractId) {
        try {
            ContractEntity contract = contractService.getById(contractId);
            // 生成表单提交信息
            String formData = generateContractApprovalFormData(contract);

            // 获取飞书用户信息
            UserFeishuVO userFeiShu = getFeiShuUserInfo(contract);

            // 根据合同ID生成uuid;
            int version = generateApprovalVersion(contractId);
            String uuid = contractId + "-" + version + "-" + generateShortUuid();

            // 创建审批实例
            String instanceCode = feiShuApprovalUtil.createApprovalInstance(approvalCode, userFeiShu.getFeishuUserId(), userFeiShu.getOpenId(), formData, uuid);

            //保存到本地数据库
            ContractApprovalEntity entity = new ContractApprovalEntity();
            entity.setContractId(contractId);
            entity.setApprovalInstanceUuid(uuid);
            entity.setApprovalVersion(version);
            entity.setApprovalInstanceCode(instanceCode);
            entity.setApprovalStatus(0);
            contractApprovalService.save(entity);

            log.info("创建合同审批实例成功 - userId: {}, instanceCode: {}, contractId: {}, formData: {}, uuid: {}", UserThreadLocal.getUserId(), instanceCode, contractId, formData, uuid);
        } catch (Exception e) {
            log.error("创建合同审批实例失败 - contractId: {}", contractId);
            log.error("异常堆栈:{}", e);
            throw new RuntimeException("创建合同审批实例失败", e);
        }
    }

    public String generateShortUuid() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 16).toUpperCase();
    }

    /**
     * 获取当前用户的飞书审批信息
     *
     * @return
     */
    private UserFeishuVO getFeiShuUserInfo(ContractEntity contract) {
        Integer userId = contract.getFollower();
        UserVO user = getUserByUserId(userId);
        Integer userType = user.getType();

        if (userType != null && userType == 2) {
            //外部代理商
            ResultTemplate<SysUserApproveVO> userDetail = feignMethWebRpc.getUserDetail(user.getWno());
            if (!userDetail.getSuccess() || userDetail.getData() == null) {
                log.warn("外部用户获取审批用户为空,当前用户ID：{}", userId);
                throw new RuntimeException("外部用户获取审批用户为空,当前用户ID：" + userId);
            }
            SysUserApproveVO data = userDetail.getData();
            UserFeishuVO vo = new UserFeishuVO();
            vo.setFeishuUserId(data.getFsUserId());
            vo.setOpenId(data.getFsOpenId());
            return vo;
        }
        // 内部用户
        ResultTemplate<List<UserFeishuVO>> result = authorityRpc.getUserFeishuList(Collections.singletonList(userId));
        if (!result.getSuccess() || CollectionUtils.isEmpty(result.getData())) {
            log.warn("飞书用户信息获取失败，返回数据：{}", JSON.toJSONString(result));
            throw new RuntimeException("飞书用户信息获取失败");
        }
        return result.getData().get(0);
    }

    private UserVO getUserByUserId(Integer userId) {
        log.info("获取用户信息，用户ID：{}", userId);
        ResultTemplate<List<UserVO>> listResultTemplate = feignAuthorityRpc.getUserByIds(Collections.singletonList(userId));
        if (listResultTemplate == null || CollectionUtils.isEmpty(listResultTemplate.getData())) {
            log.warn("用户信息获取失败,返回结果：{}", JSON.toJSONString(listResultTemplate));
            throw new IllegalStateException("获取用户信息失败");
        }
        UserVO user = listResultTemplate.getData().get(0);
        if (user == null) {
            log.warn("用户信息获取失败,当前用户ID：{}", userId);
            throw new IllegalStateException("获取用户信息失败");
        }
        log.info("获取用户信息成功，用户信息：{}", JSON.toJSONString(user));
        return user;
    }

    /**
     * 填充申请表单
     * <p>
     * 表单示例
     * [
     * {
     * "id": "contract_apply_no",
     * "type": "input",
     * "value": "1"
     * },
     * {
     * "id": "is_third",
     * "type": "radioV2",
     * "value": "m4mha171-i4wqwrp3iu-0"
     * },
     * {
     * "id": "amount",
     * "type": "amount",
     * "value": 1234.56,
     * "currency": "CNY"
     * }
     * ]
     *
     * @param contract
     * @return
     */
    private String generateContractApprovalFormData(ContractEntity contract) {
        if (contract == null) {
            log.error("没有获取到合同或者相关项目信息");
            throw new IllegalStateException("没有获取到合同或者相关项目信息");
        }

        List<BaseFormItem> formData = new ArrayList<>();
        Integer contractId = contract.getId();
        //获取合同
        List<ContractProjectEntity> projectEntityList = contractProjectService.list(new LambdaQueryWrapper<ContractProjectEntity>().eq(ContractProjectEntity::getContractId, contractId));
        if (CollectionUtils.isEmpty(projectEntityList)) {
            log.error("没有获取到合同或者相关项目信息,contractId:{}", contractId);
            throw new IllegalStateException("没有获取到合同或者相关项目信息");
        }

        //合同申请单号
        StringFormItem contractApplyNoItem = generateInputFormItem(FeiShuApprovalFormIdEnum.CONTRACT_APPLY_NO.getCode(), contract.getApplyCode());
        formData.add(contractApplyNoItem);

        //合同申请类型
        Integer contractApplyType = contract.getContractType();
        if (contractApplyType != null && contractApplyType != 0) {
            String contractApplyTypeName = ContractTypeEnum.parse(contractApplyType).getDesc();
            if (ContractTypeEnum.AMENDMENT.getCode().equals(contractApplyType)) {
                contractApplyTypeName = contractApplyTypeName + assembleChangeInfo(contract);
            }
            StringFormItem contractApplyTypeItem = generateInputFormItem(FeiShuApprovalFormIdEnum.CONTRACT_APPLY_TYPE.getCode(), contractApplyTypeName);
            formData.add(contractApplyTypeItem);
        }

        // 名称截取 如 四川省,成都市,青羊区 获取最后一个,前面的字符串
        String areaName = projectEntityList.get(0).getAreaName();
        int lastCommaIndex = areaName.lastIndexOf(",");
        if (lastCommaIndex != -1) {
            areaName = areaName.substring(0, lastCommaIndex);
        }
        StringFormItem contractCityItem = generateInputFormItem(FeiShuApprovalFormIdEnum.CITY.getCode(), areaName);
        formData.add(contractCityItem);

        //合同类型
        StringFormItem contractTypeItem = generateInputFormItem(FeiShuApprovalFormIdEnum.TYPE.getCode(), contract.getBusinessType() == 1 ? "自营" : "代理");
        formData.add(contractTypeItem);

        //项目名称
        String projectName = projectEntityList.stream().map(ContractProjectEntity::getProjectName)
                .collect(Collectors.joining(","));

        StringFormItem contractProjectItem = generateInputFormItem(FeiShuApprovalFormIdEnum.ITEM_NAME.getCode(), projectName);
        formData.add(contractProjectItem);

        //查询此合同下设备数量
        Long deviceCount = contractDevicePointService.count(new LambdaQueryWrapper<ContractDevicePointEntity>().eq(ContractDevicePointEntity::getContractId, contractId));
        NumberFormItem signNumberItem = generateNumberFormItem(FeiShuApprovalFormIdEnum.POINT_NUM.getCode(), new BigDecimal(deviceCount));
        formData.add(signNumberItem);

        //是否三方合同
        Integer thirdFlag = contract.getThirdFlag();
        if (thirdFlag == null) {
            throw new IllegalArgumentException("缺少是否是三方合同字段，contractId" + contractId);
        }
        StringFormItem isThirdContractItem = generateRadioFormItem(FeiShuApprovalFormIdEnum.IS_THIRD.getCode(), thirdFlag == 1 ? contractApprovalRadioYes : contractApprovalRadioNo);
        formData.add(isThirdContractItem);
        // 是否标准合同
        Integer normalFlag = contract.getNormalFlag();
        if (normalFlag == null) {
            throw new IllegalArgumentException("缺少是否是标准合同字段，contractId" + contractId);
        }
        StringFormItem isNormalContractItem = generateRadioFormItem(FeiShuApprovalFormIdEnum.IS_NORMAL.getCode(), normalFlag == 1 ? contractApprovalRadioNormalYes : contractApprovalRadioNormalNo);
        formData.add(isNormalContractItem);
        // 总金额
        CurrencyFormItem totalAmountItem = generateAmountFormItem(FeiShuApprovalFormIdEnum.AMOUNT.getCode(), contract.getTotalAmount());
        formData.add(totalAmountItem);
        // 申请人姓名
        UserVO user = getUserByUserId(contract.getFollower());
        String name = user.getName();
        String wno = user.getWno();
        StringFormItem submitPersonItem = generateInputFormItem(FeiShuApprovalFormIdEnum.APPLY_USER.getCode(), name + "(" + wno + ")");
        formData.add(submitPersonItem);
        // 申请时间
        StringFormItem submitTimeItem = generateInputFormItem(FeiShuApprovalFormIdEnum.APPLY_TIME.getCode(), LocalDateTime.now()
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        formData.add(submitTimeItem);
        // 合同申请详情
        String detailUrl = generateContractDetailUrl(contract.getId() + "", contractApplyType);
        StringFormItem detailItem = generateInputFormItem(FeiShuApprovalFormIdEnum.DETAILS.getCode(), detailUrl);
        formData.add(detailItem);

        // 部门归属
        String departmentName = generateDepartmentName(contract.getCityId());
        DepartmentFormItem departmentItem = generateDepartmentFormItem(FeiShuApprovalFormIdEnum.DEPARTMENT.getCode(), departmentName);
        formData.add(departmentItem);

        String jsonString = JSON.toJSONString(formData);
        log.info("提交的审批表单数据contractId：{},表单：{}", contractId, jsonString);
        return jsonString;
    }

    /**
     * 组装变更信息
     *
     * @param contract
     * @return
     */
    private String assembleChangeInfo(ContractEntity contract) {
        if (contract == null) {
            return "";
        }
        Integer contractId = contract.getId();
        List<ContractAmendmentChangeTypeEntity> typeList = contractAmendmentChangeTypeService.lambdaQuery()
                .eq(ContractAmendmentChangeTypeEntity::getContractId, contractId)
                .orderByAsc(ContractAmendmentChangeTypeEntity::getId)
                .list();
        if (CollectionUtils.isEmpty(typeList)) {
            return "";
        }

        ContractAmendmentChangeTypeEntity entity = typeList.get(0);
        try {
            String changeType = ContractChangeTypeEnum.parse(entity.getChangeType()).getDesc();

            String subChangeType = typeList.stream()
                    .map(item -> ContractChangeTypeEnum.parse(item.getSubChangeType()).getDesc())
                    .collect(Collectors.joining(", "));

            if (StringUtils.isNotEmpty(subChangeType)) {
                return "(" + changeType + "(" + subChangeType + "))";
            }
        } catch (Exception e) {
            log.error("变更类型解析异常", e);
        }
        return "";
    }

    private String generateDepartmentName(Integer cityId) {
        String cityName = getCityNameById(cityId);
        if (StringUtils.isEmpty(cityName)) {
            log.warn("没有获取到城市名称,cityId:{}", cityId);
            return defaultDepartment;
        }
        String departmentName = null;
        try {
            departmentName = configService.lambdaQuery().select(ConfigEntity::getValue)
                    .eq(ConfigEntity::getName, cityName).eq(ConfigEntity::getParentCode, "CITY_DEPARTMENT_RELATION")
                    .last("LIMIT 1").one().getValue();
        } catch (Exception e) {
            log.error("没有获取到部门归属,cityId:{}", cityId);
        }
        if (StringUtils.isEmpty(departmentName)) {
            log.warn("没有获取到部门归属,cityId:{}", cityId);
            return defaultDepartment;
        }
        return departmentName;
    }

    private String getCityNameById(Integer cityId) {
        ResultTemplate<List<CodeNameVO>> template = feignAuthorityRpc.listCityByIds(Collections.singletonList(cityId));
        if (template == null || CollectionUtils.isEmpty(template.getData())) {
            return null;
        }
        return template.getData().get(0).getName();
    }

    /**
     * 生成审批单中的合同详情链接
     *
     * @param contractId
     * @return
     */
    private String generateContractDetailUrl(String contractId, Integer contractType) {
        if (contractType == null) {
            log.warn("合同类型为空，contractId:{}", contractId);
            return "";
        }
        if (ContractTypeEnum.AGREEMENT.getCode().equals(contractType)) {
            // 补充协议
            return contractDetailPrefix + CONTRACT_APPROVAL_DETAIL_ADD_URL + AesUtils.encryptHex(contractId);
        } else if (ContractTypeEnum.AMENDMENT.getCode().equals(contractType)) {
            // 变更合同
            return contractDetailPrefix + CONTRACT_APPROVAL_DETAIL_CHANGE_URL + AesUtils.encryptHex(contractId);
        } else {
            return contractDetailPrefix + CONTRACT_APPROVAL_DETAIL_NORMAL_URL + AesUtils.encryptHex(contractId);
        }
    }

    /**
     * 创建amount类型条目
     *
     * @param id
     * @param amount
     * @return
     */
    private CurrencyFormItem generateAmountFormItem(String id, BigDecimal amount) {
        CurrencyFormItem item = new CurrencyFormItem();
        item.setId(id);
        item.setType(FeiShuFormItemTypeEnum.AMOUNT.getCode());
        item.setValue(amount);
        item.setCurrency("CNY");
        return item;
    }

    /**
     * 生成input控件item
     *
     * @param id
     * @param value
     * @return
     */
    private StringFormItem generateInputFormItem(String id, String value) {
        StringFormItem item = new StringFormItem();
        item.setId(id);
        item.setType(FeiShuFormItemTypeEnum.INPUT.getCode());
        item.setValue(value);
        return item;
    }

    /**
     * 生成department控件item
     * {
     * "id":"widget1",
     * "type":"department",
     * "value":[
     * {
     * "open_id":"od-xxx"
     * }
     * ]
     * }
     *
     * @param id
     * @param value
     * @return
     */
    private DepartmentFormItem generateDepartmentFormItem(String id, String value) {
        DepartmentFormItem item = new DepartmentFormItem();
        item.setId(id);
        item.setType(FeiShuFormItemTypeEnum.DEPARTMENT.getCode());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("open_id", value);
        item.setValue(Collections.singletonList(jsonObject));
        return item;
    }

    /**
     * 生成number控件item
     *
     * @param id
     * @param value
     * @return
     */
    private NumberFormItem generateNumberFormItem(String id, BigDecimal value) {
        NumberFormItem item = new NumberFormItem();
        item.setId(id);
        item.setType(FeiShuFormItemTypeEnum.NUMBER.getCode());
        item.setValue(value);
        return item;
    }

    /**
     * 生成radio控件item
     *
     * @param id
     * @param value
     * @return
     */
    private StringFormItem generateRadioFormItem(String id, String value) {
        StringFormItem item = new StringFormItem();
        item.setId(id);
        item.setType(FeiShuFormItemTypeEnum.RADIOV2.getCode());
        item.setValue(value);
        return item;
    }

    /**
     * 生成此合同下的申请次数
     *
     * @param contractId 合同id
     * @return
     */
    private int generateApprovalVersion(Integer contractId) {
        // 查询此合同最大的已申请次数
        ContractApprovalEntity entity = contractApprovalService.lambdaQuery()
                .eq(ContractApprovalEntity::getContractId, contractId)
                .orderByDesc(ContractApprovalEntity::getApprovalVersion).last("limit 1").one();
        if (entity == null) {
            return 1;
        }
        return entity.getApprovalVersion() + 1;
    }

    /**
     * 查看审批详情
     *
     * @param contractId 合同id
     */
    public List<FlowNode> getApprovalInstance(Integer contractId) {
        List<FlowNode> approvalFlowList = new ArrayList<>();
        try {
            // 根据合同ID获取审批实例中id最大的
            ContractApprovalEntity entity = contractApprovalService.lambdaQuery()
                    .eq(ContractApprovalEntity::getContractId, contractId)
                    .orderByDesc(ContractApprovalEntity::getApprovalVersion).last("limit 1").one();

            if (entity == null) {
                log.error("没有获取到合同审批实例,contractId:{}", contractId);
                throw new IllegalArgumentException("没有获取到合同审批实例,contractId:" + contractId);
            }

            // 流程已结束从数据库查询
            Integer approvalStatus = entity.getApprovalStatus();
            if (approvalStatus != null && approvalStatus != 0) {
                ContractApprovalDetailEntity detailEntity = contractApprovalDetailService.lambdaQuery()
                        .eq(ContractApprovalDetailEntity::getApprovalInstanceUuid, entity.getApprovalInstanceUuid())
                        .one();
                if (detailEntity != null) {
                    log.error("没有获取到合同审批详情,contractId:{}", contractId);
                    try {
                        approvalFlowList = JSON.parseArray(detailEntity.getNodeListString(), FlowNode.class);
                        return approvalFlowList;
                    } catch (Exception e) {
                        log.error("解析审批详情失败", e);
                    }
                }
            }

            // 从飞书查询审批流
            return getFlowNodesFromFeiShu(entity, approvalFlowList);
        } catch (Exception e) {
            log.error("获取审批流异常，异常信息：{}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 查看审批详情(包括历史审批)
     *
     * @param contractId 合同id
     */
    public List<FlowNode> getApprovalInstanceWithHistory(Integer contractId) {
        List<FlowNode> approvalFlowList = new ArrayList<>();
        try {
            List<ContractApprovalEntity> approvalEntities = contractApprovalService.lambdaQuery()
                    .eq(ContractApprovalEntity::getContractId, contractId)
                    .orderByAsc(ContractApprovalEntity::getApprovalVersion).list();

            if (CollectionUtils.isEmpty(approvalEntities)) {
                log.warn("没有获取到合同审批实例,contractId:{}", contractId);
                return approvalFlowList;
            }

            for (ContractApprovalEntity entity : approvalEntities) {
                try {
                    // 流程已结束从数据库查询
                    if (isApprovalFinished(entity.getApprovalStatus())) {
                        List<FlowNode> nodes = getFinishedApprovalNodes(entity);
                        if (CollectionUtils.isNotEmpty(nodes)) {
                            approvalFlowList.addAll(nodes);
                            continue;
                        }
                    }
                    // 从飞书查询审批流
                    approvalFlowList.addAll(getFlowNodesFromFeiShu(entity, new ArrayList<>()));
                } catch (Exception e) {
                    log.error("处理审批实例异常, contractId:{}, instanceUuid:{}", contractId, entity.getApprovalInstanceUuid(), e);
                }
            }
            return approvalFlowList;
        } catch (Exception e) {
            log.error("获取审批流异常，contractId:{}", contractId, e);
            return approvalFlowList;
        }
    }

    /**
     * 清洗合同申请时间
     */
    public void cleaningApplicationTime(Integer contractType, Integer id, Integer businessType) {
        List<Integer> contractIds = contractService.lambdaQuery()
                .select(ContractEntity::getId)
                .eq(Objects.nonNull(contractType), ContractEntity::getContractType, contractType)
                .eq(Objects.nonNull(id), ContractEntity::getId, id)
                .eq(Objects.nonNull(businessType), ContractEntity::getBusinessType, businessType)
                .list()
                .stream().map(ContractEntity::getId).toList();

        List<ContractApprovalEntity> approvalEntities = contractApprovalService.lambdaQuery()
                .in(ContractApprovalEntity::getContractId, contractIds)
                .orderByAsc(ContractApprovalEntity::getApprovalVersion).list();

        Map<Integer, List<ContractApprovalEntity>> contractApprovalMap = approvalEntities.stream()
                .collect(Collectors.groupingBy(ContractApprovalEntity::getContractId));

        for (Integer contractId : contractIds) {
            try {
                List<ContractApprovalEntity> approvalEntityList = contractApprovalMap.get(contractId);
                if (approvalEntityList.size() > 1) {
                    ContractApprovalEntity contractApprovalEntity = approvalEntityList.get(0);
                    contractService.lambdaUpdate()
                            .set(ContractEntity::getApplyTime, contractApprovalEntity.getCreateTime())
                            .eq(ContractEntity::getId, contractId)
                            .update();
                }

            } catch (Exception e) {
                log.error("合同审批时间清洗失败, contractId:{}，错误信息：{}", contractId, e.getMessage());
            }
        }

    }

    private boolean isApprovalFinished(Integer approvalStatus) {
        return approvalStatus != null && approvalStatus != 0;
    }

    private List<FlowNode> getFinishedApprovalNodes(ContractApprovalEntity entity) {
        try {
            ContractApprovalDetailEntity detailEntity = contractApprovalDetailService.lambdaQuery()
                    .eq(ContractApprovalDetailEntity::getApprovalInstanceUuid, entity.getApprovalInstanceUuid())
                    .one();
            if (detailEntity == null) {
                log.warn("没有获取到合同审批详情, instanceUuid:{}", entity.getApprovalInstanceUuid());
                return Collections.emptyList();
            }
            return JSON.parseArray(detailEntity.getNodeListString(), FlowNode.class);
        } catch (Exception e) {
            log.error("解析审批详情失败, instanceUuid:{}", entity.getApprovalInstanceUuid(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 飞书获取审批流
     *
     * @param entity
     * @param approvalFlowList
     * @return
     */
    private List<FlowNode> getFlowNodesFromFeiShu(ContractApprovalEntity entity, List<FlowNode> approvalFlowList) {
        if (entity == null) {
            return approvalFlowList;
        }
        String instanceCode = entity.getApprovalInstanceCode();
        GetInstanceRespBody approvalInstance = feiShuApprovalUtil.getApprovalInstance(instanceCode);
        InstanceTimeline[] timeline = approvalInstance.getTimeline();
        if (timeline == null || timeline.length == 0) {
            return approvalFlowList;
        }
        InstanceTask[] taskListArray = approvalInstance.getTaskList();
        List<InstanceTimeline> timeLineList = Arrays.asList(timeline);
        List<InstanceTask> taskList = null;
        if (taskListArray == null || taskListArray.length == 0) {
            taskList = new ArrayList<>();
        } else {
            taskList = Arrays.asList(taskListArray);
        }
        // 获取taskList转换成map形式
        Map<String, InstanceTask> taskMap = taskList.stream()
                .collect(Collectors.toMap(InstanceTask::getId, task -> task));

        //根据openId获取飞书用户信息
        List<String> openIdsAll = new ArrayList<>();
        List<String> timelineOpenIds = timeLineList.stream().map(InstanceTimeline::getOpenId)
                .filter(userId -> StringUtils.isNotBlank(userId)).collect(Collectors.toList());
        List<String> taskListOpenIds = taskList.stream().map(InstanceTask::getOpenId)
                .filter(userId -> StringUtils.isNotBlank(userId)).collect(Collectors.toList());
        openIdsAll.addAll(timelineOpenIds);
        openIdsAll.addAll(taskListOpenIds);

        Map<String, String> userNames = feiShuApprovalUtil.getUserInfoByUserIds(openIdsAll);


        for (InstanceTimeline node : timeLineList) {
            FlowNode flowNode = new FlowNode();
            String openId = node.getOpenId();

            String type = node.getType();
            String taskId = node.getTaskId();

            InstanceTask instanceTask = null;

            //节点名称
            if (StringUtils.isEmpty(taskId)) {
                if (StringUtils.equalsIgnoreCase(type, "START")) {
                    flowNode.setNodeName("提交申请");
                } else if (StringUtils.equalsIgnoreCase(type, "CANCEL")) {
                    flowNode.setNodeName("撤回");
                } else {
                    log.info("taskId为空，type为{}", type);
                    continue;
                }
            } else {
                instanceTask = taskMap.get(taskId);
                //审核名称
                if (instanceTask != null) {
                    flowNode.setNodeName(instanceTask.getNodeName());
                }
            }

            // 操作人
            flowNode.setOperator(userNames.get(openId));

            // 操作时间
            String formattedTime = formatTimestamp(node.getCreateTime());
            flowNode.setOperateTime(formattedTime);

            //审核意见
            flowNode.setComment(node.getComment());

            //审批状态
            if (instanceTask != null) {
                flowNode.setStatus(StringUtils.equals(type, "START") ? "" : convertApprovalStatus(instanceTask.getStatus()));
            }
            if (StringUtils.isNotBlank(type) && type.contains("ROLLBACK")) {
                flowNode.setStatus("已退回");
            }
            approvalFlowList.add(flowNode);
        }
        // 获取taskListArray中最后一个对象
        if (taskList.size() == 0) {
            return approvalFlowList;
        }
        // 如果最后一个节点状态是已退回，则不再添加节点
        if (approvalFlowList.size() > 0 && StringUtils.equalsIgnoreCase(approvalFlowList.get(approvalFlowList.size() - 1).getStatus(), "已退回")) {
            return approvalFlowList;
        }
        InstanceTask lastTask = taskList.get(taskList.size() - 1);
        // timeLineList转换taskIdSet
        Set<String> taskIdSet = timeLineList.stream().map(InstanceTimeline::getTaskId).collect(Collectors.toSet());
        if (lastTask != null && !taskIdSet.contains(lastTask.getId())) {
            String status = lastTask.getStatus();
            if (!StringUtils.equalsIgnoreCase(status, "DONE")) {
                FlowNode flowNode = new FlowNode();
                flowNode.setNodeName(lastTask.getNodeName());
                flowNode.setOperator(userNames.get(lastTask.getOpenId()));
                String formattedTime = formatTimestamp(lastTask.getStartTime());
                flowNode.setOperateTime(formattedTime);
                flowNode.setStatus(convertApprovalStatus(status));
                approvalFlowList.add(flowNode);
            }
        }
        return approvalFlowList;
    }

    private String convertApprovalStatus(String type) {
        switch (type) {
            case "PENDING":
                return "进行中";
            case "APPROVED":
                return "已同意";
            case "REJECTED":
                return "已拒绝";
            case "TRANSFERRED":
                return "已转交";
            case "DONE":
                return "已完成";
            default:
                return type;
        }
    }

    /**
     * 获取审批定义
     */
    public List<ApprovalNodeInfo> getApproveDefinition() {
        List<ApprovalNodeInfo> approvalDefinition = feiShuApprovalUtil.getApprovalDefinition(approvalCode);
        return approvalDefinition;
    }

    private String convertNodeName(String type) {
        switch (type) {
            case "START":
                return "提交申请";
            default:
                return type;
        }
    }

    private String formatTimestamp(String timestamp) {
        // 将时间戳转换为 yyyy/M/d HH:mm:ss 格式
        long time = Long.parseLong(timestamp);
        return new SimpleDateFormat("yyyy/M/d HH:mm:ss").format(new Date(time));
    }

    public void getRejectReason(List<String> contractIds) {
        List<ContractApprovalEntity> approvalRejectItems = contractApprovalService.lambdaQuery()
                .eq(ContractApprovalEntity::getApprovalStatus, -1)
                .in(CollectionUtils.isNotEmpty(contractIds), ContractApprovalEntity::getContractId, contractIds)
                .list();

        if (CollectionUtils.isEmpty(approvalRejectItems)) {
            log.info("contractIds:{} 没有驳回记录", contractIds);
            return;
        }

        List<ApprovalRejectVO> resultList = new ArrayList<>();

        for (ContractApprovalEntity entity : approvalRejectItems) {

            try {
                ApprovalRejectVO item = new ApprovalRejectVO();
                item.setContractId(entity.getContractId() + "");
                item.setApprovalInstanceCode(entity.getApprovalInstanceCode());
                item.setApprovalInstanceUuid(entity.getApprovalInstanceUuid());
                item.setApprovalStatus(entity.getApprovalStatus() + "");
                item.setApprovalVersion(entity.getApprovalVersion() + "");

                // 流程已结束从数据库查询
                if (isApprovalFinished(entity.getApprovalStatus())) {
                    List<FlowNode> nodes = getFinishedApprovalNodes(entity);
                    if (CollectionUtils.isEmpty(nodes)) {
                        nodes = getFlowNodesFromFeiShu(entity, new ArrayList<>());
                    }
                    if (CollectionUtils.isEmpty(nodes)) {
                        continue;
                    }
                    List<FlowNode> rejected = nodes.stream()
                            .filter(node -> StringUtils.equalsIgnoreCase(node.getStatus(), "已拒绝"))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(rejected)) {
                        continue;
                    }
                    FlowNode flowNode = rejected.get(0);
                    item.setRejectReason(flowNode.getComment());
                    item.setRejectPoint(flowNode.getNodeName());
                    item.setRejectPerson(flowNode.getOperator());
                }
                resultList.add(item);

            } catch (Exception e) {
                log.error("获取拒绝信息异常", e);
            }
        }


        File tempFile = null;
        try {
            // 创建临时文件
            tempFile = File.createTempFile(UUID.randomUUID().toString(), ".xlsx");
            tempFile.deleteOnExit();
            // 写入Excel
            writeExcelFile(tempFile, resultList);
            // 上传到云存储
            uploadToCloud(tempFile);

            log.info("批量获取拒绝原因成功,已上传COS!");
        } catch (Exception e) {

        } finally {
            if (tempFile.exists()) {
                tempFile.delete(); // 主动删除临时文件
            }
        }

    }

    private String uploadToCloud(File tempFile) {
        String feature = "approval";
        String fileName = UUID.randomUUID().toString() + ".xlsx";
        ObjectUtils.uploadFile(ObjectUtils.getCosFileName(feature, fileName), tempFile);
        return ObjectUtils.getAccessUrl(feature, fileName);
    }


    private void writeExcelFile(File tempFile, List<ApprovalRejectVO> supplierData) {
        // 使用同一个ExcelWriter写入多个sheet
        try (ExcelWriter excelWriter = EasyExcel.write(tempFile).build()) {
            // 写入第一个sheet
            WriteSheet writeSheet1 = EasyExcel.writerSheet(0, "拒绝信息").head(ApprovalRejectVO.class).build();
            excelWriter.write(supplierData, writeSheet1);

        } catch (Exception e) {
            log.error("写入Excel文件失败", e);
            throw new CommonException("写入Excel文件失败: " + e.getMessage());
        }
    }

    /**
     * 重新提交合同审批
     *
     * @param contractId
     * @param isResubmit 是否再次提交  true 基于原流程 false 发起新的流程
     */
    public void taskResubmit(Integer contractId, boolean isResubmit) {
        try {
            log.info("taskResubmit contractId:{}, isResubmit:{}", contractId, isResubmit);
            ContractEntity contract = contractService.getById(contractId);
            log.info("handleContractApproval,合同信息{}", JSON.toJSONString(contract));
            // 合同审批状态判断
            if (!StringUtils.equals(contract.getApplyStatus(), ContractApplyStatusEnum.RETURN.getCode())) {
                throw new CommonException("合同当前审批不为退回状态");
            }
            // 获取合同审批中的审批实例code
            ContractApprovalEntity contractApproval = contractApprovalService.lambdaQuery()
                    .select(ContractApprovalEntity::getApprovalInstanceCode)
                    .eq(ContractApprovalEntity::getContractId, contractId)
                    .eq(ContractApprovalEntity::getApprovalStatus, 2)
                    .orderByDesc(ContractApprovalEntity::getApprovalVersion)
                    .last("limit 1")
                    .one();
            if (Objects.isNull(contractApproval)) {
                throw new CommonException("合同不存在审批中流程");
            }
            // 获取飞书用户信息
            UserFeishuVO userFeiShu = getFeiShuUserInfo(contract);
            // 不需要发起新的审批实例则直接重新发起
            if (isResubmit) {
                // 生成表单提交信息
                log.info("不需要发起新的审批实例则直接重新发起");
                String formData = this.generateContractApprovalFormData(contract);
                // 重新发起
                feiShuApprovalUtil.taskResubmit(approvalCode, contractApproval.getApprovalInstanceCode(), null,
                        userFeiShu.getFeishuUserId(), StringUtils.EMPTY, formData);
                return;
            }
            // 发起人拒绝旧的
            log.info("发起新的审批实例则直接重新发起");
            // 修改合同审批关联表
            contractApprovalService.lambdaUpdate()
                    .set(ContractApprovalEntity::getApprovalType, 1)
                    .set(ContractApprovalEntity::getApprovalEndTime, LocalDateTime.now())
                    .eq(ContractApprovalEntity::getApprovalInstanceCode, contractApproval.getApprovalInstanceCode())
                    .update();
            feiShuApprovalUtil.taskReject(approvalCode, contractApproval.getApprovalInstanceCode(), null,
                    userFeiShu.getFeishuUserId(), StringUtils.EMPTY);
            // 发起新的审批实例
            this.createContractApprovalInstance(contractId);
        } catch (Exception e) {
            log.error("重新发起审批失败", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 是否需要发起新的审批实例,true:需要，false:不需要
     */
    private Boolean checkResubmit(ContractEntity contract) {
        log.info("checkResubmit:{}", contract.getId());

        if (contract == null) {
            return false;
        }

        ContractSnapshotEntity snapshot = contractSnapshotService.lambdaQuery()
                .eq(ContractSnapshotEntity::getContractId, contract.getId())
                .eq(ContractSnapshotEntity::getSourceType, SnapshotSourceTypeEnum.RETURN.getCode())
                .orderByDesc(ContractSnapshotEntity::getId).last("limit 1").one();

        if (snapshot == null) {
            return false;
        }

        // 如果金额 或者 是否标准合同有变化，则需要发起新的审批实例
        BigDecimal totalAmount = contract.getTotalAmount();
        Integer normalFlag = contract.getNormalFlag();


        ContractEntity originalContract = getContractInfoFromSnapshot(snapshot);
        if (originalContract == null) {
            return false;
        }

        log.info("checkResubmit:{},{},{},{}", totalAmount, normalFlag, originalContract.getTotalAmount(), originalContract.getNormalFlag());

        if (totalAmount.compareTo(originalContract.getTotalAmount()) != 0 ||
                normalFlag.compareTo(originalContract.getNormalFlag()) != 0) {
            return true;
        }

        return false;
    }

    private ContractEntity getContractInfoFromSnapshot(ContractSnapshotEntity snapshot) {
        try {
            String snapshotVo = snapshot.getSnapshotVo();
            log.info("getContractInfoFromSnapshot:{}", snapshotVo);
            // 合同快照数据解析
            try {
                ContractDetailVO contractDetailVO = OBJECT_MAPPER.readValue(snapshot.getSnapshotVo(),
                        new TypeReference<ContractSnapshotService.ContractSnapshotDataItemDTO<ContractDetailVO>>() {
                        }).getData();

                ContractEntity result = new ContractEntity();
                result.setTotalAmount(contractDetailVO.getTotalAmount());
                result.setNormalFlag(contractDetailVO.getNormalFlag());
                return result;
            } catch (JsonProcessingException e) {
                log.error("合同快照[{}]数据解析失败！", e);
                return null;
            }
        } catch (Exception e) {
            log.error("获取合同信息失败", e);
            return null;
        }
    }
}
