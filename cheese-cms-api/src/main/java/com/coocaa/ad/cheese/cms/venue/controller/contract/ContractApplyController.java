package com.coocaa.ad.cheese.cms.venue.controller.contract;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.coocaa.ad.cheese.cms.common.tools.common.result.PageRequestVo;
import com.coocaa.ad.cheese.cms.common.tools.common.result.PageResponseVo;
import com.coocaa.ad.cheese.cms.common.tools.common.result.ResultTemplate;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractTypeEnum;
import com.coocaa.ad.cheese.cms.download.enums.DownLoadTypeEnum;
import com.coocaa.ad.cheese.cms.download.service.ContractApplyProjectPayService;
import com.coocaa.ad.cheese.cms.download.service.ContractApplyService;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractApplyParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractProjectParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractQueryParam;
import com.coocaa.ad.cheese.cms.venue.controller.BaseController;
import com.coocaa.ad.cheese.cms.venue.controller.contract.helper.ContractValidateHelper;
import com.coocaa.ad.cheese.cms.venue.service.ContractExportService;
import com.coocaa.ad.cheese.cms.venue.service.ContractReadService;
import com.coocaa.ad.cheese.cms.venue.service.ContractTemplateService;
import com.coocaa.ad.cheese.cms.venue.service.ContractWriteService;
import com.coocaa.ad.cheese.cms.venue.validation.contract.ApplyValidatorFactory;
import com.coocaa.ad.cheese.cms.venue.validation.contract.BusinessAvailableValidator;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractEditDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractPageVO;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.ad.downloader.bean.dto.TaskDTO;
import com.google.common.collect.Sets;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import jakarta.validation.groups.Default;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;
import java.util.concurrent.TimeUnit;


/**
 * 物业合同申请单
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-09
 */
@Slf4j
@RestController
@RequestMapping("/venue/contract/apply")
@Tag(name = "物业合同申请", description = "物业合同申请")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ContractApplyController extends BaseController {
    private final Validator validator;
    private final StringRedisTemplate stringRedisTemplate;
    private final ApplyValidatorFactory validatorFactory;
    private final BusinessAvailableValidator businessAvailableValidator;
    private final ContractReadService contractReadService;
    private final ContractWriteService contractWriteService;
    private final ContractTemplateService contractTemplateService;
    private final ContractValidateHelper contractValidateHelper;
    private final ContractApplyService contractApplyService;
    private final ContractApplyProjectPayService contractApplyProjectPayService;

    // 合同类型，正常合同和变更合同
    private static final List<Integer> CONTRACT_TYPES = List.of(ContractTypeEnum.NORMAL.getCode(), ContractTypeEnum.CHANGE.getCode());



    /**
     * 申请单(编辑)详情
     */
    @Operation(summary = "申请单(编辑)详情")
    @Parameter(name = "id", description = "合同ID", required = true, example = "1")
    @GetMapping("/{id}")
    public ResultTemplate<ContractEditDetailVO> getApplyDetail(
            @PathVariable(name = "id") Integer id,
            @RequestParam(name = "force", required = false, defaultValue = "false") boolean force) {
        return ResultTemplate.success(contractReadService.getApplyDetail(id, force));
    }

    /**
     * 申请单列表(分页)
     */
    @Operation(summary = "申请单列表(分页)")
    @PostMapping("/list")
    public ResultTemplate<PageResponseVo<ContractPageVO>> pageListApplies(@RequestBody PageRequestVo<ContractQueryParam> pageRequest) {
        pageRequest.setQuery(Optional.ofNullable(pageRequest.getQuery()).orElseGet(ContractQueryParam::new));
        pageRequest.getQuery().setContractTypes(CONTRACT_TYPES);
        return ResultTemplate.success(contractReadService.pageListApplies(pageRequest));
    }

    /**
     * 申请单导出
     */
    @Operation(summary = "申请单导出")
    @PostMapping("/export")
    public ResultTemplate<Long> exportApplies(@RequestBody ContractQueryParam queryParam) {
        queryParam.setContractTypes(CONTRACT_TYPES);
        TaskDTO taskDTO = new TaskDTO().setExecuteParams(JSON.toJSONString(queryParam));
        return ResultTemplate.success(this.contractApplyService.downloader(taskDTO));
    }

    @Operation(summary = "合同申请单导出-项目+付款周期维度")
    @PostMapping("/project-pay/export")
    public ResultTemplate<Long> exportAppliesWithPayPeriod(@RequestBody ContractQueryParam queryParam) {
        queryParam.setContractTypes(CONTRACT_TYPES);
        TaskDTO taskDTO = new TaskDTO().setExecuteParams(JSON.toJSONString(queryParam));
        return ResultTemplate.success(this.contractApplyProjectPayService.downloader(taskDTO));
    }


    /**
     * 合同申请
     */
    @Operation(summary = "合同申请")
    @Parameter(name = "submit", description = "是否提交", example = "false")
    @Parameter(name = "force", description = "是否再次发起", example = "false")
    @PostMapping
    public ResultTemplate<Integer> createOrUpdateContract(
            @RequestParam(name = "submit", required = false, defaultValue = "false") boolean submit,
            @RequestParam(name = "force", required = false, defaultValue = "false") boolean force,
            @RequestBody ContractApplyParam applyParam) {
        log.info("保存合同申请单: \n{}", JSONUtil.toJsonStr(applyParam));


        // 商机可用性检查
        List<String> validateResults = businessAvailableValidator.support(applyParam, submit)
                ? businessAvailableValidator.validate(applyParam, submit, false)
                : Collections.emptyList();
        if (CollectionUtil.isNotEmpty(validateResults)) {
            return ResultTemplate.fail(String.join(", ", validateResults));
        }

        // 供应商有效性验证
        String supplierCheckRet = contractValidateHelper.doValidateForSupplier(applyParam);
        if (StringUtils.isNotBlank(supplierCheckRet)) {
            return ResultTemplate.fail(supplierCheckRet);
        }

        // 满足条件就验证
        if (submit) {
            StringJoiner errorMessage = new StringJoiner(", ");
            Set<ConstraintViolation<ContractApplyParam>> violations = validator.validate(applyParam, Default.class);
            if (!violations.isEmpty()) {
                for (ConstraintViolation<ContractApplyParam> violation : violations) {
                    errorMessage.add(violation.getMessage());
                }
            }

            // 如果有错误信息，则返回错误信息
            if (errorMessage.length() > 0) {
                return ResultTemplate.fail(errorMessage.toString());
            }


            // 参数检查结果
            validateResults = validatorFactory.validate(applyParam, submit, true,
                    Sets.newHashSet(businessAvailableValidator));
            if (CollectionUtil.isNotEmpty(validateResults)) {
                log.error("合同申请参数校验失败: \n{}", String.join("\n", validateResults));
                return ResultTemplate.fail(String.join(", ", validateResults));
            }
        }

        // 修正点位数量
        String projectCode = null;
        Map<String, Set<String>> projectWithPointCodesMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(applyParam.getProjects())) {
            applyParam.getProjects().forEach(project -> {
                if (CollectionUtils.isEmpty(project.getPriceApplies())) {
                    return;
                }
                Set<String> devicePointCodes = new HashSet<>();
                project.getPriceApplies().stream()
                        .filter(price -> CollectionUtils.isNotEmpty(price.getDevices()))
                        .flatMap(price -> price.getDevices().stream())
                        .peek(device -> device.setCount(CollectionUtils.isEmpty(device.getPoints()) ? 0 : device.getPoints().size()))
                        .filter(device -> CollectionUtils.isNotEmpty(device.getPoints()))
                        .flatMap(device -> device.getPoints().stream())
                        .forEach(point -> {
                            // 获取设备下的点位数据
                            if (submit) {
                                devicePointCodes.add(point.getCode());
                            }
                        });
                projectWithPointCodesMap.put(project.getProjectCode(), devicePointCodes);
            });

            // 获取项目商机编码
            projectCode = applyParam.getProjects().stream()
                    .map(ContractProjectParam::getProjectCode)
                    .filter(StringUtils::isNotBlank)
                    .sorted().findFirst().orElse("");
        }

        // 提交时进行拦截：提交合同时,包含大屏点位,判断是否已完成大屏评级,包含小屏点位,判断是否已完成小屏评级
        if (submit) {
            String ret = contractReadService.checkContractDeviceFromH5(projectWithPointCodesMap);
            if (StringUtils.isNotBlank(ret)) {
                log.error("请先完成商机[{}]的评级。详情：{}", String.join(", ", projectWithPointCodesMap.keySet()), ret);
                return ResultTemplate.fail(ret);
            }

            // 项目转移检查
            boolean notTransfer = contractReadService.doCheckResubmit(UserThreadLocal.getUser(),
                    applyParam.getProjects().stream().map(ContractProjectParam::getProjectCode).toList());
            if (!notTransfer) {
                String msg = "当前合同存在已转移的项目，不能提交审批";
                log.error(msg);
                return ResultTemplate.fail(msg);
            }
        }

        // 对合同申请加锁
        String lockKey = null;
        if (StringUtils.isNotBlank(projectCode)) {
            lockKey = "cms:venue:apply:" + projectCode;
            Boolean locked = stringRedisTemplate.opsForValue()
                    .setIfAbsent(lockKey, projectCode, 5, TimeUnit.MINUTES);
            if (!Optional.ofNullable(locked).orElse(Boolean.TRUE)) {
                return ResultTemplate.fail("合同申请单正在操作中，请稍后再试！");
            }
        }

        try {
            Integer contractId = contractWriteService.createOrUpdateContract(submit, force, applyParam);
            return ResultTemplate.success(contractId);
        } finally {
            if (StringUtils.isNotBlank(lockKey)) {
                stringRedisTemplate.delete(lockKey);
            }
        }
    }

    /**
     * 合同预览
     */
    @Operation(summary = "合同预览")
    @PostMapping("/preview")
    public void previewTemplate(HttpServletResponse response, @RequestBody ContractApplyParam applyParam) {
        contractTemplateService.preview(response, applyParam);
    }

    @Operation(summary = "检查合同申请单再次发起权限")
    @Parameter(name = "id", description = "合同ID", required = true, example = "1")
    @GetMapping("/resubmit/check/{id}")
    public ResultTemplate<Boolean> checkResubmit(@PathVariable(name = "id") Integer id) {
        return ResultTemplate.success(contractReadService.checkResubmit(id));
    }

}
