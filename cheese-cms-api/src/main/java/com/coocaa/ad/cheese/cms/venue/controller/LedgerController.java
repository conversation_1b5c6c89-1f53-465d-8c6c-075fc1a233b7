package com.coocaa.ad.cheese.cms.venue.controller;

import com.alibaba.fastjson2.JSON;
import com.coocaa.ad.cheese.cms.common.tools.common.result.PageRequestVo;
import com.coocaa.ad.cheese.cms.common.tools.common.result.PageResponseVo;
import com.coocaa.ad.cheese.cms.common.tools.common.result.ResultTemplate;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractTypeEnum;
import com.coocaa.ad.cheese.cms.download.enums.DownLoadTypeEnum;
import com.coocaa.ad.cheese.cms.download.service.AccountsPayableLedgerHisService;
import com.coocaa.ad.cheese.cms.download.service.AccountsPayableLedgerService;
import com.coocaa.ad.cheese.cms.venue.bean.ledger.LedgerEditParam;
import com.coocaa.ad.cheese.cms.venue.bean.ledger.LedgerQueryParam;
import com.coocaa.ad.cheese.cms.venue.bean.supplier.ImportResult;
import com.coocaa.ad.cheese.cms.venue.service.LedgerService;
import com.coocaa.ad.cheese.cms.venue.vo.ledger.LedgerVO;
import com.coocaa.ad.downloader.bean.dto.TaskDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Optional;


@Slf4j
@RestController
@RequestMapping("/venue/ledger")
@Tag(name = "台账管理", description = "台账")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class LedgerController extends BaseController {

    private final LedgerService ledgerService;
    private final AccountsPayableLedgerService accountsPayableLedgerService;
    private final AccountsPayableLedgerHisService accountsPayableLedgerHisService;


    /**
     * 检查台账是否已生成过
     */
    @Operation(summary = "检查台账是否已生成过")
    @GetMapping("/check")
    public ResultTemplate<Boolean> checkLedger(@RequestParam(name = "contractId", required = true) Integer contractId) {
        return ResultTemplate.success(ledgerService.checkLedger(contractId));
    }

    /**
     * 检查押金台账是否已付款
     */
    @Operation(summary = "检查押金台账是否已付款")
    @GetMapping("/check-deposit")
    public ResultTemplate<Boolean> checkDepositLedger(@RequestParam(name = "contractId", required = true) Integer contractId,
                                                      @RequestParam(name = "projectCode", required = true) String projectCode) {
        return ResultTemplate.success(ledgerService.checkDepositLedgerEverPaid(contractId, projectCode));
    }

    /**
     * 创建台账
     */
    @Operation(summary = "创建台账")
    @GetMapping("/create")
    public ResultTemplate<?> createLedger(@RequestParam(name = "contractId", required = true) Integer contractId) {
        try {
            ledgerService.createLedger(contractId);
            return ResultTemplate.success();
        } catch (Exception e) {
            return ResultTemplate.fail(e.getMessage());
        }
    }

    /**
     * 合同终止
     */
    @Operation(summary = "合同终止")
    @GetMapping("/terminate")
    public ResultTemplate<?> terminateLedger(@RequestParam(name = "contractId", required = true) Integer contractId) {
        try {
            ledgerService.terminateLedger(contractId);
            return ResultTemplate.success();
        } catch (Exception e) {
            return ResultTemplate.fail(e.getMessage());
        }
    }

    /**
     * 删除台账
     */
    @Operation(summary = "删除台账")
    @GetMapping("/delete")
    public ResultTemplate<?> deleteLedger(@RequestParam(name = "contractId", required = true) Integer contractId) {
        ledgerService.deleteLedgerByContractId(contractId);
        return ResultTemplate.success();
    }


    /**
     * 台账列表(分页)
     */
    @Operation(summary = "台账列表(分页)")
    @PostMapping("/list")
    public ResultTemplate<PageResponseVo<LedgerVO>> getLedgerPageList(@RequestBody PageRequestVo<LedgerQueryParam> pageRequest) {
        //如果对象为空则
        pageRequest.setQuery(Optional.ofNullable(pageRequest.getQuery()).orElseGet(LedgerQueryParam::new));
        PageResponseVo<LedgerVO> pageResult = ledgerService.getLedgerPageList(pageRequest, ContractTypeEnum.NORMAL.getCode());
        return ResultTemplate.success(pageResult);
    }

    /**
     * 历史合同台账列表(分页)
     */
    @Operation(summary = "历史合同台账列表(分页)")
    @PostMapping("/old/list")
    public ResultTemplate<PageResponseVo<LedgerVO>> getOldLedgerPageList(@RequestBody PageRequestVo<LedgerQueryParam> pageRequest) {
        //如果对象为空则
        pageRequest.setQuery(Optional.ofNullable(pageRequest.getQuery()).orElseGet(LedgerQueryParam::new));
        PageResponseVo<LedgerVO> pageResult = ledgerService.getLedgerPageList(pageRequest, ContractTypeEnum.HISTORY.getCode());
        return ResultTemplate.success(pageResult);
    }


    /**
     * 编辑台账付款情况
     */
    @Operation(summary = "编辑台账付款情况")
    @PostMapping("/edit")
    public ResultTemplate<Integer> editLedger(@RequestBody @Validated LedgerEditParam editParam) {
        try {
            Integer ledgerId = ledgerService.editLedger(editParam);
            return ResultTemplate.success(ledgerId);
        } catch (Exception e) {
            return ResultTemplate.fail(e.getMessage());
        }
    }


    /**
     * 批量导入付款情况
     */
    @PostMapping("/file/import")
    @Operation(summary = "批量导入付款情况")
    public ResultTemplate<String> importLedger(@RequestParam(name = "file") MultipartFile file) {
        ImportResult importResult = ledgerService.importLedger(file);
        if (importResult.isSuccess()) {
            return ResultTemplate.success(importResult.getFileUrl(), importResult.getMessage());
        } else {
            return ResultTemplate.fail("导入失败");
        }
    }

    /**
     * 导出台账
     */
    @PostMapping("/file/export")
    @Operation(summary = "导出台账")
    public ResultTemplate<Long> exportLedger(@RequestBody LedgerQueryParam queryParam) {
        TaskDTO taskDTO = new TaskDTO().setExecuteParams(JSON.toJSONString(queryParam));
        return ResultTemplate.success(this.accountsPayableLedgerService.downloader(taskDTO));
    }


    /**
     * 导出历史台账
     */
    @PostMapping("/file/old/export")
    @Operation(summary = "导出历史台账")
    public ResultTemplate<Long> exportOldLedger(@RequestBody LedgerQueryParam queryParam) {
        TaskDTO taskDTO = new TaskDTO().setExecuteParams(JSON.toJSONString(queryParam));
        return ResultTemplate.success(this.accountsPayableLedgerHisService.downloader(taskDTO));
    }

    /**
     * 导出台账
     */
    @GetMapping("/file/template")
    @Operation(summary = "获取导入模板")
    public ResultTemplate<String> getTemplate() {
        return ResultTemplate.success(ledgerService.getTemplate());
    }

}