package com.coocaa.ad.cheese.cms.venue.controller.contract;


import com.alibaba.fastjson2.JSON;
import com.coocaa.ad.cheese.cms.common.tools.common.bean.common.BusinessOpportunityStatusVO;
import com.coocaa.ad.cheese.cms.common.tools.common.bean.common.CodeNameVO;
import com.coocaa.ad.cheese.cms.common.tools.common.constant.ContractConstants;
import com.coocaa.ad.cheese.cms.common.tools.common.enums.BooleFlagEnum;
import com.coocaa.ad.cheese.cms.common.tools.common.exception.CommonException;
import com.coocaa.ad.cheese.cms.common.tools.common.result.PageRequestVo;
import com.coocaa.ad.cheese.cms.common.tools.common.result.PageResponseVo;
import com.coocaa.ad.cheese.cms.common.tools.common.result.ResultTemplate;
import com.coocaa.ad.cheese.cms.common.tools.common.util.AesUtils;
import com.coocaa.ad.cheese.cms.common.tools.common.util.StringUtils;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractTypeEnum;
import com.coocaa.ad.cheese.cms.common.validation.ValidationGroup;
import com.coocaa.ad.cheese.cms.download.enums.DownLoadTypeEnum;
import com.coocaa.ad.cheese.cms.download.service.ValidContractProjectPayService;
import com.coocaa.ad.cheese.cms.download.service.ValidContractService;
import com.coocaa.ad.cheese.cms.venue.bean.contract.BatchContractTransferParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractAgentParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractArchiveParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractCancelParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractQueryParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractRejectParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractSealParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractTransferParam;
import com.coocaa.ad.cheese.cms.venue.controller.BaseController;
import com.coocaa.ad.cheese.cms.venue.service.ContractExportService;
import com.coocaa.ad.cheese.cms.venue.service.ContractReadService;
import com.coocaa.ad.cheese.cms.venue.service.ContractTemplateService;
import com.coocaa.ad.cheese.cms.venue.service.ContractWriteService;
import com.coocaa.ad.cheese.cms.venue.service.helper.LargeScreenHelper;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractDetailDiffVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractPageVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractPointVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractSnapshotRecordVO;
import com.coocaa.ad.cheese.cms.venue.vo.user.SysUserApproveVO;
import com.coocaa.ad.common.anno.AuthIgnore;
import com.coocaa.ad.downloader.bean.dto.TaskDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.Min;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;


/**
 * 物业合同管理
 *
 * <AUTHOR>
 * @since 2024-12-04
 */
@Slf4j
@RestController
@RequestMapping("/venue/contract")
@Tag(name = "物业合同管理", description = "物业合同管理")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ContractController extends BaseController {
    private final LargeScreenHelper largeScreenHelper;
    private final ContractReadService contractReadService;
    private final ContractWriteService contractWriteService;
    private final ContractTemplateService contractTemplateService;
    private final ValidContractService validContractService;
    private final ValidContractProjectPayService validContractProjectPayService;


    // 合同类型，正常合同和变更合同
    private static final List<Integer> CONTRACT_TYPES = List.of(ContractTypeEnum.NORMAL.getCode(), ContractTypeEnum.CHANGE.getCode());


    /**
     * 合同详情
     */
    @Operation(summary = "合同详情")
    @Parameter(name = "id", description = "合同ID", required = true, example = "1")
    @GetMapping("/{id}")
    public ResultTemplate<ContractDetailVO> getDetail(@PathVariable(name = "id") Integer id) {
        return ResultTemplate.success(contractReadService.getContractDetail(id));
    }

    /**
     * 合同详情
     */
    @AuthIgnore
    @Operation(summary = "合同详情")
    @Parameter(name = "id", description = "合同ID", required = true, example = "1")
    @GetMapping("/detail")
    public ResultTemplate<ContractDetailDiffVO> getDetailWithoutAuth(@RequestParam(name = "id") String encryptedId) {
        String plainText = AesUtils.decryptStr(encryptedId);
        Integer id = StringUtils.isBlank(plainText) ? null : NumberUtils.toInt(plainText);
        if (id == null || id <= 0) throw new CommonException("合同ID不正确");
        return ResultTemplate.success(contractReadService.contractDetailDiffWrapper(
                contractReadService.getContractDetail(id, true)));
    }

    /**
     * 合同列表(分页)
     */
    @Operation(summary = "合同列表(分页)")
    @PostMapping("/list")
    public ResultTemplate<PageResponseVo<ContractPageVO>> pageListContracts(@RequestBody PageRequestVo<ContractQueryParam> pageRequest) {
        pageRequest.setQuery(Optional.ofNullable(pageRequest.getQuery()).orElseGet(ContractQueryParam::new));
        pageRequest.getQuery().setContractTypes(CONTRACT_TYPES);
        return ResultTemplate.success(contractReadService.pageListContacts(pageRequest));
    }

    /**
     * 查询合同及点位信息
     */
    @Operation(summary = "查询合同及点位信息")
    @Parameter(name = "businessCodes", description = "商机编码", required = true, example = "BRR2025011502516-1, BRR2025011502518-1")
    @GetMapping("/points")
    public ResultTemplate<List<ContractPointVO>> getContractPoints(@RequestParam(name = "businessCodes") List<String> businessCodes) {
        if (CollectionUtils.isEmpty(businessCodes)) {
            return ResultTemplate.success(Collections.emptyList());
        }
        businessCodes = businessCodes.stream().filter(StringUtils::isNotBlank).distinct().toList();
        if (CollectionUtils.isEmpty(businessCodes)) {
            return ResultTemplate.success(Collections.emptyList());
        }
        return ResultTemplate.success(contractReadService.listContactByBusinessCodes(businessCodes));
    }

    /**
     * 查询商机是否已签了合同
     */
    @Operation(summary = "商机是否签约合同")
    @Parameter(name = "businessCodes", description = "商机编码", required = true, example = "BRR2025011502516-1, BRR2025011502518-1")
    @AuthIgnore
    @PostMapping("/signed/business-codes")
    public ResultTemplate<List<String>> checkContractSigned(@RequestBody Set<String> businessCodes) {
        return ResultTemplate.success(contractReadService.getSignedBusinessCodes(businessCodes));
    }

    /**
     * 检查点位是否大屏
     */
    @Operation(summary = "查询合同及点位信息")
    @PostMapping("/large-screen/point-codes")
    public ResultTemplate<List<String>> checkLargeScreen(@RequestBody Set<String> pointCodes) {
        return ResultTemplate.success(largeScreenHelper.getLargeScreenPointCodes(pointCodes));
    }

    /**
     * 合同导出
     */
    @Operation(summary = "合同导出")
    @PostMapping("/export")
    public ResultTemplate<Long> exportContracts(@RequestBody ContractQueryParam queryParam) {
        queryParam.setContractTypes(CONTRACT_TYPES);
        TaskDTO taskDTO = new TaskDTO().setExecuteParams(JSON.toJSONString(queryParam));
        return ResultTemplate.success(this.validContractService.downloader(taskDTO));
    }

    /**
     * 合同盖章
     */
    @Operation(summary = "合同盖章")
    @Parameter(name = "id", description = "合同ID", required = true, example = "1")
    @PutMapping("/{id}/seal")
    public ResultTemplate<Boolean> sealContract(@PathVariable(name = "id") Integer id,
                                                @Validated @RequestBody ContractSealParam sealParam) {
        return ResultTemplate.success(contractWriteService.sealContract(id, sealParam));
    }

    /**
     * 合同归档
     */
    @Operation(summary = "合同归档")
    @Parameter(name = "id", description = "合同ID", required = true, example = "1")
    @PutMapping("/{id}/archive")
    public ResultTemplate<Boolean> archiveContract(@PathVariable(name = "id") Integer id,
                                                   @Validated @RequestBody ContractArchiveParam archiveParam) {
        return ResultTemplate.success(contractWriteService.archiveContract(id, archiveParam));
    }

    /**
     * 合同作废
     */
    @Operation(summary = "合同作废")
    @Parameter(name = "id", description = "合同ID", required = true, example = "1")
    @PutMapping("/{id}/cancel")
    public ResultTemplate<Boolean> cancelContract(@PathVariable(name = "id") Integer id,
                                                  @Validated @RequestBody ContractCancelParam archiveParam) {
        return ResultTemplate.success(contractWriteService.cancelContract(id, archiveParam));
    }

    /**
     * 合同驳回
     */
    @Operation(summary = "合同驳回")
    @Parameter(name = "id", description = "合同ID", required = true, example = "1")
    @PutMapping("/{id}/reject")
    public ResultTemplate<Boolean> rejectContract(@PathVariable(name = "id") Integer id,
                                                  @Validated @RequestBody ContractRejectParam contractRejectParam) {
        return ResultTemplate.success(contractWriteService.rejectContract(id, contractRejectParam));
    }

    /**
     * 合同转交
     */
    @Operation(summary = "合同转交")
    @PostMapping("/{id}/transfer")
    public ResultTemplate<Boolean> transferContract(@PathVariable(name = "id") Integer id,
                                                    @Validated @RequestBody ContractTransferParam transferParam) {
        BatchContractTransferParam param = new BatchContractTransferParam();
        param.setContractIds(List.of(id));
        param.setFollower(transferParam.getFollower());
        param.setFollowerName(transferParam.getFollowerName());
        return ResultTemplate.success(contractWriteService.batchTransferContract(param));
    }

    /**
     * 合同批量转交
     */
    @Operation(summary = "合同批量转交")
    @PostMapping("/transfer/batch")
    public ResultTemplate<Boolean> batchTransferContract(@Validated @RequestBody BatchContractTransferParam transferParam) {
        return ResultTemplate.success(contractWriteService.batchTransferContract(transferParam));
    }

    /**
     * 合同模板
     */
    @Operation(summary = "合同模板")
    @GetMapping("/template")
    public ResultTemplate<String> getTemplate(
            @RequestParam(name = "deposit", required = false, defaultValue = "false") boolean withDeposit) {
        return ResultTemplate.success(contractTemplateService.getTemplate(withDeposit));
    }

    /**
     * 合同详情
     */
    @Operation(summary = "获取登录用户代理商")
    @PostMapping("/agent-org")
    public ResultTemplate<List<SysUserApproveVO>> getAgentOrg(@RequestBody ContractAgentParam agentParam) {
        return ResultTemplate.success(contractReadService.getAgentOrg(agentParam));
    }


    @Operation(summary = "查看楼宇是否有合同")
    @PostMapping("/has-contract")
    public ResultTemplate<Boolean> hasContract(@RequestBody List<String> buildingCodes) {
        return ResultTemplate.success(contractReadService.hasContract(buildingCodes));
    }

    @Operation(summary = "合同导出-项目+付款周期维度")
    @PostMapping("/project-pay/export")
    public ResultTemplate<Long> exportContractsWithPayPeriod(@RequestBody ContractQueryParam queryParam) {
        queryParam.setContractTypes(CONTRACT_TYPES);
        TaskDTO taskDTO = new TaskDTO().setExecuteParams(JSON.toJSONString(queryParam));
        return ResultTemplate.success(this.validContractProjectPayService.downloader(taskDTO));
    }

    @Operation(summary = "楼宇小助手查看楼宇合同状态")
    @PostMapping("/has-contract-status")
    public ResultTemplate<String> hasContractStatus(@RequestBody List<String> buildingCodes) {
        return ResultTemplate.success(contractReadService.hasContractStatus(buildingCodes));
    }

    /**
     * 上传押金条
     */
    @Operation(summary = "上传押金条")
    @Parameter(name = "id", description = "合同ID", required = true, example = "1")
    @PostMapping("/{id}/deposit")
    public ResultTemplate<Boolean> uploadDeposit(@PathVariable(name = "id")
                                                 @Min(value = 1, message = "合同ID不能小于1") Integer id,
                                                 @Validated(ValidationGroup.GroupSecond.class)
                                                 @RequestBody ContractCancelParam depositParam) {
        return ResultTemplate.success(contractWriteService.uploadDeposit(id, depositParam));
    }

    /**
     * 获取合同变更记录
     */
    @Operation(summary = "获取合同变更记录")
    @Parameter(name = "id", description = "合同ID", required = true, example = "1")
    @GetMapping("/{id}/snapshots")
    public ResultTemplate<List<ContractSnapshotRecordVO>> getContractSnapshots(@PathVariable(name = "id") Integer id) {
        return ResultTemplate.success(contractReadService.getContractSnapshots(id));
    }

    @Operation(summary = "模糊查询合同编码")
    @PostMapping("/query-code")
    public ResultTemplate<PageResponseVo<CodeNameVO>> queryContractCode(@RequestBody PageRequestVo<String> queryParam) {
        if (StringUtils.isBlank(queryParam.getQuery())) {
            return ResultTemplate.success();
        }
        return ResultTemplate.success(contractReadService.queryContractCode(queryParam));
    }

    @Operation(summary = "模糊查询项目名称")
    @PostMapping("/query-project")
    public ResultTemplate<PageResponseVo<CodeNameVO>> queryProjectName(@RequestBody PageRequestVo<String> queryParam) {
        if (StringUtils.isBlank(queryParam.getQuery())) {
            return ResultTemplate.success();
        }
        return ResultTemplate.success(contractReadService.queryProjectName(queryParam.getQuery(), CONTRACT_TYPES, queryParam.getPageSize()));
    }

    @Operation(summary = "根据合同状态返回商机状态")
    @PostMapping("/business-opportunity-status")
    @AuthIgnore
    public ResultTemplate<List<BusinessOpportunityStatusVO>> queryBusinessOpportunityStatus(@RequestBody List<String> buildingCodes) {
        return ResultTemplate.success(contractReadService.queryBusinessOpportunityStatus(buildingCodes));
    }

}
