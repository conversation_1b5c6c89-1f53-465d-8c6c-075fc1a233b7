package com.coocaa.ad.cheese.cms.dataimport.service;

import com.coocaa.ad.cheese.cms.common.tools.common.result.ResultTemplate;
import com.coocaa.ad.cheese.cms.dataimport.pojo.BasicInfoAllVO;
import com.coocaa.ad.cheese.cms.dataimport.pojo.BasicInfoMetaAllParam;
import com.coocaa.ad.cheese.cms.dataimport.pojo.ChangeOwnerVo;
import com.coocaa.ad.cheese.cms.dataimport.pojo.PointData;
import com.coocaa.ad.cheese.cms.dataimport.pojo.PriceApplyVO;
import com.coocaa.ad.cheese.cms.venue.vo.building.BuildingRatingDTO;
import com.coocaa.ad.common.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = "meht",
        configuration = FeignConfig.class)
public interface FeignCrmClient {
    @PostMapping("/data-import/building-rating/change-owner")
    ResultTemplate<Object> changeOwner(@RequestBody ChangeOwnerVo changeOwnerVo);

    @PostMapping("/data-import/price-apply")
    ResultTemplate<String> priceApply(@RequestBody PriceApplyVO priceApplyVo);

    @PostMapping("/data-import/syc-point-data")
    ResultTemplate<Boolean> sycPointData(@RequestBody PointData pointData);

    @GetMapping("/data-import/business-code")
    ResultTemplate<String> businessCode(@RequestParam("buildingNo") String buildingNo);

    @GetMapping("/buildMeta/basic-info/{buildingNo}")
    ResultTemplate<BuildingRatingDTO> basicBuildMeta(@PathVariable("buildingNo") String buildingNo);




    @PostMapping("/data-import/basic-info-all")
    ResultTemplate<List<BasicInfoAllVO>> basicInfoAllBuildMeta(@RequestBody BasicInfoMetaAllParam param);
}
