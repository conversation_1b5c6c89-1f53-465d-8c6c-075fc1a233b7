package com.coocaa.ad.cheese.cms.venue.vo.contract;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 合同操作历史记录
 * @since 2025-02-21 16:48
 */
@Data
public class ContractHistoryVO {
    @Schema(description = "日志ID", type = "integer", example = "1")
    private Integer id;

    @Schema(description = "类型（contract-1、supplier-2、project-3、attachment-4等，默认1）", type = "integer", example = "1")
    private Integer objectType;

    @Schema(description = "涉及的记录ID", type = "integer", example = "1")
    private Integer objectId;

    @Schema(description = "字段名称", type = "string", example = "1")
    private String filed;

    @Schema(description = "修改前的值", type = "string", example = "1")
    private String oldVal;

    @Schema(description = "修改后的值", type = "string", example = "1")
    private String newVal;
}
