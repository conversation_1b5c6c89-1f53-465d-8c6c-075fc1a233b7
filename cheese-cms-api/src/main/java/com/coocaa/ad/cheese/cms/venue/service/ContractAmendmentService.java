package com.coocaa.ad.cheese.cms.venue.service;

import com.alibaba.fastjson2.JSON;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractAmendmentChangeTypeEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractAttachmentEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractDeviceEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractLogEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractAmendmentChangeTypeService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractAttachmentService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractDeviceService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractLogService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractService;
import com.coocaa.ad.cheese.cms.common.tools.common.enums.BooleFlagEnum;
import com.coocaa.ad.cheese.cms.common.tools.common.exception.CommonException;
import com.coocaa.ad.cheese.cms.common.tools.common.result.PageRequestVo;
import com.coocaa.ad.cheese.cms.common.tools.common.result.PageResponseVo;
import com.coocaa.ad.cheese.cms.common.tools.common.util.AesUtils;
import com.coocaa.ad.cheese.cms.common.tools.common.util.TransactionUtils;
import com.coocaa.ad.cheese.cms.common.tools.venue.bean.ContractPageDTO;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.AttachmentTypeEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.BusinessTypeEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractApplyStatusEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractChangeFlagEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractChangeTypeEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractStatusEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractTypeEnum;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractAmendmentApplyParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractArchiveParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractAttachmentParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractCancelParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractQueryParam;
import com.coocaa.ad.cheese.cms.venue.convert.contract.ContractConvert;
import com.coocaa.ad.cheese.cms.venue.job.AmendFormalContractDataJob;
import com.coocaa.ad.cheese.cms.venue.rpc.FeignMethH5Rpc;
import com.coocaa.ad.cheese.cms.venue.vo.building.CmsResult;
import com.coocaa.ad.cheese.cms.venue.vo.building.PointDetail;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractAmendmentExportVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractAttachmentVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractDetailSimplifyVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractEditDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractPageVO;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.StringJoiner;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 变更合同服务
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-07 15:15
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ContractAmendmentService {
    private final ContractReadService contractReadService;
    private final ContractWriteService contractWriteService;
    private final ContractAttachmentService contractAttachmentService;
    private final ContractConvert contractConvert;
    private final IContractService contractService;
    private final IContractAttachmentService attachmentService;
    private final ContractExportService contractExportService;
    private final IContractDeviceService deviceService;
    private final IContractLogService contractLogService;
    private final LedgerService ledgerService;
    private final TransactionUtils transactionUtils;
    private final FeignMethH5Rpc feignMethH5Rpc;
    private final AmendFormalContractDataJob amendFormalContractDataJob;
    private final IContractAmendmentChangeTypeService changeTypeService;

    /**
     * 可以发起变更的合同状态
     */
    private final List<String> contractStatusList = Arrays.asList("0028-4", "0028-5");

    /**
     * 在途变更
     */
    private final List<String> applyStatusList = Arrays.asList("0025-1", "0025-2", "0025-3", "0025-4", "0025-7");

    private static final String IDENTITY_NAME_ZH = "变更合同";

    /**
     * 变更合同列表(分页)
     *
     * @param pageRequest 分页查询参数
     * @return 变更合同列表
     */
    public PageResponseVo<ContractPageVO> pageListAmendments(PageRequestVo<ContractQueryParam> pageRequest) {
        pageRequest.getQuery().fillParentFollower();
        PageResponseVo<ContractPageVO> pageResponse = contractReadService.pageListContacts(null, null, pageRequest);

        // 补充原合同数据
        var rows = pageResponse.getRows();
        if (CollectionUtils.isEmpty(rows)) {
            return pageResponse;
        }

        // 查询原合同记录
        List<Integer> contractIds = rows.stream()
                .filter(e -> Objects.nonNull(e.getParentId()) && ContractTypeEnum.AMENDMENT.getCode().equals(e.getContractType()))
                .map(ContractPageVO::getParentId).distinct().toList();
        if (CollectionUtils.isEmpty(contractIds)) {
            return pageResponse;
        }
        Map<Integer, ContractEntity> contractMap = contractService.lambdaQuery()
                .in(ContractEntity::getId, contractIds)
                .list()
                .stream()
                .collect(Collectors.toMap(ContractEntity::getId, Function.identity(), (v1, v2) -> v1));
        if (MapUtils.isEmpty(contractMap)) {
            return pageResponse;
        }

        // 填充数据
        for (ContractPageVO row : rows) {
            if (Objects.isNull(row.getParentId())) {
                continue;
            }
            if (!ContractTypeEnum.AMENDMENT.getCode().equals(row.getContractType())) {
                continue;
            }
            //
            ContractEntity contract = contractMap.get(row.getParentId());
            if (Objects.isNull(contract)) {
                continue;
            }
            row.setParent(contractConvert.toDetailSimplifyVO(contract));
        }

        return pageResponse;
    }

    /**
     * 获取变更合同详情
     *
     * @param contractId  变更合同ID
     * @param fillingInfo 是否填充信息
     * @return 变更合同详情
     */
    public ContractDetailVO getAmendmentDetail(Integer contractId, boolean fillingInfo) {
        // 获取原始详情信息
        ContractDetailVO contractDetailVO = contractReadService.getContractDetail(contractId,
                Map.of("buildingRating", fillingInfo, "ledger", true, "subLedger", true));
        if (Objects.isNull(contractDetailVO) || Objects.isNull(contractDetailVO.getParentId())) {
            return contractDetailVO;
        }

        // 补充原合同信息
        ContractEntity entity = Objects.equals(0, contractDetailVO.getParentId())
                ? null : contractService.getById(contractDetailVO.getParentId());
        if (Objects.isNull(entity)) {
            contractDetailVO.setParent(new ContractDetailSimplifyVO().setId(contractDetailVO.getParentId()));
            return contractDetailVO;
        }
        //
        ContractDetailSimplifyVO vo = contractConvert.toDetailSimplifyVO(entity)
                .setEncryptId(AesUtils.encryptHex(String.valueOf(entity.getId())));
        if (fillingInfo) {
            vo.setAttachments(attachmentService.listByContractId(entity.getId(),
                            List.of(AttachmentTypeEnum.MAIN_SEALED.getCode(), AttachmentTypeEnum.SUB_SEALED.getCode()))
                    .stream().map(attachment -> {
                        ContractAttachmentVO attachmentVO = contractConvert.toVO(attachment);
                        attachmentVO.setTypeName("%s-%s".formatted(Objects.equals(attachment.getContractId(), entity.getId()) ? "原始合同" : "变更合同",
                                AttachmentTypeEnum.getDesc(attachment.getType())));
                        return attachmentVO;
                    })
                    .sorted(Comparator.comparing(ContractAttachmentVO::getCreateTime)).toList());
        }
        contractDetailVO.setParent(vo);

        return contractDetailVO;
    }

    /**
     * 查询有效合同是否可以发起变更
     * 在途: 存在草稿,审批中,审批通过未生效
     * 已关闭的合同不支持
     *
     * @param contractId 合同ID
     * @param id         变更申请单ID
     * @return 可创建:true
     */
    public boolean canCreateContractAmendment(Integer contractId, Integer id) {
        ContractEntity contractEntity = contractService.getById(contractId);
        if (contractEntity == null) {
            return false;
        }
        if (!contractEntity.getFollower().equals(UserThreadLocal.getUserId()) ||
                !contractStatusList.contains(contractEntity.getFormalStatus())) {
            return false;
        }

        List<ContractEntity> list = contractService.lambdaQuery().select(ContractEntity::getId)
                .eq(ContractEntity::getParentId, contractId)
                .in(ContractEntity::getApplyStatus, applyStatusList)
                .eq(ContractEntity::getEffectFlag, BooleFlagEnum.NO.getCode())
                .ne(Objects.nonNull(id), ContractEntity::getId, id)
                .list();
        return CollectionUtils.isEmpty(list);
    }

    /**
     * 新增或保存合同变更
     *
     * @param submit
     * @param contractAmendmentApplyParam
     * @return
     */
    public Integer createOrUpdateContractAmendment(boolean submit, ContractAmendmentApplyParam contractAmendmentApplyParam) {
        return contractWriteService.createOrUpdateContractAmendment(submit, contractAmendmentApplyParam);
    }

    /**
     * 变更合同申请单详情
     *
     * @param contractId 变更合同ID
     * @param force      强制
     * @return 变更合同申请单详情
     */
    public ContractEditDetailVO getAmendmentApplyDetail(Integer contractId, boolean force) {
        // 获取原始合同详情信息
        ContractEditDetailVO contractEditDetailVO = contractReadService.getApplyDetail(contractId, force, null,
                Map.of("ledger", true, "subLedger", true, "planPaymentDateOrder", false));

        // 补充原合同信息
        if (Objects.isNull(contractEditDetailVO.getParentId())) {
            return contractEditDetailVO;
        }
        ContractEntity entity = null;
        if (contractEditDetailVO.getParentId() != 0) {
            entity = contractService.getById(contractEditDetailVO.getParentId());
        }
        if (Objects.isNull(entity)) {
            contractEditDetailVO.setParent(new ContractDetailSimplifyVO().setId(contractEditDetailVO.getParentId()));
        } else {
            contractEditDetailVO.setParent(contractConvert.toDetailSimplifyVO(entity));
        }

        return contractEditDetailVO;
    }

    /**
     * 导出合同
     *
     * @param queryParam 导出合同查询参数
     * @return 导出文件URL
     */
    public String exportContracts(ContractQueryParam queryParam) {
        // 绑定原合同的跟进人ID
        queryParam.fillParentFollower();
        // 查询出所有合同
        List<ContractPageDTO> contracts = contractExportService.listContacts(null, queryParam);
        if (CollectionUtils.isEmpty(contracts)) {
            return StringUtils.EMPTY;
        }
        // 查询原合同数据
        Map<Integer, ContractEntity> parentMap = contractService.lambdaQuery()
                .in(ContractEntity::getId, contracts.stream().map(ContractPageDTO::getParentId).collect(Collectors.toSet()))
                .select(ContractEntity::getId, ContractEntity::getParentId, ContractEntity::getContractCode)
                .list()
                .stream()
                .collect(Collectors.toMap(ContractEntity::getId, e -> e));
        // 数据转换
        final ContractEntity defaultEntity = new ContractEntity();
        List<ContractAmendmentExportVO> vos = contracts.stream()
                .peek(e -> {
                    e.setContractCode(parentMap.getOrDefault(e.getParentId(), defaultEntity).getContractCode());
                })
                .map(contractConvert::toAmendmentExportVO)
                .peek(vo -> {
                    vo.setBusinessTypeName(BusinessTypeEnum.getDesc(vo.getBusinessType()));
                    vo.setNormalFlagName(BooleFlagEnum.getDesc(vo.getNormalFlag()));
                    vo.setEffectFlagName(BooleFlagEnum.getDesc(vo.getEffectFlag()));
                    vo.setArchiveFlagName(BooleFlagEnum.getDesc(vo.getArchiveFlag()));
                    // 查找签约数
                    int signCount = deviceService.lambdaQuery().select(ContractDeviceEntity::getSignCount)
                            .eq(ContractDeviceEntity::getContractId, vo.getId())
                            .list().stream().mapToInt(ContractDeviceEntity::getSignCount).sum();
                    vo.setSignCount(signCount);
                })
                .toList();

        contractExportService.fillingDeviceData(vos);

        return contractExportService.exportContracts(queryParam, vos, IDENTITY_NAME_ZH);
    }

    /**
     * 上传变更合同附件
     *
     * @param contractId  变更合同ID
     * @param attachments 附件列表
     * @return 上传结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean uploadAttachments(Integer contractId, List<ContractAttachmentParam> attachments) {
        ContractEntity contract = Optional.ofNullable(contractService.getById(contractId))
                .orElseThrow(() -> new CommonException("%s不存在".formatted(IDENTITY_NAME_ZH)));
        if (BooleFlagEnum.isYes(contract.getArchiveFlag())) {
            throw new CommonException("%s已归档，不需要重复操作".formatted(IDENTITY_NAME_ZH));
        }
        if (!ContractTypeEnum.AMENDMENT.getCode().equals(contract.getContractType())) {
            throw new CommonException("传入的【%s】不是%s".formatted(ContractTypeEnum.getDesc(contract.getContractType()), IDENTITY_NAME_ZH));
        }
        // 修正附件类型
        attachments.forEach(attachment -> {
            if (StringUtils.isBlank(attachment.getFileType()) || attachment.getFileType().length() > 10) {
                attachment.setFileType(FilenameUtils.getExtension(attachment.getUrl()));
            }
        });

        boolean result = true;
        // 特殊处理双章附件
        if (attachments.stream().anyMatch(attachment -> AttachmentTypeEnum.MAIN_SEALED.getCode().equals(attachment.getType()))) {
            // 逻辑删除
            result &= attachmentService.lambdaUpdate()
                    .eq(ContractAttachmentEntity::getContractId, contractId)
                    .eq(ContractAttachmentEntity::getType, AttachmentTypeEnum.MAIN_SEALED.getCode())
                    .eq(ContractAttachmentEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                    .set(ContractAttachmentEntity::getDeleteFlag, BooleFlagEnum.YES.getCode())
                    .update();

            // 更新对应合同的附件上传标识
            result &= contractService.lambdaUpdate()
                    .set(ContractEntity::getUploadFlag, BooleFlagEnum.YES.getCode())
                    .eq(ContractEntity::getId, contractId)
                    .update();
        }

        // 上传附件
        result &= contractAttachmentService.uploadContractAttachments(contractId, attachments);

        // 记录操作日志
        saveContractLog(contractId, logEntity -> {
            String attachmentNames = attachments.stream()
                    .map(ContractAttachmentParam::getName)
                    .collect(Collectors.joining(", "));
            logEntity.setContent("上传归档附件。%s".formatted(StringUtils.isBlank(attachmentNames)
                    ? StringUtils.EMPTY : "附件名称：" + attachmentNames));
        });

        return result;
    }

    /**
     * 变更合同归档
     *
     * @param contractId   变更合同ID
     * @param archiveParam 归档参数
     * @return 归档结果
     */
    public Boolean archiveContract(Integer contractId, ContractArchiveParam archiveParam) {
        ContractEntity contract = Optional.ofNullable(contractService.getById(contractId))
                .orElseThrow(() -> new CommonException("[%s]%s不存在".formatted(contractId, IDENTITY_NAME_ZH)));
        if (!ContractTypeEnum.AMENDMENT.getCode().equals(contract.getContractType())) {
            throw new CommonException("传入的【%s】不是%s".formatted(ContractTypeEnum.getDesc(contract.getContractType()), IDENTITY_NAME_ZH));
        }
        //
        if (BooleFlagEnum.isYes(contract.getArchiveFlag())) {
            log.info("[{}]{}已归档，不需要重复操作", contractId, IDENTITY_NAME_ZH);
            return true;
        }
        // 验证变更合同是否存在双章附件
        if (attachmentService.lambdaQuery()
                .eq(ContractAttachmentEntity::getContractId, contractId)
                .eq(ContractAttachmentEntity::getType, AttachmentTypeEnum.MAIN_SEALED.getCode())
                .eq(ContractAttachmentEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .count() == 0) {
            throw new CommonException("[%s]%s不存在双章附件，归档失败".formatted(contractId, IDENTITY_NAME_ZH));
        }

        transactionUtils.doBatch(List.of(
                // 归档
                () -> contractService.lambdaUpdate()
                        .eq(ContractEntity::getId, contractId)
                        .set(ContractEntity::getArchiveFlag, BooleFlagEnum.YES.getCode())
                        .set(ContractEntity::getArchiveTime, LocalDateTime.now())
                        .set(Objects.nonNull(archiveParam.getRemark()), ContractEntity::getArchiveRemark, archiveParam.getRemark())
                        .update(),
                // 原合同变更状态更新
                () -> contractService.lambdaUpdate()
                        .eq(ContractEntity::getId, contract.getParentId())
                        .eq(ContractEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                        .set(ContractEntity::getChangeFlag, ContractChangeFlagEnum.WAIT_EFFECT.getCode())
                        .update()
        ), () -> "更新[%s]%s的归档状态".formatted(contractId, IDENTITY_NAME_ZH));

        // 判定变更合同的是否到达生效日期，如果是，同步原合同数据 + 生成台账
        archiveContractAfterCheck(contract);

        try {
            // 记录日志
            Integer userId = null;
            try {
                userId = UserThreadLocal.getUserId();
            } catch (Exception e) {
                log.error("获取用户ID失败", e);
            }
            ContractLogEntity entity = new ContractLogEntity();
            entity.setContractId(contractId);
            entity.setContent("确认归档。");
            entity.setCreator(userId);
            contractLogService.save(entity);
        } catch (Exception e) {
            log.error("保存[{}]{}归档操作日志失败", contractId, IDENTITY_NAME_ZH, e);
        }

        return true;
    }

    /**
     * 归档时，判定变更合同的是否到达生效日期
     *
     * @param amendment 变更合同对象
     */
    public void archiveContractAfterCheck(ContractEntity amendment) {
        log.info("[{}]{}归档后检查是否可以同步原合同数据", amendment.getId(), IDENTITY_NAME_ZH);
        try {
            // 变更合同是否到达生效日期
            if (Objects.isNull(amendment.getEffectDate())
                    || Objects.equals(BooleFlagEnum.YES.getCode(), amendment.getEffectFlag())
                    || amendment.getEffectDate().isAfter(LocalDate.now())) {
                log.info("[{}]{}未到达生效日期，不需要同步原合同数据", amendment.getId(), IDENTITY_NAME_ZH);
                return;
            }

            // 确定变更类型
            ContractAmendmentChangeTypeEntity changeTypeEntity = changeTypeService.lambdaQuery()
                    .eq(ContractAmendmentChangeTypeEntity::getContractId, amendment.getId())
                    .eq(ContractAmendmentChangeTypeEntity::getDeleted, BooleFlagEnum.NO.getCode())
                    .last("LIMIT 1")
                    .one();
            if (Objects.isNull(changeTypeEntity)) {
                log.info("[{}]{}变更类型不存在，不需要同步原合同数据", amendment.getId(), IDENTITY_NAME_ZH);
                return;
            }

            // 如果是，同步原合同数据 + 生成台账
            amendFormalContractDataJob.handleAmendment(ContractChangeTypeEnum.CHANGE.getCode().equals(changeTypeEntity.getChangeType())
                            ? changeTypeEntity.getChangeType()
                            : changeTypeEntity.getSubChangeType()
                    , List.of(amendment), UserThreadLocal.getUserId());
        } catch (Exception e) {
            log.error("[{}]{}归档后，已到生效日期时，同步原合同数据失败", amendment.getId(), IDENTITY_NAME_ZH, e);
        }
    }

    /**
     * 变更合同作废
     *
     * @param contractId  变更合同ID
     * @param cancelParam 作废参数
     * @return 作废结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelContract(Integer contractId, ContractCancelParam cancelParam) {
        ContractEntity amendment = Optional.ofNullable(contractService.lambdaQuery()
                        .eq(ContractEntity::getId, contractId)
                        .eq(ContractEntity::getContractType, ContractTypeEnum.AMENDMENT.getCode())
                        .eq(ContractEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                        .one())
                .orElseThrow(() -> new CommonException("[%s]%s不存在".formatted(contractId, IDENTITY_NAME_ZH)));
        if (StringUtils.equalsIgnoreCase(amendment.getFormalStatus(), ContractStatusEnum.CLOSED.getCode())) {
            throw new CommonException("[%s]%s已关闭，作废操作失败".formatted(contractId, IDENTITY_NAME_ZH));
        }

        // 更新变更合同状态
        boolean result = contractService.lambdaUpdate()
                .set(ContractEntity::getFormalStatus, ContractStatusEnum.CLOSED.getCode())
                .set(Objects.nonNull(cancelParam.getRemark()), ContractEntity::getCancelReason, cancelParam.getRemark())
                .set(ContractEntity::getApplyStatus, ContractApplyStatusEnum.INVALID.getCode())
                .eq(ContractEntity::getId, contractId)
                .update();

        // 保存作废附件
        result &= attachmentService.saveBatch(cancelParam.getAttachments().stream()
                .map(contractConvert::toEntity)
                .peek(attachment -> {
                    attachment.setContractId(contractId);
                    attachment.setBizId(contractId);
                    attachment.setType(AttachmentTypeEnum.CANCEL.getCode());
                    attachment.setSubType(0);
                    attachment.setDeleteFlag(BooleFlagEnum.NO.getCode());
                }).toList());
        String attachmentNames = cancelParam.getAttachments().stream().map(ContractAttachmentParam::getName).collect(Collectors.joining(","));

        // 更新原合同变更状态 - 无则改之，有则不变
        Long count = contractService.lambdaQuery()
                .eq(ContractEntity::getParentId, amendment.getParentId())
                .eq(ContractEntity::getContractType, ContractTypeEnum.AMENDMENT.getCode())
                .eq(ContractEntity::getApplyStatus, ContractApplyStatusEnum.APPROVED.getCode())
                .eq(ContractEntity::getEffectFlag, BooleFlagEnum.YES.getCode())
                .eq(ContractEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .count();
        if (count == 0) {
            result &= contractService.lambdaUpdate()
                    .set(ContractEntity::getChangeFlag, ContractChangeFlagEnum.NONE.getCode())
                    .eq(ContractEntity::getId, amendment.getParentId())
                    .update();
        }

        // 变更合同作废，删除台账信息
        ledgerService.deleteLedgerByContractId(contractId);

        // 保存操作日志
        if (result) {
            StringJoiner logMsg = new StringJoiner(", ");
            logMsg.add("作废%s。作废原因：%s".formatted(IDENTITY_NAME_ZH, cancelParam.getRemark()));
            if (StringUtils.isNotBlank(attachmentNames)) {
                logMsg.add("附件名称：%s".formatted(attachmentNames));
            }
            saveContractLog(contractId, contractLog -> contractLog.setContent(logMsg.toString()));
        } else {
            log.info("[{}]{}作废失败", contractId, IDENTITY_NAME_ZH);
        }
        return result;
    }

    /**
     * 保存合同操作日志
     *
     * @param contractId 合同ID
     * @param consumer   日志对象消费者
     * @return 保存结果
     */
    private boolean saveContractLog(Integer contractId, Consumer<ContractLogEntity> consumer) {
        ContractLogEntity entity = new ContractLogEntity();
        entity.setContractId(contractId);
        Optional.ofNullable(consumer).ifPresent(c -> c.accept(entity));
        return contractLogService.save(entity);
    }

    /**
     * 变更合同驳回
     *
     * @param contractId  变更合同ID
     * @param rejectParam 驳回参数
     * @return 驳回结果
     */
    public Boolean rejectContract(Integer contractId, ContractArchiveParam rejectParam) {
        ContractEntity contract = Optional.ofNullable(contractService.lambdaQuery()
                        .eq(ContractEntity::getId, contractId)
                        .eq(ContractEntity::getContractType, ContractTypeEnum.AMENDMENT.getCode())
                        .eq(ContractEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                        .select(ContractEntity::getId, ContractEntity::getApplyStatus, ContractEntity::getArchiveFlag)
                        .one())
                .orElseThrow(() -> new CommonException("[%s]%s不存在".formatted(contractId, IDENTITY_NAME_ZH)));
        if (!StringUtils.equals(contract.getApplyStatus(), ContractApplyStatusEnum.APPROVED.getCode())
                || BooleFlagEnum.isYes(contract.getArchiveFlag())) {
            throw new CommonException("[%s]%s当前不是待归档状态，驳回操作失败".formatted(contractId, IDENTITY_NAME_ZH));
        }

        // 更新变更合同状态
        if (!contractService.lambdaUpdate()
                .set(ContractEntity::getApplyStatus, ContractApplyStatusEnum.REJECT.getCode())
                .eq(ContractEntity::getId, contractId)
                .update()) {
            throw new CommonException("更新[%s]%s状态失败".formatted(contractId, IDENTITY_NAME_ZH));
        }

        // 记录日志
        saveContractLog(contractId, contractLog -> contractLog.setContent("驳回。驳回原因：%s".formatted(rejectParam.getRemark())));

        return true;
    }

    public List<PointDetail> pointByBusinessCode(String businessCode) {
        CmsResult<List<PointDetail>> pointDtos = feignMethH5Rpc.pointByBusinessCode(businessCode);
        log.info("feignMethH5Rpc.pointByBusinessCode:{}", JSON.toJSONString(pointDtos));
        return pointDtos.getData();
    }

    public String exportContractsWithPayPeriod(ContractQueryParam queryParam, BooleFlagEnum formalFlag) {
        return contractExportService.exportAmendmentWithPayPeriod(queryParam, formalFlag, IDENTITY_NAME_ZH);
    }
}
