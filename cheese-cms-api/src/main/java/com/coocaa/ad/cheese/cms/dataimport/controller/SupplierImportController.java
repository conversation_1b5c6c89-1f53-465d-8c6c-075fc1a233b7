package com.coocaa.ad.cheese.cms.dataimport.controller;

import com.coocaa.ad.cheese.cms.common.tools.common.result.ResultTemplate;
import com.coocaa.ad.cheese.cms.venue.service.SupplierService;
import com.coocaa.ad.common.anno.AuthIgnore;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;


@Slf4j
@Tag(name = "供应商数据导入", description = "供应商数据导入")
@RestController
@RequestMapping("/supplier/old")
public class SupplierImportController {
    @Autowired
    private SupplierService supplierService;

    @AuthIgnore
    @PostMapping("/import")
    @Operation(summary = "上传供应商数据")
    public ResultTemplate<String> uploadFile(@RequestParam(name = "supplier") MultipartFile supplier) {
        try {
            supplierService.processImportSupplier(supplier);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException("上传供应商数据失败");
        }
        return ResultTemplate.success();
    }

}
