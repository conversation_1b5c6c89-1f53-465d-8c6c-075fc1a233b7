package com.coocaa.ad.cheese.cms.venue.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.AgentEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.DataAccessEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IAgentService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IDataAccessService;
import com.coocaa.ad.cheese.cms.common.tools.common.exception.CommonException;
import com.coocaa.ad.cheese.cms.common.tools.common.result.PageRequestVo;
import com.coocaa.ad.cheese.cms.common.tools.common.result.PageResponseVo;
import com.coocaa.ad.cheese.cms.common.tools.common.util.AesUtils;
import com.coocaa.ad.cheese.cms.common.tools.common.util.StringUtils;
import com.coocaa.ad.cheese.cms.common.tools.venue.constant.VenueConstants;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.DataAccessTypeEnum;
import com.coocaa.ad.cheese.cms.venue.bean.access.DataAccessAgentParam;
import com.coocaa.ad.cheese.cms.venue.bean.agent.AgentParam;
import com.coocaa.ad.cheese.cms.venue.bean.agent.AgentQueryParam;
import com.coocaa.ad.cheese.cms.venue.convert.agent.AgentConvert;
import com.coocaa.ad.cheese.cms.venue.util.HttpUtils;
import com.coocaa.ad.cheese.cms.venue.vo.agent.AgentVO;
import com.coocaa.ad.cheese.cms.venue.vo.agent.CompanyBaseInfoVO;
import com.coocaa.ad.cheese.cms.venue.vo.agent.CompanyInfoVO;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class AgentService {

    @Value("${tianyancha.api.endpoint:http://open.api.tianyancha.com}")
    private String tianYanChaApiEndpoint;

    @Value("${tianyancha.api.token:0ff1a6293924934354ea16764dec8c342c2fdb9f51f6c42423c16078540b29c7523c61b666dd877c40ecc5de00136b36}")
    private String tianYanChaApiToken;

    private final IAgentService agentService;
    private final IDataAccessService dataAccessService;

    /**
     * 创建或更新代理商
     *
     * @param applyParam 申请代理商的参数
     * @return 代理商ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer createOrUpdateAgent(AgentParam applyParam) {

        Integer id = applyParam.getId();
        // 验证代理商名称或者社会信用代码是否重复
        AgentEntity agent = agentService.getOne(new LambdaQueryWrapper<AgentEntity>().eq(AgentEntity::getAgentName, applyParam.getAgentName())
                .or().eq(AgentEntity::getUnifiedSocialCreditCode, applyParam.getUnifiedSocialCreditCode())
                .last("limit 1"));
        if (agent != null && (id == null || !id.equals(agent.getId()))) {
            throw new CommonException("代理商名称或社会信用代码已存在");
        }

        //保存或更细Agent信息
        AgentEntity entity = AgentConvert.INSTANCE.toEntity(applyParam);
        boolean result = agentService.saveOrUpdate(entity);
        Integer AgentId = entity.getId();
        if (!result || AgentId == null) {
            throw new CommonException("创建或更新代理商失败");
        }
        return entity.getId();
    }


    /**
     * 分页获取代理商列表
     *
     * @param pageRequest 分页参数
     * @return 代理商分页数据结果
     */
    public PageResponseVo<AgentVO> getAgentList(PageRequestVo<AgentQueryParam> pageRequest) {
        //如果当前页码小于零或者为空，则设置为1
        if (pageRequest.getCurrentPage() == null || pageRequest.getCurrentPage() <= 0) {
            pageRequest.setCurrentPage(1L);
        }
        // 如果每页大小小于等于或者为空，则设置为20
        if (pageRequest.getPageSize() == null || pageRequest.getPageSize() <= 0) {
            pageRequest.setPageSize(VenueConstants.DEFAULT_PAGE_SIZE);
        }
        // 分页查询代理商列表
        AgentQueryParam query = pageRequest.getQuery();
        // 按照query条件查询代理商列表，query为空则查询所有代理商,字段有值则按照字段查询，字段为空则不设置此字段的查询条件
        LambdaQueryWrapper<AgentEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (query != null) {
            queryWrapper.like(StringUtils.isNotBlank(query.getAgentName()), AgentEntity::getAgentName, query.getAgentName())
                    .like(query.getAgentType() != null, AgentEntity::getAgentTypes, query.getAgentType())
                    .eq(query.getStatus() != null, AgentEntity::getStatus, query.getStatus());
        }

        Page<AgentEntity> page = agentService.page(new Page<>(pageRequest.getCurrentPage(), pageRequest.getPageSize()), queryWrapper);
        List<AgentVO> list = page.getRecords().stream().map(AgentConvert.INSTANCE::toAgentVO)
                .collect(Collectors.toList());
        return new PageResponseVo<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), list, page.getTotal());
    }

    public List<CompanyBaseInfoVO> getCompanyInfoList(String word, Integer pageNum, Integer pageSize) {

        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json; charset=utf-8");
        header.put("Connection", "keep-alive");
        header.put("Authorization", AesUtils.decryptStr(tianYanChaApiToken));
        String apiUrl = String.format("%s/services/open/search/2.0?word=%s&pageSize=%s&pageNum=%s", tianYanChaApiEndpoint, URLEncoder.encode(word, Charset.defaultCharset()), pageSize, pageNum);

        String response = HttpUtils.get(apiUrl, header);
        JSONObject json = JSON.parseObject(response);
        if (Objects.isNull(json) || !Objects.equals(json.get("reason"), "ok")) {
            throw new IllegalStateException("天眼查查询失败");
        }
        JSONObject data = json.getJSONObject("result");
        List<CompanyInfoVO> companyInfoVOS = JSON.parseArray(JSON.toJSONString(data.get("items")), CompanyInfoVO.class);
        if (CollectionUtils.isEmpty(companyInfoVOS)) {
            return new ArrayList<>();
        }

        return companyInfoVOS.stream().map(item -> {
            CompanyBaseInfoVO companyBaseInfoVO = new CompanyBaseInfoVO();
            companyBaseInfoVO.setUnifiedSocialCreditCode(item.getCreditCode());
            companyBaseInfoVO.setName(item.getName());
            return companyBaseInfoVO;
        }).collect(Collectors.toList());
    }

    public List<AgentVO> getAllAgentList(AgentQueryParam agentQueryParam) {
        List<AgentEntity> daoAgentEntities = agentService.list(new LambdaQueryWrapper<AgentEntity>()
                .eq(agentQueryParam.getStatus() != null, AgentEntity::getStatus, agentQueryParam.getStatus())
                .like(agentQueryParam.getAgentType() != null, AgentEntity::getAgentTypes, agentQueryParam.getAgentType())
        );
        return daoAgentEntities.stream().map(item -> {
            AgentVO agentVO = new AgentVO();
            agentVO.setId(item.getId());
            agentVO.setAgentName(item.getAgentName());
            return agentVO;
        }).collect(Collectors.toList());
    }

    /**
     * 获取代理商列表
     *
     * @param agentQueryParam 查询参数
     * @return 代理商列表
     */
    public List<AgentVO> getSelfAgentList(AgentQueryParam agentQueryParam) {
        // 参数校验
        if (agentQueryParam == null) {
            agentQueryParam = new AgentQueryParam();
        }
        Integer userId = UserThreadLocal.getUserId();
        DataAccessEntity accessEntity = dataAccessService.lambdaQuery()
                .eq(DataAccessEntity::getUserId, userId)
                .one();
        if (accessEntity == null) {
            throw new CommonException("用户未配置数据权限");
        }
        String accessType = accessEntity.getAccessType();
        if (Objects.equals(DataAccessTypeEnum.ALL.getCode(), accessType)) {
            return getAllAgentList(agentQueryParam);
        }

        // 构建查询条件
        LambdaQueryWrapper<AgentEntity> queryWrapper = buildQueryWrapper(agentQueryParam);

        // 获取数据权限过滤
        List<Integer> accessibleAgentIds = getAccessibleAgentIds(accessEntity);
        if (CollectionUtils.isEmpty(accessibleAgentIds)) {
            return Collections.emptyList();
        }
        // 如果代理商选择的是全部则返回所有代理商公司
        if (accessibleAgentIds.contains(0)) {
            return getAllAgentList(agentQueryParam);
        }
        queryWrapper.in(AgentEntity::getId, accessibleAgentIds);

        // 查询并转换
        return agentService.list(queryWrapper)
                .stream()
                .map(item -> {
                    AgentVO agentVO = new AgentVO();
                    agentVO.setId(item.getId());
                    agentVO.setAgentName(item.getAgentName());
                    return agentVO;
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取用户可访问的代理商ID列表
     */
    private List<Integer> getAccessibleAgentIds(DataAccessEntity accessEntity) {


        if (StringUtils.isBlank(accessEntity.getAgentListStr())) {
            return Collections.emptyList();
        }

        try {
            return JSON.parseArray(accessEntity.getAgentListStr(), DataAccessAgentParam.class)
                    .stream()
                    .map(DataAccessAgentParam::getId)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("解析代理商权限数据异常: {}", accessEntity.getAgentListStr(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<AgentEntity> buildQueryWrapper(AgentQueryParam param) {
        return new LambdaQueryWrapper<AgentEntity>()
                .eq(param.getStatus() != null, AgentEntity::getStatus, param.getStatus())
                .like(param.getAgentType() != null, AgentEntity::getAgentTypes, param.getAgentType())
                .orderByDesc(AgentEntity::getUpdateTime);
    }
}
