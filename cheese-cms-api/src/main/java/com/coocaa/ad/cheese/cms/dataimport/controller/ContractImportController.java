package com.coocaa.ad.cheese.cms.dataimport.controller;

import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractAttachmentEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractAttachmentService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractService;
import com.coocaa.ad.cheese.cms.common.tools.common.enums.BooleFlagEnum;
import com.coocaa.ad.cheese.cms.common.tools.common.result.ResultTemplate;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.AttachmentTypeEnum;
import com.coocaa.ad.cheese.cms.dataimport.pojo.ContractImportParam;
import com.coocaa.ad.cheese.cms.dataimport.service.ContractImportService;
import com.coocaa.ad.cheese.cms.dataimport.service.FeignSspClient;
import com.coocaa.ad.cheese.cms.dataimport.util.ParseContractExcel;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractAttachmentParam;
import com.coocaa.ad.cheese.cms.venue.convert.contract.ContractConvert;
import com.coocaa.ad.cheese.cms.venue.service.ContractApprovalService;
import com.coocaa.ad.cheese.cms.venue.service.ContractWriteService;
import com.coocaa.ad.common.anno.AuthIgnore;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Tag(name = "历史合同数据导入", description = "历史合同数据导入")
@RestController
@RequestMapping("/contract/old")
public class ContractImportController {
    @Resource
    private ContractConvert contractConvert;

    @Resource
    private IContractAttachmentService attachmentService;

    @Resource
    private IContractService contractService;

    @Autowired
    private FeignSspClient feignSspClient;
    @Autowired
    private ParseContractExcel parseContractExcel;
    @Autowired
    private ContractWriteService contractWriteService;

    @Autowired
    private ContractApprovalService contractApprovalService;

    @Autowired
    private ContractImportService contractImportService;

    @AuthIgnore
    @PostMapping("/import")
    @Operation(summary = "上传合同老数据")
    public ResultTemplate<String> uploadFile(@RequestParam(name = "fileFeishu") MultipartFile fileFeishu,
                                             @RequestParam(name = "fileOther") MultipartFile fileOther,
                                             @RequestParam(name = "fileProjectUser") MultipartFile fileContractUser) {
        try {
            parseContractExcel.importContract(fileFeishu, fileOther, fileContractUser);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException("导入失败");
        }
        return ResultTemplate.success();
    }

    @AuthIgnore
    @PostMapping("/import-v2")
    @Operation(summary = "上传合同老数据V2")
    public ResultTemplate<String> uploadFileV2(@RequestParam(name = "fileFeishu") MultipartFile fileFeishu,
                                               @RequestParam(name = "fileOther") MultipartFile fileOther) {
        try {
            parseContractExcel.importContractV2(fileFeishu, fileOther);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException("导入失败");
        }
        return ResultTemplate.success();
    }

    @AuthIgnore
    @GetMapping("/getInvalid")
    public ResultTemplate<List<ContractImportParam>> getInvalid() {
        return ResultTemplate.success(parseContractExcel.getInvalid());
    }

    @AuthIgnore
    @GetMapping("/getValid")
    public ResultTemplate<List<ContractImportParam>> getValid() {
        return ResultTemplate.success(parseContractExcel.getValid());
    }

    @AuthIgnore
    @PostMapping("/saveLocalContract")
    @Operation(summary = "生成本地合同")
    public ResultTemplate<String> saveLocalContract() {
        try {
            parseContractExcel.saveLocalContract();
            parseContractExcel.getValid().clear();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException("生成本地合同失败" + e.getMessage());
        }
        return ResultTemplate.success();
    }


    @AuthIgnore
    @GetMapping("/get")
    public ResultTemplate<Integer> getContractId(@RequestParam(name = "code") String contractCode) {
        if (StringUtils.isBlank(contractCode)) return ResultTemplate.success();
        Integer contractId = Optional.ofNullable(contractService.lambdaQuery()
                        .select(ContractEntity::getId, ContractEntity::getContractCode, ContractEntity::getFollower, ContractEntity::getCreator)
                        .eq(ContractEntity::getContractCode, contractCode)
                        .last("limit 1")
                        .one())
                .map(ContractEntity::getId).orElse(null);
        return ResultTemplate.success(contractId);
    }


    /**
     * 上传合同附件
     */
    @AuthIgnore
    @PostMapping("/{contractId}/attachments")
    public ResultTemplate<Boolean> uploadAttachments(@PathVariable(name = "contractId") Integer contractId,
                                                     @RequestBody @Validated List<ContractAttachmentParam> attachments) {
        if (CollectionUtils.isEmpty(attachments)) return ResultTemplate.fail("附件列表不能为空");

        // 查合同信息
        ContractEntity contract = contractService.lambdaQuery()
                .select(ContractEntity::getId, ContractEntity::getContractCode, ContractEntity::getFollower, ContractEntity::getCreator)
                .eq(ContractEntity::getId, contractId)
                .last("limit 1")
                .one();
        if (Objects.isNull(contract)) return ResultTemplate.fail("合同不存在");

        // 先删除数据
        attachmentService.lambdaUpdate()
                .set(ContractAttachmentEntity::getDeleteFlag, BooleFlagEnum.YES.getCode())
                .eq(ContractAttachmentEntity::getContractId, contractId)
                .update();

        // 数据转换
        List<ContractAttachmentEntity> entities = attachments.stream()
                .map(contractConvert::toEntity)
                .peek(attachment -> {
                    if (StringUtils.isBlank(attachment.getFileType()) || attachment.getFileType().length() > 10) {
                        attachment.setFileType(FilenameUtils.getExtension(attachment.getUrl()));
                    }

                    attachment.setContractId(contractId);
                    attachment.setBizId(contractId);
                    attachment.setSubType(0);
                    attachment.setType(AttachmentTypeEnum.MAIN_OTHER.getCode());
                    attachment.setDeleteFlag(BooleFlagEnum.NO.getCode());
                    attachment.setCreator(contract.getCreator());
                    attachment.setOperator(contract.getCreator());
                    attachment.setCreateTime(LocalDateTime.now());
                    attachment.setUpdateTime(LocalDateTime.now());
                }).toList();
        boolean result = attachmentService.saveBatch(entities);
        return ResultTemplate.success(result);
    }

    @AuthIgnore
    @PutMapping("/fix-project-info")
    @Hidden
    public ResultTemplate<Boolean> fixProjectInfo(@RequestParam(value = "force", defaultValue = "false") Boolean force) {
        new Thread(() -> contractWriteService.fixProjectInfo(force)).start();
//        contractWriteService.fixProjectInfo();
        return ResultTemplate.success(true);
    }


    @AuthIgnore
    @PutMapping("/fix-project-info-all")
    @Hidden
    public ResultTemplate<Boolean> fixProjectInfoAll() {
        contractWriteService.fixProjectInfoAll();
        return ResultTemplate.success(true);
    }

    @AuthIgnore
    @PutMapping("/fix-contract-level")
    @Hidden
    public ResultTemplate<Boolean> fixContractLevel() {
        contractWriteService.fixContractLevel();
        return ResultTemplate.success(true);
    }

    @AuthIgnore
    @DeleteMapping("/delete-contracts")
    @Hidden
    public ResultTemplate<Boolean> deleteContracts(@RequestBody List<Integer> ids) {
        contractWriteService.deleteContracts(ids);
        return ResultTemplate.success(true);
    }

    @AuthIgnore
    @DeleteMapping("/delete-projects")
    @Hidden
    public ResultTemplate<Boolean> deleteProjects(@RequestBody List<Integer> ids) {
        contractWriteService.deleteProjects(ids);
        return ResultTemplate.success(true);
    }

    /**
     * 合同审批通过补偿历史数据 设置项目初始签约数
     */
    @Operation(summary = "合同审批通过 设置项目初始签约数")
    @PutMapping("/init-sign-count")
    @AuthIgnore
    @Hidden
    public ResultTemplate<Boolean> approvePassedInitSignCount() {
        CompletableFuture.runAsync(() -> {
            contractWriteService.initialProjectsFirstSignCount();
        });
        return ResultTemplate.success();
    }

    @AuthIgnore
    @PostMapping("/import-abnormal")
    @Operation(summary = "导入异常申请")
    public ResultTemplate<String> uploadAbnormal(@RequestParam(name = "abnormal") MultipartFile abnormal) {
        try {
            parseContractExcel.importContractAbnormal(abnormal);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException("导入失败");
        }
        return ResultTemplate.success();
    }

    @AuthIgnore
    @PutMapping("/fix-abnormal-city-region")
    public ResultTemplate<Boolean> fixAbnormalCityRegion() {
        parseContractExcel.fixAbnormalCityRegion();
        return ResultTemplate.success(true);
    }


    /**
     * 清洗合同申请时间
     */
    @Operation(summary = "清洗合同申请时间")
    @GetMapping("/cleaning-application-time")
    @AuthIgnore
    @Hidden
    public ResultTemplate<Boolean> cleaningApplicationTime(@RequestParam(required = false) Integer contractType
            , @RequestParam(required = false) Integer id
            , @RequestParam(required = false) Integer businessType) {
        contractApprovalService.cleaningApplicationTime(contractType, id, businessType);
        return ResultTemplate.success();
    }

    @AuthIgnore
    @GetMapping("/fix-payment-period")
    public ResultTemplate<Boolean> fixPaymentPeriod(@RequestParam(value = "contractCode", required = false) String contractCode) {
        new Thread(() -> contractImportService.fixPaymentPeriod(contractCode)).start();
        return ResultTemplate.success(true);
    }
}
