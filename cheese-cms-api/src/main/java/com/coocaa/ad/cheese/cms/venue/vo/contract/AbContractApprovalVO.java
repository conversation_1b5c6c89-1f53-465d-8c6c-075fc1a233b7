package com.coocaa.ad.cheese.cms.venue.vo.contract;

import com.coocaa.ad.cheese.cms.common.tools.venue.constant.VenueConstants;
import com.coocaa.ad.cheese.cms.common.util.translate.VenueTransTypes;
import com.coocaa.ad.translate.anno.TransField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-24
 */
@Data
public class AbContractApprovalVO {
    @Schema(description = "异常合同id")
    private Integer abContractId;

    @Schema(description = "类型名称")
    private String typeName;

    @Schema(description = "异常合同编号")
    private String abContractCode;

    @Schema(description = "原合同id")
    private Integer contractId;

    @Schema(description = "原合同编号")
    private String code;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "状态(字典0102)")
    @TransField(type = VenueTransTypes.DICT)
    private String applyStatus;
    private String applyStatusName;

    @Schema(description = "供应商")
    private String agentName;

    @Schema(description = "合同金额(元)")
    private BigDecimal totalAmount;

    @Schema(description = "合同年限")
    private BigDecimal period;

    @Schema(description = "异常点位数")
    private Integer abnormalCount;

    @Schema(description = "申请人")
    @TransField(type = VenueTransTypes.USER)
    private Integer creator;
    private String creatorName;

    @Schema(description = "申请时间")
    private LocalDateTime applyTime;

    @Schema(description = "跟进人")
    @TransField(type = VenueTransTypes.USER)
    private Integer follower;
    private String followerName;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = VenueConstants.DATE_TIME_FORMAT)
    private LocalDateTime createTime;

    @Schema(description = "当前审批节点code")
    private String nodeCode;

    @Schema(description = "当前审批节点")
    private String nodeName;

    @Schema(description = "当前审批人")
    @TransField(type = VenueTransTypes.USER, target = "approveName")
    private Integer userId;
    private String approveName;

    @Schema(description = "是否是普通用户")
    private Boolean ordinaryUser;
}
