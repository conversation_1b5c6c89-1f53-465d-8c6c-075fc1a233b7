package com.coocaa.ad.cheese.cms.venue.rpc;

import com.coocaa.ad.cheese.cms.common.tools.common.result.ResultTemplate;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractAgentParam;
import com.coocaa.ad.cheese.cms.venue.vo.building.BuildingDetailsVO;
import com.coocaa.ad.cheese.cms.venue.vo.building.BuildingRatingDTO;
import com.coocaa.ad.cheese.cms.venue.vo.building.PointDto;
import com.coocaa.ad.cheese.cms.venue.vo.user.SysUserApproveVO;
import com.coocaa.ad.common.config.FeignConfig;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;

/**
 * 楼盘点位信息
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-16
 */
@FeignClient(value = "cheese-meht-web-api", path = "/api/meht", configuration = FeignConfig.class)
public interface FeignMethWebRpc {

    /**
     * 根据点位编码获取点位详情
     *
     * @param pointCodes 点位编码列表
     * @return 点位详情列表
     */
    @PostMapping("/point/codes")
    ResultTemplate<List<PointDto>> listPointByCodes(@RequestBody Collection<String> pointCodes);

    /**
     * 根据点位编码获取点位尺寸
     *
     * @param pointCodes 点位编码列表
     * @return 点位尺寸列表
     */
    @PostMapping("/point/size/codes")
    ResultTemplate<List<PointDto>> listPointSizeByCodes(@RequestBody Collection<String> pointCodes);

    /**
     * 检查商机是否有效
     *
     * @param buildingNo 楼宇编码
     * @return true:有效
     */
    @GetMapping("/point/business/{buildingNo}/available")
    ResultTemplate<Boolean> isBusinessAvailable(@PathVariable("buildingNo") String buildingNo);

    /**
     * 根据商机查询点位列表
     *
     * @param businessCodes 商机编码 (多个使用 "," 隔开)
     * @return 点位列表
     */
    @GetMapping("/point/contract-inspection-points")
    ResultTemplate<List<String>> getPointsByBusinessCodes(@RequestParam(value = "businessCodes") String businessCodes);


    /**
     * 审批用户信息
     * @param userCode 用户编码
     */
    @GetMapping("/user/approve")
    ResultTemplate<SysUserApproveVO> getUserDetail(@RequestParam(value = "userCode",required = false) String userCode);

    /**
     * 查询审批人供应商配置表
     *
     * @param agentParam 请求参数
     */
    @PostMapping("/personnel/approval/agent/list")
    ResultTemplate<List<SysUserApproveVO>> getAgents(@RequestBody ContractAgentParam agentParam);

    /**
     * 根据商机编码获取楼宇详情
     */
    @GetMapping("/building/rating/simple-details")
    ResultTemplate<List<BuildingRatingDTO>> listBuildingRatings(@RequestParam(name = "businessCodes", required = false) List<String> businessCodes,
                                                                @RequestParam(name = "buildingCodes", required = false) List<String> buildingCodes);


    /**
     * 获取获取评分详情
     *
     * @param buildingNo 楼宇编码
     * @return
     */
    @GetMapping("/building/rating/details/{buildingNo}")
    ResultTemplate<BuildingDetailsVO> getBuildingDetails(@PathVariable("buildingNo") String buildingNo);

    /**
     * 同步用户标签
     *
     * @param empCode 工号
     * @return
     */
    @GetMapping("/user-tags/{empCode}/init")
    @Operation(summary = "初始化用户标签")
    ResultTemplate<Boolean> initUserTag(@PathVariable("empCode") String empCode);

}
