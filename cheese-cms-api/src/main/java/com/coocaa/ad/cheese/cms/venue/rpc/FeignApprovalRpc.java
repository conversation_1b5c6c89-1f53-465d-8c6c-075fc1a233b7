package com.coocaa.ad.cheese.cms.venue.rpc;

import com.coocaa.ad.cheese.cms.common.tools.common.result.ResultTemplate;
import com.coocaa.ad.cheese.cms.venue.rpc.bean.ApprovalInitiateParam;
import com.coocaa.ad.cheese.cms.venue.rpc.bean.ApprovalResubmitParam;
import com.coocaa.ad.cheese.cms.venue.rpc.bean.ApprovalTaskListParam;
import com.coocaa.ad.cheese.cms.venue.rpc.bean.ApprovalTaskOperateParam;
import com.coocaa.ad.cheese.cms.venue.rpc.bean.ApprovalTaskRollbackParam;
import com.coocaa.ad.cheese.cms.venue.rpc.bean.ApprovalTaskUserListParam;
import com.coocaa.ad.cheese.cms.venue.rpc.vo.ApprovalNodeVO;
import com.coocaa.ad.cheese.cms.venue.rpc.vo.ApprovalRollbackVO;
import com.coocaa.ad.cheese.cms.venue.rpc.vo.ApprovalTaskListVO;
import com.coocaa.ad.cheese.cms.venue.rpc.vo.ApprovalTimelineVO;
import com.coocaa.ad.common.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 审批系统接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-04
 */
@FeignClient(
//        name = "cheese-authority-api",
        value = "cheese-authority-api", path = "/sys/approve",
//        url = "http://beta-cheese-ssp.coocaa.com",
        configuration = FeignConfig.class)
public interface FeignApprovalRpc {

    /**
     * 发起审批
     */
    @PostMapping("/instance/initiate")
    ResultTemplate<String> initiateApproval(@RequestBody ApprovalInitiateParam param);

    /**
     * 撤回审批实例
     */
    @PostMapping("/instance/cancel")
    ResultTemplate<String> cancelApproval(@RequestBody ApprovalInitiateParam param);

    /**
     * 用户任务列表(分页)
     */
    @PostMapping("/task/list")
    ResultTemplate<ApprovalTaskListVO> listApprovalTasks(@RequestBody ApprovalTaskListParam param);

    /**
     * 用户任务列表(不分页)
     */
    @PostMapping("/task/list/no-page")
    ResultTemplate<List<ApprovalTaskListVO.Task>> listAllApprovalTasks(@RequestBody ApprovalTaskListParam param);

    /**
     * 查询用户任务列表（不分页）（本接口无延迟，但是只能查询指定用户的已办或者待办，且没有任务开始时间）
     */
    @PostMapping("/user/task/list/no-page")
    ResultTemplate<List<ApprovalTaskListVO.Task>> listAllUserApprovalTasks(@RequestBody ApprovalTaskUserListParam param);

    /**
     * 同意审批
     */
    @PostMapping("/task/agree")
    ResultTemplate<String> agreeApproval(@RequestBody ApprovalTaskOperateParam param);

    /**
     * 拒绝审批
     */
    @PostMapping("/task/reject")
    ResultTemplate<String> rejectApproval(@RequestBody ApprovalTaskOperateParam param);

    /**
     * 获取可退回节点
     */
    @GetMapping("/rollback-root")
    ResultTemplate<List<ApprovalRollbackVO>> getRollbackRoot(@RequestParam("processCode") String processCode,
                                                             @RequestParam("approveType") String approveType);

    /**
     * 任务退回
     */
    @PostMapping("/task/rollback")
    ResultTemplate<String> rollbackApproval(@RequestBody ApprovalTaskRollbackParam param);

    /**
     * 退回到提交人后重新发起
     */
    @PostMapping("/task/resubmit")
    ResultTemplate<String> resubmitApproval(@RequestBody ApprovalResubmitParam param);

    /**
     * 查询审批节点名称和自定义ID
     */
    @GetMapping("/task/node")
    ResultTemplate<ApprovalNodeVO> getTaskNode(@RequestHeader("Token") String token, @RequestParam("processCode") String processCode,
                                               @RequestParam("approveType") String approveType, @RequestParam("userFlag") Boolean userFlag);

    /**
     * 审批动态
     */
    @GetMapping("/time/line")
    ResultTemplate<List<ApprovalTimelineVO>> getApprovalTimeline(@RequestParam("processCode") String processCode,
                                                                 @RequestParam("approveType") String approveType);
} 