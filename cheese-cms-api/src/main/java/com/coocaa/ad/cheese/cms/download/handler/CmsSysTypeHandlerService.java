package com.coocaa.ad.cheese.cms.download.handler;

import com.coocaa.ad.cheese.cms.download.enums.DownLoadTypeEnum;
import com.coocaa.ad.downloader.core.SysTypeHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 *
 *  系统类型
 * <AUTHOR>
 * @since 1.1.0
 */
@Slf4j
@Service
public class CmsSysTypeHandlerService implements SysTypeHandler {
    @Override
    public String getSysType() {
        return DownLoadTypeEnum.SYS_TYPE;
    }
}
