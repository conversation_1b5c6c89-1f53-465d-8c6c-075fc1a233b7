package com.coocaa.ad.cheese.cms.venue.service;

import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractAttachmentEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractLogEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractAttachmentService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractLogService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractService;
import com.coocaa.ad.cheese.cms.common.tools.common.enums.BooleFlagEnum;
import com.coocaa.ad.cheese.cms.common.tools.common.exception.CommonException;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.AttachmentTypeEnum;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractAttachmentParam;
import com.coocaa.ad.cheese.cms.venue.convert.contract.ContractConvert;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractAttachmentVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractAttachmentWrapVO;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 合同附件管理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-09
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ContractAttachmentService {
    private final ContractConvert contractConvert;
    private final IContractService contractService;
    private final IContractAttachmentService attachmentService;
    private final IContractLogService contractLogService;

    /**
     * 查询合同所有附件
     */
    public List<ContractAttachmentWrapVO> listAttachments(Integer contractId, Collection<Integer> types) {
        List<ContractAttachmentEntity> entities = attachmentService.lambdaQuery()
                .in(CollectionUtils.isNotEmpty(types), ContractAttachmentEntity::getType, types)
                .eq(ContractAttachmentEntity::getContractId, contractId)
                .eq(ContractAttachmentEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .list();

        // 没有数据直接返回
        if (CollectionUtils.isEmpty(entities)) {
            return Collections.emptyList();
        }

        // 根据类型分组
        Map<Integer, List<ContractAttachmentVO>> attachmentMap = entities.stream()
                .map(contractConvert::toVO).peek(vo -> vo.setTypeName(AttachmentTypeEnum.getDesc(vo.getType())))
                .collect(Collectors.groupingBy(ContractAttachmentVO::getType));

        List<ContractAttachmentWrapVO> wraps = Lists.newArrayListWithExpectedSize(attachmentMap.size());
        attachmentMap.forEach((type, vos) -> {
            ContractAttachmentWrapVO vo = new ContractAttachmentWrapVO();
            vo.setType(type);
            vo.setTypeName(AttachmentTypeEnum.getDesc(type));
            vo.setAttachments(vos);
            wraps.add(vo);
        });

        return wraps;
    }

    /**
     * 上传合同附件
     */
    public boolean uploadContractAttachments(Integer contractId, List<ContractAttachmentParam> attachments) {
        if (CollectionUtils.isEmpty(attachments)) return false;

        // 数据转换
        List<ContractAttachmentEntity> entities = attachments.stream()
                .map(contractConvert::toEntity)
                .peek(attachment -> {
                    attachment.setContractId(contractId);
                    attachment.setBizId(contractId);
                    attachment.setSubType(0);
                    attachment.setDeleteFlag(BooleFlagEnum.NO.getCode());
                }).toList();
        return attachmentService.saveBatch(entities);
    }

    /**
     * 上传合同原件
     */
    public boolean uploadContractSealedAttachments(Integer contractId, List<ContractAttachmentParam> attachments) {
        ContractEntity contract = Optional.ofNullable(contractService.getById(contractId))
                .orElseThrow(() -> new CommonException("合同不存在"));

        // 只有甲乙方都盖章后才能上传附件
        if (!BooleFlagEnum.isYes(contract.getSealParty1Flag())) {
            throw new CommonException("甲方未盖章，甲方完成盖章才可以上传附件");
        }

        if (!BooleFlagEnum.isYes(contract.getSealParty2Flag())) {
            throw new CommonException("乙方未盖章，乙方完成盖章才可以上传附件");
        }

        // 如果是三方合同，必须丙方也要盖章
        if (BooleFlagEnum.isYes(contract.getThirdFlag()) && !BooleFlagEnum.isYes(contract.getSealParty3Flag())) {
            throw new CommonException("丙方未盖章，丙方完成盖章才可以上传附件");
        }

        // 数据转换
        List<ContractAttachmentEntity> entities = attachments.stream()
                .map(contractConvert::toEntity)
                .peek(attachment -> {
                    attachment.setContractId(contractId);
                    attachment.setBizId(contractId);
                    attachment.setSubType(0);
                    attachment.setDeleteFlag(BooleFlagEnum.NO.getCode());
                }).toList();
        boolean result = attachmentService.saveBatch(entities);
        if (!result) throw new CommonException("上传双方盖章的合同扫描件失败");

        Map<Integer, String> attachmentMap = attachments.stream()
                .collect(Collectors.groupingBy(ContractAttachmentParam::getType,
                        Collectors.mapping(ContractAttachmentParam::getName, Collectors.joining(","))));

        // 更新合同附件上传状态
        result = contractService.lambdaUpdate()
                .set(attachmentMap.containsKey(AttachmentTypeEnum.MAIN_SEALED.getCode()), ContractEntity::getUploadFlag, BooleFlagEnum.YES.getCode())
                .set(attachmentMap.containsKey(AttachmentTypeEnum.SUB_SEALED.getCode()), ContractEntity::getUploadSubFlag, BooleFlagEnum.YES.getCode())
                .eq(ContractEntity::getId, contractId).update();


        // 上传成功，记录操作日志
        if (result) {
            StringJoiner logMsg = new StringJoiner("; ");
            attachmentMap.forEach((type, fileNames) -> {
                logMsg.add(String.format("%s: %s", AttachmentTypeEnum.getDesc(type), fileNames));
            });
            saveContractLog(contractId, logEntity -> logEntity.setContent("上传附件。" + logMsg));
        }

        return result;
    }

    /**
     * 删除附件
     */
    public boolean deleteContractAttachments(Integer contractId, List<Integer> attachmentIds) {
        // 已经存在的合同附件类型, key:附件类型, val:附件id集合
        Map<Integer, Set<Integer>> existedTypeMap = attachmentService.lambdaQuery()
                .select(ContractAttachmentEntity::getId, ContractAttachmentEntity::getType)
                .eq(ContractAttachmentEntity::getContractId, contractId)
                .eq(ContractAttachmentEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .in(CollectionUtils.isNotEmpty(attachmentIds), ContractAttachmentEntity::getId, attachmentIds)
                .list().stream()
                .collect(Collectors.groupingBy(ContractAttachmentEntity::getType,
                        Collectors.mapping(ContractAttachmentEntity::getId, Collectors.toSet())));

        // 标记附件为删除
        boolean result = attachmentService.lambdaUpdate()
                .set(ContractAttachmentEntity::getDeleteFlag, BooleFlagEnum.YES.getCode())
                .eq(ContractAttachmentEntity::getContractId, contractId)
                .in(CollectionUtils.isNotEmpty(attachmentIds), ContractAttachmentEntity::getId, attachmentIds)
                .update();

        // 删除成功，记录操作日志
        if (result) {
            // 处理合同附件上传状态
            boolean mainSealedDeleted = CollectionUtils.containsAny(attachmentIds,
                    existedTypeMap.getOrDefault(AttachmentTypeEnum.MAIN_SEALED.getCode(), Collections.emptySet()));
            boolean subSealedDeleted = CollectionUtils.containsAny(attachmentIds,
                    existedTypeMap.getOrDefault(AttachmentTypeEnum.SUB_SEALED.getCode(), Collections.emptySet()));
            if (mainSealedDeleted || subSealedDeleted) {
                result = contractService.lambdaUpdate()
                        .set(mainSealedDeleted, ContractEntity::getUploadFlag, BooleFlagEnum.NO.getCode())
                        .set(subSealedDeleted, ContractEntity::getUploadSubFlag, BooleFlagEnum.NO.getCode())
                        .eq(ContractEntity::getId, contractId)
                        .update();
            }

            // 根据类型分组
            Map<Integer, List<ContractAttachmentEntity>> attachmentMap = attachmentService.lambdaQuery()
                    .select(ContractAttachmentEntity::getId, ContractAttachmentEntity::getType, ContractAttachmentEntity::getName)
                    .eq(ContractAttachmentEntity::getContractId, contractId)
                    .in(CollectionUtils.isNotEmpty(attachmentIds), ContractAttachmentEntity::getId, attachmentIds)
                    .list().stream().collect(Collectors.groupingBy(ContractAttachmentEntity::getType));

            StringJoiner logMsg = new StringJoiner("; ");
            attachmentMap.forEach((type, vos) -> {
                logMsg.add(String.format("%s: %s", AttachmentTypeEnum.getDesc(type),
                        vos.stream().map(ContractAttachmentEntity::getName).collect(Collectors.joining(","))));
            });
            saveContractLog(contractId, logEntity -> logEntity.setContent("删除附件。" + logMsg));
        }

        return result;
    }


    /**
     * 保存合同操作日志
     */
    private boolean saveContractLog(Integer contractId, Consumer<ContractLogEntity> consumer) {
        ContractLogEntity entity = new ContractLogEntity();
        entity.setContractId(contractId);
        Optional.ofNullable(consumer).ifPresent(c -> c.accept(entity));
        return contractLogService.save(entity);
    }

    /**
     * 上传补充协议附件
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean uploadAgreementAttachments(Integer contractId, List<ContractAttachmentParam> attachments) {
        ContractEntity contract = Optional.ofNullable(contractService.getById(contractId))
                .orElseThrow(() -> new CommonException("补充协议不存在"));
        if (BooleFlagEnum.isYes(contract.getArchiveFlag())) {
            throw new CommonException("补充协议已归档，不需要重复操作");
        }

        // 上传附件
        if (!uploadContractAttachments(contractId, attachments)) {
            return false;
        }
        // 更新补充协议的归档状态
        boolean b = contractService.lambdaUpdate()
                .set(ContractEntity::getArchiveFlag, BooleFlagEnum.YES.getCode())
                .set(ContractEntity::getArchiveTime, LocalDateTime.now())
                .set(ContractEntity::getArchiveRemark, "补充协议上传双方盖章的合同扫描件")
                .eq(ContractEntity::getId, contractId)
                .eq(ContractEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .update();
        if (!b) {
            throw new CommonException("更新补充协议的归档状态失败");
        }

        try {
            // 记录日志
            Integer userId = null;
            try {
                userId = UserThreadLocal.getUserId();
            } catch (Exception e) {
                log.error("获取用户ID失败", e);
            }
            ContractLogEntity entity = new ContractLogEntity();
            entity.setContractId(contractId);
            entity.setContent("上传补充协议归档附件");
            entity.setCreator(userId);
            contractLogService.save(entity);
        } catch (Exception e) {
            log.error("上传补充协议归档附件,保存合同操作日志失败", e);
        }

        return true;
    }

}
