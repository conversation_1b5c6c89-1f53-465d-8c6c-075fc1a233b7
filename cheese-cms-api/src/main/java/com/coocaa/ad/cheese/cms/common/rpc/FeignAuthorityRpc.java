package com.coocaa.ad.cheese.cms.common.rpc;

import com.coocaa.ad.cheese.cms.common.tools.common.bean.common.CityDetailVO;
import com.coocaa.ad.cheese.cms.common.tools.common.bean.common.CodeNameVO;
import com.coocaa.ad.cheese.cms.common.tools.common.bean.common.UserFeishuVO;
import com.coocaa.ad.cheese.cms.common.tools.common.bean.common.UserVO;
import com.coocaa.ad.cheese.cms.common.tools.common.result.ResultTemplate;
import com.coocaa.ad.cheese.cms.venue.vo.building.DictCodeVO;
import com.coocaa.ad.common.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 调用权限系统接口
 *
 * <AUTHOR>
 * @since 2024/11/1
 */
@FeignClient(value = "cheese-authority-api",
        // url = "http://beta-cheese-ssp.coocaa.com",
        configuration = FeignConfig.class)
public interface FeignAuthorityRpc {
    /**
     * 获取当前登陆用户信息
     * 如果传了工号，则优先查询工号对应的用户信息
     */
    @GetMapping("/sys/user/info?encrypt=false")
    ResultTemplate<UserVO> getUserInfo(@RequestParam(name = "wno") String wno);

    /**
     * 根据用户ID查询用户信息
     *
     * @param userIds 用户IDs
     * @return 用户名字列表
     */
    @PostMapping("/sys/user/list/ids")
    ResultTemplate<List<UserVO>> getUserByIds(@RequestBody List<Integer> userIds);

    /**
     * 根据用户工号查询用户信息
     *
     * @param userWnos 用户工号列表
     * @return 用户名字列表
     */
    @PostMapping("/sys/user/list/wnos")
    ResultTemplate<List<UserVO>> getUserByWnos(@RequestBody List<String> userWnos);

    /**
     * 根据用户工号查询用户信息
     *
     * @param names 用户姓名列表
     * @return 用户名字列表
     */
    @PostMapping("/sys/user/list/names")
    ResultTemplate<List<UserVO>> getUserByNames(@RequestBody List<String> names);

    /**
     * 根据城市名称获取城市
     *
     * @param name 城市名称
     * @return 城市
     */
    @GetMapping("/sys/city/name/{name}")
    ResultTemplate<CodeNameVO> getCityByName(@PathVariable(name = "name") String name);

    /**
     * 根据城市ID获取城市详情
     *
     * @param cityId 城市ID
     * @return 城市详情
     */
    @GetMapping("/sys/city/bz-code/city/{cityId}")
    ResultTemplate<CityDetailVO> getCityDetail(@PathVariable(name = "cityId") Integer cityId);


    /**
     * 根据ID获取城市名称
     *
     * @param ids 城市ID列表
     * @return 城市名称列表
     */
    @PostMapping("/sys/city/list/ids")
    ResultTemplate<List<CodeNameVO>> listCityByIds(@RequestBody List<Integer> ids);


    /**
     * 根据字典编码获取字典名称
     *
     * @param codes 字典编码列表
     * @return 字典名称列表
     */
    @PostMapping("/sys/dict/list/codes")
    ResultTemplate<List<CodeNameVO>> listDictByCodes(@RequestBody List<String> codes);

    /**
     * 根据父编码获取字典名称
     *
     * @param code 父字典编码
     * @return 字典名称列表
     */
    @GetMapping("/sys/dict/select/{code}")
    ResultTemplate<List<CodeNameVO>> listDictByParent(@PathVariable("code") String code);

    /**
     * 查询所有已启用的城市列表
     */
    @GetMapping("/sys/city/list/select")
    ResultTemplate<List<CodeNameVO>> getAvailableCities();

    /**
     * 查询用户拥有的城市列表
     */
    @GetMapping("/sys/city/list/select/auth")
    ResultTemplate<List<CodeNameVO>> listCityByAuthority();

    /**
     * 查询用户拥有的城市列表
     * 如果是全部，仅返回一条 id = 0的数据
     */
    @GetMapping("/sys/user/city")
    ResultTemplate<List<CodeNameVO>> listUserCities();

    /**
     * 查询数据权限用户
     */
    @GetMapping("/sys/user/data-user")
    ResultTemplate<List<CodeNameVO>> listDataUser(@RequestParam(name = "keyword", required = false) String keyword,
                                                  @RequestParam(name = "size", required = false) Integer size);

    /**
     * 根据用户ID查询用户名字
     *
     * @param userIds 用户IDs
     * @return 用户名字列表
     */
    @PostMapping("/sys/user/list/ids")
    ResultTemplate<List<CodeNameVO>> listUserByIds(@RequestBody List<Integer> userIds);

    /**
     * 根据行业编码获取行业名称
     *
     * @param codes 行业编码
     * @return 行业名称列表
     */
    @PostMapping("/sys/industry/list/codes")
    ResultTemplate<List<CodeNameVO>> listIndustryByCodes(@RequestBody List<String> codes);

    /**
     * 查询登陆用户允许查看其他用户的数据范围
     * 1. 超管，集合中仅有一条为0的数据
     * 2. 其它情况，集合中包含可以查看的用户ID
     *
     * @return 用户ID列表
     */
    @GetMapping("/sys/data/permission/detail")
    ResultTemplate<List<Integer>> getScopedUserIds();

    /**
     * 获取自己及下属
     *
     * @param userId 指定用户ID
     * @return 用户ID列表
     */
    @GetMapping("/sys/data/permission/subordinates")
    ResultTemplate<List<Integer>> getSubordinates(@RequestParam(name = "userId") Integer userId);

    @PostMapping("/sys/user/feishu/getUserFeishuList")
    ResultTemplate<List<UserFeishuVO>> getUserFeishuList(@RequestBody List<Integer> ids);

    /**
     * 查询启用的城市,城市组等查询使用,这里是全量城市
     */
    @GetMapping("/sys/city/list/select")
    ResultTemplate<List<DictCodeVO>> selectList();
}