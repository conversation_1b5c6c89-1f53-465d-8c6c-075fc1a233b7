package com.coocaa.ad.cheese.cms.venue.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractDepositSupplierEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractDeviceEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractPaymentPeriodEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractProjectEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractSubEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.LedgerEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractDepositSupplierService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractDeviceService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractLogService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractPaymentPeriodService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractProjectService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractSubService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.ILedgerService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.supplier.ISupplierBankService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.supplier.ISupplierService;
import com.coocaa.ad.cheese.cms.common.tools.common.cos.ObjectUtils;
import com.coocaa.ad.cheese.cms.common.tools.common.enums.BooleFlagEnum;
import com.coocaa.ad.cheese.cms.common.tools.common.exception.CommonException;
import com.coocaa.ad.cheese.cms.common.tools.common.result.PageRequestVo;
import com.coocaa.ad.cheese.cms.common.tools.common.result.PageResponseVo;
import com.coocaa.ad.cheese.cms.common.tools.common.util.BigDecimalUtils;
import com.coocaa.ad.cheese.cms.common.tools.venue.bean.UserDataAccessV2DTO;
import com.coocaa.ad.cheese.cms.common.tools.venue.constant.VenueConstants;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractHistoryTypeEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractStatusEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractTypeEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.FeeTypeEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.LedgerDelTypeEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.PaymentTypeEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.UnPaidAmountEnum;
import com.coocaa.ad.cheese.cms.common.util.CodeNameHelper;
import com.coocaa.ad.cheese.cms.download.enums.DownLoadTypeEnum;
import com.coocaa.ad.cheese.cms.venue.bean.contract.BatchContractTransferParam;
import com.coocaa.ad.cheese.cms.venue.bean.ledger.LedgerEditParam;
import com.coocaa.ad.cheese.cms.venue.bean.ledger.LedgerExcelParam;
import com.coocaa.ad.cheese.cms.venue.bean.ledger.LedgerQueryParam;
import com.coocaa.ad.cheese.cms.venue.bean.supplier.ImportResult;
import com.coocaa.ad.cheese.cms.venue.convert.ledger.LedgerConvert;
import com.coocaa.ad.cheese.cms.venue.vo.ledger.LedgerExcelVO;
import com.coocaa.ad.cheese.cms.venue.vo.ledger.LedgerVO;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;


@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class LedgerService {

    /**
     * 供应商导入模板地址
     */
    @Value("${ledger.excel.template:https://test-1330579985.cos.ap-guangzhou.myqcloud.com/cms/supplier/2024/12/16/3ff1b1a3-67d1-407e-8ba0-3b7f13059885.xls}")
    private String ledgerExcelTemplateUrl;

    private final ILedgerService ledgerService;
    private final IContractService contractService;
    private final IContractSubService contractSubService;
    private final IContractPaymentPeriodService contractPaymentPeriodService;
    private final IContractProjectService contractProjectService;
    private final IContractDeviceService contractDeviceService;
    private final IContractDepositSupplierService contractDepositSupplierService;
    private final ISupplierService supplierService;
    private final ISupplierBankService supplierBankService;
    private final LedgerConvert ledgerConvert;
    private final DataAccessService dataAccessService;
    private final CodeNameHelper codeNameHelper;
    private final IContractLogService contractLogService;

    /**
     * 根据合同ID生成台账信息
     *
     * @param contractId 合同ID
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void createLedger(Integer contractId) {
        log.info("生成台账开始，contractId:{}", contractId);
        try {
            // 根据合同组装台账信息
            List<LedgerEntity> ledgerList = getLedgerInfo(contractId);
            if (CollectionUtils.isEmpty(ledgerList)) {
                log.info("台账信息为空，contractId:{}", contractId);
                return;
            }

            // 处理历史台账
            processExistLedger(contractId, ledgerList);

            // 批量保存台账
            ledgerService.saveOrUpdateBatch(ledgerList);

            // 记录日志
            Integer userId = null;
            try {
                userId = UserThreadLocal.getUserId();
            } catch (Exception e) {
                log.error("获取用户ID失败", e);
            }
            String ledgerDateLog = ledgerList.stream().map(e -> "%s / %s".formatted(e.getStartDate(), e.getEndDate()))
                    .collect(Collectors.joining(", "));
            String logContent = "生成%s。新增了【%s】的%s记录".formatted(ContractHistoryTypeEnum.LEDGER.getDesc(), ledgerDateLog, ContractHistoryTypeEnum.LEDGER.getDesc());
            contractLogService.saveCreateOrRemoveLogAsync(contractId, logContent, ContractHistoryTypeEnum.LEDGER, userId, null, ledgerList);

            log.info("生成台账成功，contractId:{}", contractId);
        } catch (Exception e) {
            log.error("生成台账失败，contractId:{}", contractId);
            log.error("异常堆栈", e);
            throw new RuntimeException("生成台账失败" + e.getMessage());
        }
    }

    /**
     * 处理已存在台账
     *
     * @param contractId
     */
    private void processExistLedger(Integer contractId, List<LedgerEntity> ledgerList) {
        log.info("处理已存在台账开始，contractId:{}", contractId);
        List<LedgerEntity> daoLedgers = ledgerService.lambdaQuery().eq(LedgerEntity::getContractId, contractId)
                .eq(LedgerEntity::getDelFlag, BooleFlagEnum.NO.getCode()).list();
        if (CollectionUtils.isEmpty(daoLedgers)) {
            log.info("台账不存在无需处理，contractId:{}", contractId);
            return;
        }

        if (CollectionUtils.isEmpty(ledgerList)) {
            log.info("变更后合同没有台账信息，则逻辑删除原台账，contractId:{}", contractId);
            ledgerService.lambdaUpdate().eq(LedgerEntity::getContractId, contractId)
                    .set(LedgerEntity::getDelFlag, BooleFlagEnum.YES.getCode()).update();
            return;
        }

        // 根据费用类型分组
        Map<String, List<LedgerEntity>> daoFeeTypeMap = daoLedgers.stream()
                .collect(Collectors.groupingBy(bean -> bean.getFeeType()));
        Map<String, List<LedgerEntity>> paramFeeTypeMap = ledgerList.stream()
                .collect(Collectors.groupingBy(bean -> bean.getFeeType()));


        // 付款周期台账
        processExistPeriodLedger(contractId, getPeriodLedgers(daoFeeTypeMap), getPeriodLedgers(paramFeeTypeMap));

        // 押金台账
        processExistDepositLedger(contractId, daoFeeTypeMap.get(FeeTypeEnum.DEPOSIT.getCode()), paramFeeTypeMap.get(FeeTypeEnum.DEPOSIT.getCode()));

        // 意向金台账
        processExistIntentionLedger(contractId, daoFeeTypeMap.get(FeeTypeEnum.INTENTION.getCode()), paramFeeTypeMap.get(FeeTypeEnum.INTENTION.getCode()));

    }

    /**
     * 获取付款周期台账
     *
     * @param feeTypeMap
     * @return
     */
    private static List<LedgerEntity> getPeriodLedgers(Map<String, List<LedgerEntity>> feeTypeMap) {
        List<LedgerEntity> daoPeriodLedger = new ArrayList<>();
        List<LedgerEntity> rentalEntities = feeTypeMap.get(FeeTypeEnum.RENTAL.getCode());
        if (CollectionUtils.isNotEmpty(rentalEntities)) {
            daoPeriodLedger.addAll(rentalEntities);
        }
        List<LedgerEntity> electricEntities = feeTypeMap.get(FeeTypeEnum.ELECTRIC.getCode());
        if (CollectionUtils.isNotEmpty(electricEntities)) {
            daoPeriodLedger.addAll(electricEntities);
        }

        List<LedgerEntity> serviceEntities = feeTypeMap.get(FeeTypeEnum.SERVICE.getCode());
        if (CollectionUtils.isNotEmpty(serviceEntities)) {
            daoPeriodLedger.addAll(serviceEntities);
        }
        return daoPeriodLedger;
    }

    /**
     * 处理意向金台账
     *
     * @param contractId
     * @param daoIntentionLedgers
     * @param paramIntentionLedgers
     */
    private void processExistIntentionLedger(Integer contractId, List<LedgerEntity> daoIntentionLedgers, List<LedgerEntity> paramIntentionLedgers) {
        log.info("处理意向金台账开始，contractId:{}", contractId);
        if (CollectionUtils.isEmpty(daoIntentionLedgers)) {
            log.info("合同ID:{}，无意向金台账，无需处理历史数据", contractId);
            return;
        }

        // 变更后合同为空，则逻辑删除历史数据
        if (CollectionUtils.isEmpty(paramIntentionLedgers)) {
            log.info("合同ID:{}，无意向金台账台账，逻辑删除历史数据", contractId);
            ledgerService.lambdaUpdate().eq(LedgerEntity::getContractId, contractId)
                    .eq(LedgerEntity::getFeeType, FeeTypeEnum.INTENTION.getCode())
                    .set(LedgerEntity::getDelFlag, BooleFlagEnum.YES.getCode()).update();
            return;
        }

        // 更新已存在的台账
        LedgerEntity intentionLedger = paramIntentionLedgers.get(0);
        LedgerEntity daoIntentionLedger = daoIntentionLedgers.get(0);
        BigDecimal paidAmount = daoIntentionLedger.getPaidAmount();
        if (BigDecimalUtils.gt(paidAmount, BigDecimal.ZERO)) {
            log.info("合同ID:{}，已存在意向金台账，更新台账信息", contractId);
            intentionLedger.setId(daoIntentionLedger.getId());
            intentionLedger.setPaidAmount(paidAmount);
            calculateUnpaidAmount(intentionLedger);
        } else {
            log.info("合同ID:{}，已存在意向金台账，逻辑删除台账信息", contractId);
            ledgerService.lambdaUpdate().eq(LedgerEntity::getContractId, contractId)
                    .eq(LedgerEntity::getFeeType, FeeTypeEnum.INTENTION.getCode()).set(LedgerEntity::getDelFlag, 1)
                    .update();
        }

    }

    /**
     * 处理押金台账
     * <p>
     * 处理逻辑
     * 1. 合同加项目维度，如果押金有付款，则更新
     *
     * @param contractId
     * @param daoDepositLedgers
     * @param paramDepositLedgers
     */
    private void processExistDepositLedger(Integer contractId, List<LedgerEntity> daoDepositLedgers, List<LedgerEntity> paramDepositLedgers) {
        log.info("处理已存在押金台账开始，contractId:{}", contractId);
        if (CollectionUtils.isEmpty(daoDepositLedgers)) {
            log.info("合同ID:{}，无押金台账，无需处理历史数据", contractId);
            return;
        }

        // 押金台账为空，则逻辑删除原来生成的押金台账
        if (CollectionUtils.isEmpty(paramDepositLedgers)) {
            log.info("合同ID:{}，无付款周期台账，逻辑删除历史台账数据", contractId);
            ledgerService.lambdaUpdate().eq(LedgerEntity::getContractId, contractId)
                    .eq(LedgerEntity::getFeeType, FeeTypeEnum.DEPOSIT.getCode())
                    .set(LedgerEntity::getDelFlag, BooleFlagEnum.YES.getCode()).update();
            return;
        }

        // 获取projectId集合
        Set<Integer> daoProjectIds = daoDepositLedgers.stream().map(bean -> bean.getProjectId())
                .collect(Collectors.toSet());
        Set<Integer> paramProjectIds = paramDepositLedgers.stream().map(bean -> bean.getProjectId())
                .collect(Collectors.toSet());

        // 押金台账不为空，项目ID相同则更新，ID不同的则新增
        Map<Integer, LedgerEntity> daoIdMap = daoDepositLedgers.stream()
                .collect(Collectors.toMap(bean -> bean.getProjectId(), bean -> bean, (k1, k2) -> k1));


        // 数据库中独有的，则逻辑删除
        Set<Integer> daoNeedToRemove = daoProjectIds.stream().filter(id -> !paramProjectIds.contains(id))
                .collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(daoNeedToRemove)) {
            log.info("合同ID:{}，押金台账有数据被删除，ids:{}", contractId, daoNeedToRemove);
            ledgerService.lambdaUpdate().eq(LedgerEntity::getContractId, contractId)
                    .in(LedgerEntity::getProjectId, daoNeedToRemove)
                    .set(LedgerEntity::getDelFlag, BooleFlagEnum.YES.getCode()).update();
        }

        // 交集部分，则更新某些字段
        paramDepositLedgers.stream().filter(bean -> daoProjectIds.contains(bean.getProjectId()))
                .collect(Collectors.toList()).forEach(bean -> {
                    LedgerEntity ledgerEntity = daoIdMap.get(bean.getProjectId());
                    if (ledgerEntity == null) {
                        return;
                    }
                    bean.setId(ledgerEntity.getId());
                    bean.setPaidAmount(ledgerEntity.getPaidAmount());
                    calculateUnpaidAmount(bean);
                });
    }

    /**
     * 处理周期台账
     * <p>
     * 处理逻辑：
     * 1. dao独有部分，则dao删除
     * 2. param独有部分，则新增（此方法中无需处理）
     * 3. dao和param共有部分，则更新
     *
     * @param contractId
     * @param daoPeriodLedgers
     * @param paramPeriodLedgers
     */
    private void processExistPeriodLedger(Integer contractId, List<LedgerEntity> daoPeriodLedgers, List<LedgerEntity> paramPeriodLedgers) {
        log.info("处理已存在付款周期台账开始，contractId:{}", contractId);

        // 如果以前没有台账记录，则无需处理
        if (CollectionUtils.isEmpty(daoPeriodLedgers)) {
            log.info("合同ID:{}，无付款周期台账，无需处理历史数据", contractId);
            return;
        }

        // 防御性处理null
        if (paramPeriodLedgers == null) {
            paramPeriodLedgers = Collections.emptyList();
        }

        // dao中的付款周期ID
        Set<Integer> daoLedgerPeriodIds = daoPeriodLedgers.stream().map(bean -> bean.getPaymentPeriodId())
                .filter(id -> !id.equals(0)).collect(Collectors.toSet());

        // 如果当前合同没有付款周期，则需要逻辑删除数据库台账
        if (CollectionUtils.isEmpty(paramPeriodLedgers)) {
            log.info("合同ID:{}，无付款周期台账，逻辑删除历史台账数据", contractId);
            ledgerService.lambdaUpdate().eq(LedgerEntity::getContractId, contractId)
                    .in(LedgerEntity::getPaymentPeriodId, daoLedgerPeriodIds)
                    .set(LedgerEntity::getDelFlag, BooleFlagEnum.YES.getCode()).update();
            return;
        }

        // 当前合同中包含的付款周期ID
        Set<Integer> paramLedgerIds = paramPeriodLedgers.stream().map(bean -> bean.getPaymentPeriodId())
                .filter(id -> !id.equals(0)).collect(Collectors.toSet());

        Map<Integer, LedgerEntity> daoIdMap = daoPeriodLedgers.stream()
                .collect(Collectors.toMap(LedgerEntity::getPaymentPeriodId, ledger -> ledger, (existing, replacement) -> existing));

        // 数据库独有，则逻辑删除
        Set<Integer> daoNeedToRemove = daoLedgerPeriodIds.stream().filter(id -> !paramLedgerIds.contains(id))
                .collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(daoNeedToRemove)) {
            log.info("合同ID:{}，付款周期台账有数据被删除，ids:{}", contractId, daoNeedToRemove);
            ledgerService.lambdaUpdate().eq(LedgerEntity::getContractId, contractId)
                    .in(LedgerEntity::getPaymentPeriodId, daoNeedToRemove)
                    .set(LedgerEntity::getDelFlag, BooleFlagEnum.YES.getCode()).update();
        }

        // 交集部分，则更新某些字段
        paramPeriodLedgers.stream().filter(bean -> daoLedgerPeriodIds.contains(bean.getPaymentPeriodId()))
                .collect(Collectors.toList()).forEach(bean -> {
                    LedgerEntity ledgerEntity = daoIdMap.get(bean.getPaymentPeriodId());
                    if (ledgerEntity == null) {
                        return;
                    }
                    Integer id = ledgerEntity.getId();
                    bean.setId(id);
                    bean.setPaidAmount(ledgerEntity.getPaidAmount());
                    calculateUnpaidAmount(bean);
                    log.info("合同ID:{}，付款周期台账有数据被更新，id:{}", contractId, id);
                });
    }

    /**
     * 计算未付款金额
     * <p>
     * 未支付金额=应付金额-已支付金额-应退金额
     *
     * @param bean
     */
    private void calculateUnpaidAmount(LedgerEntity bean) {
        if (bean == null) {
            return;
        }

        BigDecimal unpaidAmount = BigDecimal.ZERO;
        BigDecimal payableAmount = bean.getPayableAmount();
        if (payableAmount == null) {
            bean.setUnpaidAmount(unpaidAmount);
            return;
        }

        unpaidAmount = payableAmount;
        BigDecimal paidAmount = bean.getPaidAmount();
        if (paidAmount != null) {
            unpaidAmount = unpaidAmount.subtract(paidAmount);
        }

        BigDecimal refundAmount = bean.getRefundAmount();
        if (refundAmount != null) {
            unpaidAmount = unpaidAmount.subtract(refundAmount);
        }

        bean.setUnpaidAmount(unpaidAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
    }

    /**
     * 根据合同ID获取台账信息
     *
     * @param contractId 合同ID
     * @return
     */
    public List<LedgerEntity> getLedgerInfo(Integer contractId) {
        log.info("根据合同ID获取台账信息开始，contractId:{}", contractId);
        List<LedgerEntity> ledgerResult = new ArrayList<>();

        // 根据合同ID获取多个合同价格周期
        List<ContractPaymentPeriodEntity> contractPricePeriods = contractPaymentPeriodService.lambdaQuery()
                .eq(ContractPaymentPeriodEntity::getContractId, contractId).list();
        if (CollectionUtils.isEmpty(contractPricePeriods)) {
            log.error("合同ID:{}，无付款周期信息，请完善合同", contractId);
            return new ArrayList<>();
        }

        // 获取合同信息
        ContractEntity contract = contractService.getById(contractId);

        // 信息缓存
        Map<Integer, ContractProjectEntity> projectMap = new HashMap<>();
        Map<Integer, ContractDeviceEntity> deviceMap = new HashMap<>();
        Map<Integer, String> userMap = new HashMap<>();
        Map<Integer, Integer> projectSignCountMap = new HashMap<>();

        try {

            // 获取付款周期台账
            List<LedgerEntity> periodLedgerList = generatePeriodLedger(contract, contractPricePeriods, projectMap, deviceMap, userMap, projectSignCountMap);
            ledgerResult.addAll(periodLedgerList);

            // 获取押金台账
            List<LedgerEntity> depositLedgerList = generateDepositLedger(contract, projectMap, userMap);
            ledgerResult.addAll(depositLedgerList);

            // 获取意向金台账
            List<LedgerEntity> intentionLedgerList = generateIntentionLedger(contract, userMap);
            ledgerResult.addAll(intentionLedgerList);

            // 设置台账期数
            setLedgerPeriod(ledgerResult);

            log.info("根据合同ID获取台账信息成功，contractId:{}", contractId);
        } catch (Exception e) {
            log.error("获取台账失败，contractId:{}", contractId);
            log.error("异常堆栈", e);
        }
        return ledgerResult;
    }

    /**
     * 生成意向金台账
     *
     * @param contract
     * @param userMap
     * @return
     */
    private List<LedgerEntity> generateIntentionLedger(ContractEntity contract, Map<Integer, String> userMap) {
        List<LedgerEntity> ledgerList = new ArrayList<>();
        BigDecimal intentionalDeposit = contract.getIntentionalDeposit();
        if (intentionalDeposit == null || BigDecimalUtils.eq(intentionalDeposit, BigDecimal.ZERO)) {
            return ledgerList;
        }

        // 添加押金台账
        LedgerEntity ledgerItem = new LedgerEntity();

        // 意向金是合同维度，无需设置项目相关信息
        ledgerItem.setContractId(contract.getId());
        ledgerItem.setContractCode(contract.getContractCode());
        ledgerItem.setContractType(contract.getContractType());

        // 一个合同有可能有多个供应商，这里无需设置供应商信息
        ledgerItem.setFeeType(FeeTypeEnum.INTENTION.getCode());
        ledgerItem.setPaymentType(PaymentTypeEnum.ONCE.getCode());
        ledgerItem.setStartDate(contract.getStartDate());
        ledgerItem.setEndDate(contract.getEndDate());

        // 意向金无需设置计划付款日
        ledgerItem.setPayableAmount(intentionalDeposit);
        ledgerItem.setUnpaidAmount(intentionalDeposit);
        ledgerItem.setPaidAmount(BigDecimal.ZERO);

        // 合同签约数量
        Integer signAccount = contractDeviceService.lambdaQuery()
                .eq(ContractDeviceEntity::getContractId, contract.getId()).list().stream()
                .map(ContractDeviceEntity::getSignCount).reduce(0, Integer::sum);
        ledgerItem.setContractCount(signAccount);
        ledgerItem.setCityId(contract.getCityId());

        // 设置跟进人
        assembleUserInfo(contract, userMap, ledgerItem);

        ledgerList.add(ledgerItem);

        return ledgerList;
    }

    /**
     * 生成付款周期台账
     *
     * @param contract
     * @param contractPricePeriods
     * @param projectMap
     * @param deviceMap
     * @param userMap
     * @param projectSignCountMap
     * @return
     */
    private List<LedgerEntity> generatePeriodLedger(ContractEntity contract, List<ContractPaymentPeriodEntity> contractPricePeriods,
                                                    Map<Integer, ContractProjectEntity> projectMap, Map<Integer, ContractDeviceEntity> deviceMap,
                                                    Map<Integer, String> userMap, Map<Integer, Integer> projectSignCountMap) {
        if (CollectionUtils.isEmpty(contractPricePeriods)) {
            throw new IllegalStateException("付款周期信息不能为空!");
        }

        List<LedgerEntity> ledgerList = new ArrayList<>();

        // 遍历合同价格周期，生成台账信息
        for (ContractPaymentPeriodEntity contractPaymentPeriod : contractPricePeriods) {

            LedgerEntity ledgerItem = new LedgerEntity();

            // 组装台账付款周期基本信息
            assembleContractPaymentPeriodInfo(contractPaymentPeriod, ledgerItem);

            // 组装主合同信息
            assembleContractInfo(contract, ledgerItem);

            // 组装子合同信息
            assembleSubContractInfo(projectSignCountMap, contractPaymentPeriod, ledgerItem);

            // 获取用户相关信息
            assembleUserInfo(contract, userMap, ledgerItem);

            // 组装设备信息
            assembleDeviceInfo(deviceMap, contractPaymentPeriod, ledgerItem);

            // 获取项目信息
            assembleProjectInfo(projectMap, contractPaymentPeriod, ledgerItem);

            // 组装供应商信息
            assembleSupplierInfo(contractPaymentPeriod, ledgerItem);

            // 添加到需要新增的台账集合
            ledgerList.add(ledgerItem);
        }
        return ledgerList;
    }

    private void assembleSubContractInfo(Map<Integer, Integer> projectSignCountMap, ContractPaymentPeriodEntity contractPaymentPeriod, LedgerEntity ledgerItem) {
        Integer subContractId = contractPaymentPeriod.getSubContractId();
        if (subContractId == 0) {
            return;
        }

        ContractSubEntity contractSubEntity = contractSubService.getById(subContractId);
        ledgerItem.setSubContractCode(contractSubEntity.getContractCode());
        ledgerItem.setPaymentType(contractSubEntity.getPaymentType());
        // 设置项目签约数
        Integer signCount = projectSignCountMap.get(contractPaymentPeriod.getProjectId());
        if (signCount == null) {
            Integer projectSignCount = contractDeviceService.lambdaQuery()
                    .eq(ContractDeviceEntity::getProjectId, contractPaymentPeriod.getProjectId())
                    .eq(ContractDeviceEntity::getContractId, contractPaymentPeriod.getContractId()).list().stream()
                    .map(ContractDeviceEntity::getSignCount).reduce(0, Integer::sum);
            projectSignCountMap.put(contractPaymentPeriod.getProjectId(), projectSignCount);
            ledgerItem.setContractCount(projectSignCount);
        } else {
            ledgerItem.setContractCount(signCount);
        }
    }

    private void assembleProjectInfo(Map<Integer, ContractProjectEntity> projectMap, ContractPaymentPeriodEntity contractPaymentPeriod, LedgerEntity ledgerItem) {
        Integer projectId = contractPaymentPeriod.getProjectId();
        ContractProjectEntity contractProjectEntity = projectMap.get(projectId);
        if (contractProjectEntity == null) {
            contractProjectEntity = contractProjectService.getById(projectId);
            projectMap.put(projectId, contractProjectEntity);
        }
        ledgerItem.setProjectName(contractProjectEntity.getProjectName());
        ledgerItem.setAreaName(contractProjectEntity.getAreaName());
        ledgerItem.setAddress(contractProjectEntity.getAddress());
        ledgerItem.setLevel(contractProjectEntity.getLevel());
        if (StringUtils.isBlank(ledgerItem.getPaymentType())) {
            // 如果是空则表示非子合同，为空则表示非子合同，不为空则表示是子合同，已经设置过付款类型
            ledgerItem.setPaymentType(contractProjectEntity.getPaymentType());
        }
        ledgerItem.setPropertyType(contractProjectEntity.getPropertyType());
    }

    private void assembleDeviceInfo(Map<Integer, ContractDeviceEntity> deviceMap, ContractPaymentPeriodEntity contractPaymentPeriod, LedgerEntity ledgerItem) {
        Integer deviceId = contractPaymentPeriod.getDeviceId();
        ContractDeviceEntity contractDeviceEntity = deviceMap.get(deviceId);
        if (contractDeviceEntity == null) {
            contractDeviceEntity = contractDeviceService.lambdaQuery().eq(ContractDeviceEntity::getId, deviceId).one();
            if (contractDeviceEntity == null) {
                return;
            }
            deviceMap.put(deviceId, contractDeviceEntity);
        }
        Integer signCount = contractDeviceEntity.getSignCount();
        if (signCount != null && signCount != 0) {
            ledgerItem.setContractCount(signCount);
            BigDecimal singlePrice = ledgerItem.getPayableAmount()
                    .divide(new BigDecimal(signCount), 2, BigDecimal.ROUND_HALF_UP);
            ledgerItem.setSinglePrice(singlePrice);
        }
    }

    private void assembleUserInfo(ContractEntity contract, Map<Integer, String> userMap, LedgerEntity ledgerItem) {
        Integer submitterId = contract.getSubmitter();
        Integer followerId = contract.getFollower();
        ledgerItem.setSubmitter(submitterId);
        ledgerItem.setFollower(followerId);
        if (StringUtils.isEmpty(userMap.get(submitterId)) || StringUtils.isEmpty(userMap.get(followerId))) {
            List<Integer> userIds = new ArrayList<>(2);
            userIds.add(submitterId);
            userIds.add(followerId);
            Map<Integer, String> userMapping = codeNameHelper.getUserMapping(userIds);
            userMap.putAll(userMapping);
        }
        ledgerItem.setSubmitterName(userMap.get(submitterId));
        ledgerItem.setFollowerName(userMap.get(followerId));
    }

    /**
     * 生成押金台账
     *
     * @param contract
     * @param projectMap
     * @param userMap
     * @return
     */
    private List<LedgerEntity> generateDepositLedger(ContractEntity contract, Map<Integer, ContractProjectEntity> projectMap,
                                                     Map<Integer, String> userMap) {

        List<LedgerEntity> depositLedgerList = new ArrayList<>();

        // 遍历项目信息
        for (Map.Entry<Integer, ContractProjectEntity> entry : projectMap.entrySet()) {
            // 获取项目信息
            Integer projectId = entry.getKey();
            ContractProjectEntity contractProjectEntity = entry.getValue();

            // 如果没有押金金额，则跳过
            if (contractProjectEntity.getDepositAmount() == null || BigDecimalUtils.eq(contractProjectEntity.getDepositAmount(), BigDecimal.ZERO)) {
                continue;
            }

            // 添加押金台账
            depositLedgerList.add(getProjectDepositLedger(contract, userMap, contractProjectEntity, projectId));
        }
        return depositLedgerList;
    }

    private LedgerEntity getProjectDepositLedger(ContractEntity contract, Map<Integer, String> userMap,
                                                 ContractProjectEntity contractProjectEntity, Integer projectId) {

        ContractDepositSupplierEntity depositSupplier = contractDepositSupplierService.lambdaQuery()
                .eq(ContractDepositSupplierEntity::getContractId, contract.getId())
                .eq(ContractDepositSupplierEntity::getProjectId, contractProjectEntity.getId()).one();

        // 添加押金台账
        LedgerEntity ledgerItem = new LedgerEntity();
        ledgerItem.setProjectId(projectId);
        ledgerItem.setProjectName(contractProjectEntity.getProjectName());
        ledgerItem.setAreaName(contractProjectEntity.getAreaName());
        ledgerItem.setPropertyType(contractProjectEntity.getPropertyType());
        ledgerItem.setAddress(contractProjectEntity.getAddress());
        ledgerItem.setLevel(contractProjectEntity.getLevel());
        ledgerItem.setContractId(contract.getId());
        ledgerItem.setContractCode(contract.getContractCode());
        ledgerItem.setContractType(contract.getContractType());

        // 供应商账号信息
        ledgerItem.setSupplierId(depositSupplier.getSupplierId());
        ledgerItem.setSupplierCode(depositSupplier.getSupplierCode());
        ledgerItem.setSupplierName(depositSupplier.getSupplierName());
        ledgerItem.setAccountName(depositSupplier.getAccountName());
        ledgerItem.setBankName(depositSupplier.getBankName());
        ledgerItem.setBankCode(depositSupplier.getBankCode());
        ledgerItem.setAccountNo(depositSupplier.getAccountNo());

        ledgerItem.setFeeType(FeeTypeEnum.DEPOSIT.getCode());
        ledgerItem.setPaymentType(PaymentTypeEnum.ONCE.getCode());
        ledgerItem.setStartDate(contractProjectEntity.getStartDate());
        ledgerItem.setEndDate(contractProjectEntity.getEndDate());
        ledgerItem.setPlannedPaymentDate(contractProjectEntity.getDepositPaymentDate());
        ledgerItem.setPayableAmount(contractProjectEntity.getDepositAmount());
        ledgerItem.setUnpaidAmount(contractProjectEntity.getDepositAmount());
        ledgerItem.setPaidAmount(BigDecimal.ZERO);

        ledgerItem.setFollowerName(contract.getFollowerName());
        // 合同签约数量
        Integer signAccount = contractDeviceService.lambdaQuery()
                .eq(ContractDeviceEntity::getContractId, contract.getId())
                .eq(ContractDeviceEntity::getProjectId, contractProjectEntity.getId()).list().stream()
                .map(ContractDeviceEntity::getSignCount).reduce(0, Integer::sum);
        ledgerItem.setContractCount(signAccount);

        // 设置跟进人
        assembleUserInfo(contract, userMap, ledgerItem);

        ledgerItem.setCityId(contract.getCityId());
        return ledgerItem;
    }


    /**
     * 设置台账期数
     * 主协议：以项目 供应商+终端 维度按付款周期ID顺序从1递增
     * 子协议：以费用类型 维度按付款周期计划付款日顺序从1递增
     *
     * @param ledgerList 台账数据
     */
    private static void setLedgerPeriod(List<LedgerEntity> ledgerList) {
        if (CollectionUtils.isEmpty(ledgerList)) {
            return;
        }
        // 按照设备类型及deviceId进行分组
        Map<String, List<LedgerEntity>> feeTypeGroup = ledgerList.stream()
                .collect(Collectors.groupingBy(ledger -> ledger.getFeeType() + ledger.getDeviceId() + ledger.getSupplierName() + ledger.getProjectName()));
        for (Map.Entry<String, List<LedgerEntity>> entry : feeTypeGroup.entrySet()) {
            List<LedgerEntity> feeTypeLedgers = entry.getValue();
            // 按费用类型分组后,设置每组的期数
            int period = 1;
            // 按PlannedPaymentDate升序
            feeTypeLedgers.sort(Comparator.comparing(
                    LedgerEntity::getPlannedPaymentDate,
                    Comparator.nullsLast(Comparator.naturalOrder())
            ));
            for (LedgerEntity ledger : feeTypeLedgers) {
                ledger.setPeriods(period);
                period++;
            }
        }
    }

    private LocalDate calculatePlannedPaymentDate(LedgerEntity ledgerItem, ContractProjectEntity contractProjectEntity, ContractSubEntity contractSubEntity) {
        if (ledgerItem == null) {
            return null;
        }
        String feeType = ledgerItem.getFeeType();
        LocalDate startDate = ledgerItem.getStartDate();
        LocalDate plannedDate = null;
        if (StringUtils.equals(FeeTypeEnum.RENTAL.getCode(), feeType)) {
            // 场地租赁分
            plannedDate = startDate.minusDays(contractProjectEntity.getIntervalDay());
        } else if (StringUtils.equals(FeeTypeEnum.DEPOSIT.getCode(), feeType)) {
            // 押金
            plannedDate = contractProjectEntity.getDepositPaymentDate();
        } else if (StringUtils.equals(FeeTypeEnum.ELECTRIC.getCode(), feeType) || StringUtils.equals(FeeTypeEnum.SERVICE.getCode(), feeType)) {
            // 电费或者服务费
            plannedDate = startDate.minusDays(contractSubEntity.getIntervalDay());
        } else {
            // 未知费用类型
            log.warn("calculatePlannedPaymentDate 未知费用类型，contractId:{}", ledgerItem.getContractId());
        }
        return plannedDate;
    }

    /**
     * 组装签约数量信息
     *
     * @param contractDeviceEntity
     * @param ledgerItem
     */
    private void assembleSignCount(ContractDeviceEntity contractDeviceEntity, LedgerEntity ledgerItem) {
        if (contractDeviceEntity == null || ledgerItem == null) {
            return;
        }
        Integer signCount = contractDeviceEntity.getSignCount();
        if (signCount != null && signCount != 0) {
            ledgerItem.setContractCount(signCount);
            BigDecimal singlePrice = ledgerItem.getPayableAmount().divide(new BigDecimal(signCount), 2,  // 保留2位小数
                    BigDecimal.ROUND_HALF_UP  // 四舍五入
            );
            ledgerItem.setSinglePrice(singlePrice);
        }
    }

    /**
     * 组装供应商信息
     *
     * @param ledgerItem
     */
    private void assembleSupplierInfo(ContractPaymentPeriodEntity contractPaymentPeriod, LedgerEntity ledgerItem) {
        ledgerItem.setSupplierId(contractPaymentPeriod.getSupplierId());
        ledgerItem.setSupplierCode(contractPaymentPeriod.getSupplierCode());
        ledgerItem.setSupplierName(contractPaymentPeriod.getSupplierName());
        ledgerItem.setAccountNo(contractPaymentPeriod.getAccountNo());
        ledgerItem.setBankName(contractPaymentPeriod.getBankName());
        ledgerItem.setBankCode(contractPaymentPeriod.getBankCode());
        ledgerItem.setAccountName(contractPaymentPeriod.getAccountName());
    }

    /**
     * 组装主合同信息
     *
     * @param contract
     * @param ledgerItem
     */
    private void assembleContractInfo(ContractEntity contract, LedgerEntity ledgerItem) {
        if (contract == null || ledgerItem == null) {
            return;
        }
        ledgerItem.setContractCode(contract.getContractCode());
        ledgerItem.setCityId(contract.getCityId());
        ledgerItem.setAgentId(contract.getAgentId());
        ledgerItem.setContractType(contract.getContractType());
    }

    /**
     * 组装项目相关信息
     *
     * @param contractProjectEntity
     * @param ledgerItem
     */
    private void assembleContractProjectInfo(ContractProjectEntity contractProjectEntity, LedgerEntity ledgerItem) {
        if (contractProjectEntity == null || ledgerItem == null) {
            return;
        }
        ledgerItem.setProjectName(contractProjectEntity.getProjectName());
        ledgerItem.setAreaName(contractProjectEntity.getAreaName());
        ledgerItem.setAddress(contractProjectEntity.getAddress());
        ledgerItem.setLevel(contractProjectEntity.getLevel());
        ledgerItem.setPaymentType(contractProjectEntity.getPaymentType());
        ledgerItem.setPropertyType(contractProjectEntity.getPropertyType());
    }

    /**
     * 组装台账基本信息
     *
     * @param contractPaymentPeriod
     * @param ledgerItem
     */
    private void assembleContractPaymentPeriodInfo(ContractPaymentPeriodEntity contractPaymentPeriod, LedgerEntity ledgerItem) {
        if (contractPaymentPeriod == null || ledgerItem == null) {
            return;
        }
        ledgerItem.setPaymentPeriodId(contractPaymentPeriod.getId());
        ledgerItem.setContractId(contractPaymentPeriod.getContractId());
        ledgerItem.setSubContractId(contractPaymentPeriod.getSubContractId());
        ledgerItem.setProjectId(contractPaymentPeriod.getProjectId());
        ledgerItem.setDeviceId(contractPaymentPeriod.getDeviceId());
        ledgerItem.setFeeType(contractPaymentPeriod.getFeeType());
        ledgerItem.setStartDate(contractPaymentPeriod.getStartDate());
        ledgerItem.setEndDate(contractPaymentPeriod.getEndDate());
        ledgerItem.setPayableAmount(contractPaymentPeriod.getAmount());
        ledgerItem.setUnpaidAmount(contractPaymentPeriod.getAmount());
        ledgerItem.setPlannedPaymentDate(contractPaymentPeriod.getPlanPaymentDate());
        ledgerItem.setRefundAmount(contractPaymentPeriod.getRefundAmount());
    }


    /**
     * 分页获取台账信息
     *
     * @param pageRequest  分页及删选信息
     * @param contractType 合同类型
     * @return 数据分页结果
     * @see com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractTypeEnum
     */
    public PageResponseVo<LedgerVO> getLedgerPageList(PageRequestVo<LedgerQueryParam> pageRequest, Integer contractType) {
        // 如果当前页码小于零或者为空，则设置为1
        if (pageRequest.getCurrentPage() == null || pageRequest.getCurrentPage() <= 0) {
            pageRequest.setCurrentPage(1L);
        }
        // 如果每页大小小于等于或者为空，则设置为20
        if (pageRequest.getPageSize() == null || pageRequest.getPageSize() <= 0) {
            pageRequest.setPageSize(VenueConstants.DEFAULT_PAGE_SIZE);
        }
        // 分页查询台账列表
        LedgerQueryParam query = pageRequest.getQuery();
        // 按照query条件查询台账列表，query为空则查询所有台账,字段有值则按照字段查询，字段为空则不设置此字段的查询条件
        LambdaQueryWrapper<LedgerEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (query != null) {
            queryWrapper = buildQueryWrapper(query, contractType);
        }
        Page<LedgerEntity> page = ledgerService.page(new Page<>(pageRequest.getCurrentPage(), pageRequest.getPageSize()), queryWrapper);
        List<LedgerVO> list = page.getRecords().stream().map(LedgerConvert.INSTANCE::toLedgerVO)
                .collect(Collectors.toList());
        return new PageResponseVo<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), list, page.getTotal());
    }

    /**
     * 编辑付款情况
     *
     * @param editParam
     * @return
     */
    public Integer editLedger(LedgerEditParam editParam) {

        // 已支付金额必须大于0
        if (editParam.getPaidAmount().compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalStateException("已支付金额必须大于0");
        }
        if (editParam.getReturnInvoicedAmount().compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalStateException("已回票金额必须大于等于0");
        }

        // 校验已支付金额
        LedgerEntity daoLedger = ledgerService.getById(editParam.getId());
        if (daoLedger == null) {
            throw new IllegalStateException("台账不存在");
        }
        BigDecimal payableAmount = daoLedger.getPayableAmount();
        if (editParam.getPaidAmount().compareTo(payableAmount) > 0) {
            throw new IllegalStateException("已支付金额不能大于应付金额");
        }

        // 更新台账
        LedgerEntity entity = new LedgerEntity();
        entity.setId(editParam.getId());
        BigDecimal paidAmount = editParam.getPaidAmount();
        entity.setPayableAmount(payableAmount);
        entity.setPaidAmount(paidAmount);
        entity.setRefundAmount(daoLedger.getRefundAmount());
        entity.setReturnInvoicedAmount(editParam.getReturnInvoicedAmount());
        calculateUnpaidAmount(entity);

        boolean isSuccess = ledgerService.updateById(entity);
        if (isSuccess) {
            return entity.getId();
        } else {
            log.warn("付款金额修改失败，参数信息:{}", JSON.toJSONString(editParam));
            throw new IllegalStateException("修改失败");
        }
    }

    /**
     * 批量导入付款情况
     *
     * @param file 导数Excel数据
     * @return 导入结果Excel地址
     */
    public ImportResult importLedger(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new CommonException("上传文件不能为空");
        }

        String resultMessage = "导入完成，所有数据导入成功！";

        try {
            // 读取Excel数据
            List<LedgerExcelParam> ledgerData = readExcelSupplierBankInfo(file);

            if (ledgerData != null && ledgerData.size() > 1000) {
                throw new IllegalArgumentException("导入台账数量不能超过1000条");
            }

            // 数据写入
            importExcelData(ledgerData);

            // 创建临时文件
            File tempFile = File.createTempFile(UUID.randomUUID().toString(), ".xlsx");
            tempFile.deleteOnExit();

            try {
                // 写入Excel
                writeExcelFile(tempFile, ledgerData);

                // 上传到云存储
                String fileUrl = uploadToCloud(tempFile);

                // 查看是否有错误
                boolean ledgerError = ledgerData.stream().anyMatch(bean -> bean.getImportResult().equals("失败"));
                if (ledgerError) {
                    resultMessage = "部分数据导入失败，请导出查看具体情况!";
                    return ImportResult.builder().success(true).fileUrl(fileUrl).message(resultMessage).build();
                }
                return ImportResult.builder().success(true).message(resultMessage).build();
            } finally {
                // 主动删除临时文件
                if (tempFile.exists()) {
                    tempFile.delete();
                }
            }
        } catch (Exception e) {
            log.error("批量导入台账异常：", e);
            return ImportResult.failure(e.getMessage());
        }
    }

    /**
     * 数据写入数据库
     *
     * @param ledgerData
     */
    private void importExcelData(List<LedgerExcelParam> ledgerData) {
        if (CollectionUtils.isEmpty(ledgerData)) {
            return;
        }

        for (LedgerExcelParam rowParam : ledgerData) {
            try {
                LedgerEntity paramEntity = validAndConvertLedgerParam(rowParam);
                if (paramEntity == null) {
                    continue;
                }
                LedgerEntity daoEntity = ledgerService.getById(paramEntity.getId());
                if (daoEntity == null) {
                    rowParam.setImportResult("失败");
                    rowParam.setMessage("无效的台账ID");
                    continue;
                }
                BigDecimal payableAmount = daoEntity.getPayableAmount();
                BigDecimal paidAmount = paramEntity.getPaidAmount();
                if (paidAmount.compareTo(payableAmount) > 0) {
                    rowParam.setImportResult("失败");
                    rowParam.setMessage("已支付金额不能大于应付金额");
                    continue;
                }
                paramEntity.setRefundAmount(daoEntity.getRefundAmount());
                calculateUnpaidAmount(paramEntity);
                boolean success = ledgerService.updateById(paramEntity);
                if (success) {
                    rowParam.setImportResult("成功");
                    rowParam.setMessage("");
                } else {
                    rowParam.setImportResult("失败");
                    rowParam.setMessage("数据库操作失败");
                }
            } catch (Exception e) {
                rowParam.setImportResult("失败");
                rowParam.setMessage("服务器数据库异常");
            }
        }
    }

    private LedgerEntity validAndConvertLedgerParam(LedgerExcelParam rowParam) {
        LedgerEntity entity = new LedgerEntity();

        // 台账ID
        String ledgerId = rowParam.getLedgerId();
        if (StringUtils.isEmpty(ledgerId)) {
            rowParam.setImportResult("失败");
            rowParam.setMessage("台账ID不能为空");
            return null;
        }
        try {
            entity.setId(Integer.valueOf(ledgerId));
        } catch (Exception e) {
            rowParam.setImportResult("失败");
            rowParam.setMessage("台账ID必须为数字");
            return null;
        }

        // 已支付金额
        String paidAmount = rowParam.getPaidAmount();
        if (StringUtils.isEmpty(paidAmount)) {
            rowParam.setImportResult("失败");
            rowParam.setMessage("已支付金额不能为空");
            return null;
        }
        try {
            BigDecimal paid = BigDecimal.valueOf(Double.valueOf(paidAmount));
            if (paid.compareTo(BigDecimal.ZERO) < 0) {
                rowParam.setImportResult("失败");
                rowParam.setMessage("已支付金额必须大于等于0");
                return null;
            }
            entity.setPaidAmount(paid);
        } catch (Exception e) {
            rowParam.setImportResult("失败");
            rowParam.setMessage("已支付金额必须为数字");
            return null;
        }

        // 已回票金额
        String returnInvoicedAmount = rowParam.getReturnInvoicedAmount();
        if (StringUtils.isEmpty(returnInvoicedAmount)) {
            rowParam.setImportResult("失败");
            rowParam.setMessage("已回票金额不能为空");
            return null;
        }
        try {
            BigDecimal returnInvoiceAmount = BigDecimal.valueOf(Double.valueOf(returnInvoicedAmount));
            if (returnInvoiceAmount.compareTo(BigDecimal.ZERO) < 0) {
                rowParam.setImportResult("失败");
                rowParam.setMessage("已回票金额必须大于等于0");
                return null;
            }
            entity.setReturnInvoicedAmount(returnInvoiceAmount);
        } catch (Exception e) {
            rowParam.setImportResult("失败");
            rowParam.setMessage("已回票金额必须为数字");
            return null;
        }
        return entity;
    }

    /**
     * 读取Excel 供应商银行信息
     *
     * @param file
     * @return
     */
    private List<LedgerExcelParam> readExcelSupplierBankInfo(MultipartFile file) {
        List<LedgerExcelParam> bankData = new ArrayList<>();

        try {
            // Reset input stream for second sheet
            InputStream fileInputStream = file.getInputStream();

            // 读第二个表单
            EasyExcel.read(fileInputStream, LedgerExcelParam.class, new AnalysisEventListener<LedgerExcelParam>() {
                private int rowIndex = 0;

                @Override
                public void invoke(LedgerExcelParam data, AnalysisContext context) {
                    rowIndex++;
                    data.setRowNum(rowIndex);
                    bankData.add(data);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    log.info("Sheet 2 读取完成，总行数：{}", rowIndex);
                }
            }).sheet(0).doRead();

            return bankData;
        } catch (IOException e) {
            log.error("读取Excel文件失败", e);
            throw new RuntimeException("读取Excel文件失败", e);
        }
    }

    private String uploadToCloud(File tempFile) {
        String feature = "ledger";
        String fileName = UUID.randomUUID().toString() + ".xlsx";
        ObjectUtils.uploadFile(ObjectUtils.getCosFileName(feature, fileName), tempFile);
        return ObjectUtils.getAccessUrl(feature, fileName);
    }

    private void writeExcelFile(File tempFile, List<LedgerExcelParam> ledgerData) {
        if (tempFile == null) {
            throw new IllegalArgumentException("临时文件不能为空");
        }
        if (ledgerData == null) {
            throw new IllegalArgumentException("台账数据不能为空");
        }
        // 使用同一个ExcelWriter写入多个sheet
        try (ExcelWriter excelWriter = EasyExcel.write(tempFile).build()) {
            // 写入第一个sheet
            WriteSheet writeSheet1 = EasyExcel.writerSheet(0, "导入结果").head(LedgerExcelParam.class).build();
            excelWriter.write(ledgerData, writeSheet1);
        } catch (Exception e) {
            log.error("写入Excel文件失败", e);
            throw new CommonException("写入Excel文件失败: " + e.getMessage());
        }

    }

    /**
     * 导出台账
     *
     * @param queryParam   过滤条件
     * @param contractType 合同类型
     * @return
     */
    public String exportLedger(LedgerQueryParam queryParam, Integer contractType) {
        // 构建查询条件
        LambdaQueryWrapper<LedgerEntity> queryWrapper = buildQueryWrapper(queryParam, contractType);

        // 查询数据
        List<LedgerEntity> ledgerList = ledgerService.list(queryWrapper);
        if (CollectionUtils.isEmpty(ledgerList)) {
            return "";
        }
        List<LedgerExcelVO> excelVOList = ledgerList.stream().map(item -> ledgerConvert.toLedgerExcelVO(item))
                .collect(Collectors.toList());

        // 生成临时文件
        String tempFileName = UUID.randomUUID().toString() + ".xlsx";
        File tempFile = new File(System.getProperty("java.io.tmpdir"), tempFileName);

        try {
            // 写入Excel
            EasyExcel.write(tempFile).head(LedgerExcelVO.class).sheet("台账数据").doWrite(excelVOList);

            // 上传到COS并获取URL
            String feature = "ledger";
            String name="应付台账";
            if (ContractTypeEnum.NORMAL.getCode().equals(contractType)){
                name=DownLoadTypeEnum.YFTZ.getDesc();
            }else if (ContractTypeEnum.HISTORY.getCode().equals(contractType)){
                name=DownLoadTypeEnum.YFTZ_HIS.getDesc();
            }
            String date = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String fileName = String.format("%s_%s_%s.xlsx", name, date, System.currentTimeMillis());
            ObjectUtils.uploadFile(ObjectUtils.getCosFileName(feature, fileName), tempFile);
            return ObjectUtils.getAccessUrl(feature, fileName);

        } catch (Exception e) {
            log.error("导出台账异常", e);
            throw new RuntimeException("导出台账失败");
        } finally {
            // 清理临时文件
            if (tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    private LambdaQueryWrapper<LedgerEntity> buildQueryWrapper(LedgerQueryParam queryParam, Integer contractType) {
        LambdaQueryWrapper<LedgerEntity> queryWrapper = new LambdaQueryWrapper<>();
        // 获取用户数据权限
        UserDataAccessV2DTO userDataAccessV2 = dataAccessService.getUserDataAccessV2();
        if (Objects.isNull(userDataAccessV2)) {
            log.warn("用户:{}, 无数据访问权限", UserThreadLocal.getUserId());
            throw new CommonException("用户数据权限不足");
        }
        List<Integer> cityIds = userDataAccessV2.getCityIds();
        List<Integer> userIds = userDataAccessV2.getUserIds();
        List<Integer> agentIds = userDataAccessV2.getAgentIds();

        return queryWrapper.eq(contractType != null, LedgerEntity::getContractType, contractType)
                .eq(LedgerEntity::getDelFlag, BooleFlagEnum.NO.getCode())
                .like(StringUtils.isNotBlank(queryParam.getSupplierName()), LedgerEntity::getSupplierName, queryParam.getSupplierName())
                .eq(StringUtils.isNotBlank(queryParam.getContractCode()), LedgerEntity::getContractCode, queryParam.getContractCode())
                .like(StringUtils.isNotBlank(queryParam.getProjectName()), LedgerEntity::getProjectName, queryParam.getProjectName())
                .eq(StringUtils.isNotBlank(queryParam.getFeeType()), LedgerEntity::getFeeType, queryParam.getFeeType())
                .eq(StringUtils.equals(queryParam.getUnpaidAmount(), UnPaidAmountEnum.ZERO.getCode()), LedgerEntity::getUnpaidAmount, 0)
                .gt(StringUtils.equals(queryParam.getUnpaidAmount(), UnPaidAmountEnum.GTZERO.getCode()), LedgerEntity::getUnpaidAmount, 0)
                .ge(queryParam.getStartDate() != null, LedgerEntity::getStartDate, queryParam.getStartDate())
                .le(queryParam.getEndDate() != null, LedgerEntity::getEndDate, queryParam.getEndDate())
                .in(CollectionUtils.isNotEmpty(agentIds), LedgerEntity::getAgentId, agentIds)
                .in(CollectionUtils.isNotEmpty(cityIds), LedgerEntity::getCityId, cityIds)
                .in(CollectionUtils.isNotEmpty(userIds), LedgerEntity::getFollower, userIds);
    }

    /**
     * 获取导入模板
     *
     * @return
     */
    public String getTemplate() {
        return ledgerExcelTemplateUrl;
    }

    /**
     * 根据合同ID删除台账
     */
    public void deleteLedgerByContractId(Integer contractId) {
        log.info("删除台账，合同ID:{}", contractId);
        ledgerService.remove(new LambdaQueryWrapper<LedgerEntity>().eq(LedgerEntity::getContractId, contractId));
    }

    /**
     * 校验台账是否存在，存在返回true，不存在返回false
     *
     * @param contractId 合同ID
     * @return true 已存在
     */
    public Boolean checkLedger(Integer contractId) {
        Long count = ledgerService.lambdaQuery().eq(LedgerEntity::getContractId, contractId).count();
        return count > 0;
    }

    public void updateFollower(Integer id, Integer follower, String followerName) {
        ledgerService.lambdaUpdate().set(LedgerEntity::getFollower, follower)
                .set(LedgerEntity::getFollowerName, followerName).eq(LedgerEntity::getContractId, id).update();
    }

    public void batchUpdateFollower(BatchContractTransferParam batchContractTransferParam) {
        ledgerService.lambdaUpdate().set(LedgerEntity::getFollower, batchContractTransferParam.getFollower())
                .set(LedgerEntity::getFollowerName, batchContractTransferParam.getFollowerName())
                .in(LedgerEntity::getContractId, batchContractTransferParam.getContractIds()).update();
    }

    /**
     * 检查押金台账是否已经支付过
     *
     * @param contractId
     * @param projectCode
     * @return
     */
    public Boolean checkDepositLedgerEverPaid(Integer contractId, String projectCode) {
        // 获取项目编码
        String realProjectCode = projectCode.split("-")[0];

        // 获取此项目下的所有项目ID
        Set<Integer> projectIds = contractProjectService.lambdaQuery().select(ContractProjectEntity::getId)
                .eq(ContractProjectEntity::getContractId, contractId)
                .like(ContractProjectEntity::getProjectCode, realProjectCode).list().stream()
                .map(ContractProjectEntity::getId).collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(projectIds)) {
            return false;
        }

        // 查询该合同下，该项目下，该类型的台账，是否已经支付过
        return ledgerService.lambdaQuery().eq(LedgerEntity::getContractId, contractId)
                .eq(LedgerEntity::getFeeType, FeeTypeEnum.DEPOSIT.getCode()).in(LedgerEntity::getProjectId, projectIds)
                .gt(LedgerEntity::getPaidAmount, 0).count() > 0;

    }

    /**
     * 终止合同，逻辑删除所有台账
     *
     * @param contractId
     */
    public void terminateLedger(Integer contractId) {
        ContractEntity contract = contractService.getById(contractId);
        if (!StringUtils.equalsIgnoreCase(contract.getFormalStatus(), ContractStatusEnum.CLOSED.getCode())) {
            log.info("合同状态为非关闭状态，不可删除台账！,合同ID：{}", contractId);
            throw new IllegalStateException("合同状态为非关闭状态，不可删除台账！");
        }
        ledgerService.lambdaUpdate().eq(LedgerEntity::getContractId, contractId)
                .set(LedgerEntity::getDelFlag, BooleFlagEnum.YES.getCode())
                .set(LedgerEntity::getDelType, LedgerDelTypeEnum.TERMINATE.getCode()).update();
        log.info("终止合同，合同ID:{}", contractId);

    }
}
