package com.coocaa.ad.cheese.cms.venue.convert.contract;

import com.coocaa.ad.cheese.cms.common.convert.PageableConvert;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.CityEntity;
import com.coocaa.ad.cheese.cms.common.tools.common.bean.common.CodeNameVO;
import com.coocaa.ad.cheese.cms.venue.bean.contract.CityEditParam;
import com.coocaa.ad.cheese.cms.venue.vo.contract.CityListVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.CitySimpleListVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.CityVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.RegionCityVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.control.DeepClone;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-24
 */
@Mapper(componentModel = "spring", mappingControl = DeepClone.class)
public interface CityConvert extends PageableConvert<CityEntity, CityVO> {

    /**
     * 权限系统城市VO转合同系统城市实体
     */
    CityEntity toEntity(CodeNameVO codeNameVO);

    CityEntity toEntity(CodeNameVO codeNameVO, @MappingTarget CityEntity cityEntity);

    /**
     * @param cityEditParam 编辑城市参数
     * @param cityEntity    数据库中城市数据
     * @return
     */
    @Mapping(target = "id", ignore = true)
    CityEntity toEntity(CityEditParam cityEditParam, @MappingTarget CityEntity cityEntity);

    CityListVO toCityListVO(CityEntity cityEntity);

    CityVO toCityVO(CityEntity cityEntity);

    RegionCityVO toRegionCityVO(CityEntity cityEntity);

    CitySimpleListVO toCitySimpleListVO(CityEntity cityEntity);
}
