package com.coocaa.ad.cheese.cms.venue.rpc;

import com.coocaa.ad.cheese.cms.common.tools.common.result.ResultTemplate;
import com.coocaa.ad.cheese.cms.venue.bean.workorder.FilterBuildingNoDTO;
import com.coocaa.ad.common.config.FeignConfig;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/2/28
 */
@FeignClient(value = "cheese-work-order-api", configuration = FeignConfig.class)
public interface FeignWorkOrderRpc {
    /**
     * 根据点位编码获取点位详情
     *
     * @return 区粒度的点位状态统计
     */
    @PostMapping("/api/work-order/order/filter/building-no")
    ResultTemplate<Set<String>> filterDispatchedBuildingNoList(@RequestBody FilterBuildingNoDTO filterBuildingNoDTO);

}
