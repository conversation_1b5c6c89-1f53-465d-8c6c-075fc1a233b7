package com.coocaa.ad.cheese.cms.venue.controller.contract;

import com.alibaba.fastjson2.JSON;
import com.coocaa.ad.cheese.cms.common.tools.common.exception.CommonException;
import com.coocaa.ad.cheese.cms.common.tools.common.result.PageRequestVo;
import com.coocaa.ad.cheese.cms.common.tools.common.result.PageResponseVo;
import com.coocaa.ad.cheese.cms.common.tools.common.result.ResultTemplate;
import com.coocaa.ad.cheese.cms.download.enums.DownLoadTypeEnum;
import com.coocaa.ad.cheese.cms.download.service.AnomalousContractService;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractAbnormalAddParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractAbnormalExportParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractAbnormalQueryParam;
import com.coocaa.ad.cheese.cms.venue.service.ContractAbnormalService;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractAbnormalInfoVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractAbnormalInfoWithPointVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractAbnormalVO;
import com.coocaa.ad.downloader.bean.dto.TaskDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

/**
 * 异常合同管理
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Slf4j
@RestController
@RequestMapping("/venue/contract/abnormal")
@Tag(name = "异常合同管理", description = "异常合同管理")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ContractAbnormalController {

    private final ContractAbnormalService contractAbnormalService;
    private final AnomalousContractService anomalousContractService;

    @Operation(summary = "新增异常合同")
    @PostMapping
    public ResultTemplate<Integer> create(@Validated @RequestBody ContractAbnormalAddParam addParam) {
        try {
            Integer id = contractAbnormalService.save(addParam);
            return ResultTemplate.success(id);
        } catch (CommonException e) {
            log.error(e.getMessage(), e);
            return ResultTemplate.fail(e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResultTemplate.fail("操作失败");
        }
    }

    /**
     * 异常合同列表(分页)
     */
    @Operation(summary = "异常合同列表(分页)")
    @PostMapping("/list")
    public ResultTemplate<PageResponseVo<ContractAbnormalVO>> pageList(@RequestBody PageRequestVo<ContractAbnormalQueryParam> pageRequest) {
        pageRequest.setQuery(Optional.ofNullable(pageRequest.getQuery()).orElseGet(ContractAbnormalQueryParam::new));
        return ResultTemplate.success(contractAbnormalService.pageList(pageRequest));
    }

    /**
     * 异常合同详情（SSP）
     */
    @Operation(summary = "异常合同详情（SSP）")
    @Parameter(name = "id", description = "合同ID", required = true, example = "1")
    @GetMapping("/{id}")
    public ResultTemplate<ContractAbnormalInfoWithPointVO> getInfoWithPointCount(@PathVariable(name = "id") Integer id) {
        return ResultTemplate.success(contractAbnormalService.getInfoWithPointCount(id,true));
    }

    /**
     * 异常合同详情
     */
    @Operation(summary = "异常合同详情")
    @Parameter(name = "id", description = "合同ID", required = true, example = "1")
    @GetMapping
    public ResultTemplate<ContractAbnormalInfoVO> getInfo(@RequestParam(name = "contractId") Integer contractId, @RequestParam(name = "id") Integer id) {
        return ResultTemplate.success(contractAbnormalService.getInfo(contractId, id));
    }

    /**
     * 异常合同申请单导出
     */
    @Operation(summary = "异常合同申请单导出")
    @PostMapping("/export")
    public ResultTemplate<Long> export(@RequestBody ContractAbnormalExportParam exportParam) {
        TaskDTO taskDTO = new TaskDTO().setExecuteParams(JSON.toJSONString(exportParam));
        return ResultTemplate.success(this.anomalousContractService.downloader(taskDTO));
    }

    @Operation(summary = "查询是否可以填写处理结果")
    @Parameter(name = "Token", description = "Token", in = ParameterIn.HEADER, required = true)
    @GetMapping("{abnormalId}/can-write-result")
    public ResultTemplate<Boolean> canWriteResult(@PathVariable("abnormalId") Integer abnormalId) {
        return ResultTemplate.success(contractAbnormalService.canWriteResult(abnormalId));
    }
}
