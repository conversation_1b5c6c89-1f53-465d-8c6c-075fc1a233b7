package com.coocaa.ad.cheese.cms.venue.service;

import cn.hutool.core.collection.CollectionUtil;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.RegionEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IRegionService;
import com.coocaa.ad.cheese.cms.common.tools.common.enums.BooleFlagEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.bean.UserDataAccessV2DTO;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.DataAccessTypeEnum;
import com.coocaa.ad.cheese.cms.venue.bean.contract.RegionAddParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.RegionEditParam;
import com.coocaa.ad.cheese.cms.venue.convert.contract.RegionConvert;
import com.coocaa.ad.cheese.cms.venue.vo.contract.RegionCityVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.RegionListVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.RegionSimpleListVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.RegionVO;
import com.coocaa.ad.translate.anno.AutoTranslate;
import com.google.common.collect.Maps;
import groovy.util.logging.Slf4j;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/3/24
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class RegionService {
    private final IRegionService regionService;
    private final RegionConvert regionConvert;
    private final CityService cityService;
    private final DataAccessService dataAccessService;

    /**
     * 更新大区
     */
    @Transactional
    public void updateRegion(RegionEditParam regionEditParam) {
        RegionEntity regionEntity = regionService.lambdaQuery()
                .eq(RegionEntity::getId, regionEditParam.getId())
                .one();
        if (Objects.isNull(regionEntity)) {
            throw new RuntimeException("根据大区id未找到对应的大区");
        }
        regionEntity = regionConvert.toEntity(regionEditParam, regionEntity);
        regionService.updateById(regionEntity);
        cityService.updateCityRegion(regionEditParam.getCities(), regionEntity.getId());
    }

    /**
     * 添加大区
     */
    @Transactional
    public void addRegion(RegionAddParam regionAddParam) {
        RegionEntity regionEntity = regionConvert.toEntity(regionAddParam);
        regionService.save(regionEntity);
        cityService.updateCityRegion(regionAddParam.getCityIds(), regionEntity.getId());
    }

    /**
     * 大区列表-详细版
     */
    @AutoTranslate
    public List<RegionListVO> listRegions() {
        List<RegionEntity> regionEntities = regionService.lambdaQuery()
                .orderByAsc(RegionEntity::getPriority)
                .list();

        Map<Integer, List<String>> regionCityNamesMap = cityService.listCityNamesGroupByRegion();
        return regionEntities.stream().map(regionConvert::toRegionListVO)
                .peek(regionListVO -> regionListVO.setCityNames(regionCityNamesMap.get(regionListVO.getId())))
                .toList();
    }


    /**
     * 获取城市和大区关系
     *
     * @return key:城市名称 value:所属大区名称
     */
    public Map<String, String> getCityReginMapping() {
        // key:大区ID, val:大区名称
        Map<Integer, String> regionMapping = regionService.lambdaQuery()
                .select(RegionEntity::getId, RegionEntity::getName)
                .list().stream()
                .collect(Collectors.toMap(RegionEntity::getId, RegionEntity::getName, (o, n) -> n));

        // key:城市名称, val:大区名称
        Map<String, String> cityReginMapping = Maps.newHashMapWithExpectedSize(500);
        for (Map.Entry<Integer, List<String>> entry : cityService.listCityNamesGroupByRegion().entrySet()) {
            String regionName = regionMapping.get(entry.getKey());
            if (StringUtils.isEmpty(regionName)) {
                continue;
            }
            entry.getValue().forEach(city -> cityReginMapping.put(city, regionName));
        }
        return cityReginMapping;
    }


    /**
     * 大区详情
     */
    @AutoTranslate
    public RegionVO getRegion(Integer id) {
        RegionEntity regionEntity = regionService.getById(id);
        RegionVO regionVO = regionConvert.toRegionVO(regionEntity);
        List<RegionCityVO> regionCities = cityService.getRegionCities(regionVO.getId());
        regionVO.setCities(regionCities.stream().map(RegionCityVO::getId).toList());
        return regionVO;
    }

    /**
     * 查询大区列表-简略版
     */
    public List<RegionSimpleListVO> listSimpleRegions() {
        UserDataAccessV2DTO userDataAccessV2 = dataAccessService.getUserDataAccessV2();
        if (Objects.isNull(userDataAccessV2)
                || DataAccessTypeEnum.SELF.getCode().equals(userDataAccessV2.getAccessType())
                || DataAccessTypeEnum.SELF_AND_SUB.getCode().equals(userDataAccessV2.getAccessType())) {
            return Collections.emptyList();
        }

        Set<Integer> regionIdsByCityIds = cityService.getRegionIdsByCityIds(userDataAccessV2.getCityIds());
        if (CollectionUtil.isEmpty(regionIdsByCityIds)) {
            return Collections.emptyList();
        }

        return regionService.lambdaQuery()
                .select(RegionEntity::getId, RegionEntity::getName)
                .eq(RegionEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .in(RegionEntity::getId, regionIdsByCityIds)
                .list().stream()
                .map(regionConvert::toRegionSimpleListVO)
                .toList();
    }

    /**
     * 获取所有大区信息
     */
    public List<RegionSimpleListVO> listAllRegions() {
        return regionService.lambdaQuery()
                .select(RegionEntity::getId, RegionEntity::getName)
                .list().stream()
                .map(regionConvert::toRegionSimpleListVO)
                .toList();

    }
}
