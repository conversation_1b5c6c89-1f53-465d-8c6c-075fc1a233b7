package com.coocaa.ad.cheese.cms.dataimport.service;

import com.coocaa.ad.cheese.cms.common.tools.common.result.ResultTemplate;
import com.coocaa.ad.cheese.cms.dataimport.pojo.BuildingNoVo;
import com.coocaa.ad.cheese.cms.dataimport.pojo.BuildingNoVoRet;
import com.coocaa.ad.cheese.cms.dataimport.pojo.CrmProjectParam;
import com.coocaa.ad.cheese.cms.dataimport.pojo.PointInfoVo;
import com.coocaa.ad.common.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

@FeignClient(value = "cheese-ssp-api",
        configuration = FeignConfig.class)
public interface FeignSspClient {
    @PostMapping("/ssp/project/crm/building_no")
    ResultTemplate<BuildingNoVoRet> getBuildingNo(@RequestBody BuildingNoVo buildingNoVo);

    @PostMapping("/ssp/project/crm/city/building_no")
    ResultTemplate<List<BuildingNoVoRet>> getBuildingNoOnlyCity(@RequestBody BuildingNoVo buildingNoVo);

    @GetMapping("/ssp/point/building_rating_no/{buildingNo}")
    ResultTemplate<List<PointInfoVo>> getPointInfo(@PathVariable(value = "buildingNo") String buildingNo);

    @PostMapping("/ssp/point/building_rating_no/cms")
    ResultTemplate<List<PointInfoVo>> building_rating_no(@RequestBody CrmProjectParam crmProjectParam);

    /**
     * 获取点位验收数量
     */
    @PostMapping("/ssp/project/point-count/by-building-no")
    ResultTemplate<List<Map<Object, Object>>> getPointCountByBuildingNo(@RequestBody List<String> buildingNos);
}
