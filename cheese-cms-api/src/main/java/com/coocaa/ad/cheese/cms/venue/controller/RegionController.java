package com.coocaa.ad.cheese.cms.venue.controller;

import com.coocaa.ad.cheese.cms.venue.bean.contract.RegionAddParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.RegionEditParam;
import com.coocaa.ad.cheese.cms.venue.service.RegionService;
import com.coocaa.ad.cheese.cms.venue.vo.contract.RegionListVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.RegionSimpleListVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.RegionVO;
import com.coocaa.ad.common.result.ResultTemplate;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 大区信息表 前端控制器
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Slf4j
@RestController
@RequestMapping("/venue/region")
@Tag(name = "大区管理", description = "大区管理")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class RegionController {
    private final RegionService regionService;

    @Operation(summary = "修改大区信息")
    @Parameter(name = "Token", description = "Token", in = ParameterIn.HEADER, required = true)
    @PutMapping
    public ResultTemplate<Boolean> edit(@RequestBody RegionEditParam regionEditParam) {
        regionService.updateRegion(regionEditParam);
        return ResultTemplate.success();
    }

    @Operation(summary = "新增大区")
    @Parameter(name = "Token", description = "Token", in = ParameterIn.HEADER, required = true)
    @PostMapping
    public ResultTemplate<Boolean> add(@RequestBody RegionAddParam regionAddParam) {
        regionService.addRegion(regionAddParam);
        return ResultTemplate.success();
    }

    @Operation(summary = "大区列表")
    @Parameter(name = "Token", description = "Token", in = ParameterIn.HEADER, required = true)
    @RequestMapping(path = "/list", method = {RequestMethod.GET, RequestMethod.POST})
    public ResultTemplate<List<RegionListVO>> listRegions() {
        return ResultTemplate.success(regionService.listRegions());
    }

    @Operation(summary = "大区详情-编辑")
    @Parameter(name = "Token", description = "Token", in = ParameterIn.HEADER, required = true)
    @GetMapping("/detail/{id}")
    public ResultTemplate<RegionVO> getRegion(@PathVariable("id") Integer id) {
        return ResultTemplate.success(regionService.getRegion(id));
    }

    @Operation(summary = "大区列表-精简")
    @Parameter(name = "Token", description = "Token", in = ParameterIn.HEADER, required = true)
    @RequestMapping(path = "/list/simple", method = {RequestMethod.GET, RequestMethod.POST})
    public ResultTemplate<List<RegionSimpleListVO>> listSimpleRegions() {
        return ResultTemplate.success(regionService.listSimpleRegions());
    }
}
