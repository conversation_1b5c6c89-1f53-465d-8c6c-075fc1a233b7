package com.coocaa.ad.cheese.cms.venue.controller;

import com.coocaa.ad.cheese.cms.common.tools.common.result.ResultTemplate;
import com.coocaa.ad.cheese.cms.venue.bean.contract.CityEditParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.CityQueryParam;
import com.coocaa.ad.cheese.cms.venue.service.CityService;
import com.coocaa.ad.cheese.cms.venue.vo.contract.CityListVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.CitySimpleListVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.CityVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 城市信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Slf4j
@RestController
@RequestMapping("/venue/city")
@Tag(name = "城市管理", description = "城市管理")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class CityController {
    private final CityService cityService;

    @Operation(summary = "同步权限系统城市")
    @Parameter(name = "Token", description = "Token", in = ParameterIn.HEADER, required = true)
    @GetMapping("/sync-from-auth")
    public ResultTemplate<Boolean> syncFromAuth() {
        cityService.syncFromAuth();
        return ResultTemplate.success();
    }

    @Operation(summary = "修改城市信息")
    @Parameter(name = "Token", description = "Token", in = ParameterIn.HEADER, required = true)
    @PutMapping
    public ResultTemplate<Boolean> edit(@RequestBody CityEditParam cityEditParam) {
        cityService.updateCity(cityEditParam);
        return ResultTemplate.success();
    }

    @Operation(summary = "城市列表-合同")
    @Parameter(name = "Token", description = "Token", in = ParameterIn.HEADER, required = true)
    @PostMapping("/list")
    public ResultTemplate<List<CityListVO>> listCities(@RequestBody CityQueryParam cityQueryParam) {
        return ResultTemplate.success(cityService.listCities(cityQueryParam));
    }

    @Operation(summary = "城市名称列表-合同")
    @Parameter(name = "Token", description = "Token", in = ParameterIn.HEADER, required = true)
    @GetMapping("/list/names")
    public ResultTemplate<List<String>> listCityNames() {
        return ResultTemplate.success(cityService.listCityNames());
    }

    @Operation(summary = "城市详情-编辑")
    @Parameter(name = "Token", description = "Token", in = ParameterIn.HEADER, required = true)
    @GetMapping("/detail/{id}")
    public ResultTemplate<CityVO> getCity(@PathVariable("id") Integer id) {
        return ResultTemplate.success(cityService.getCity(id));
    }

    @Operation(summary = "城市列表-根据大区获取")
    @Parameter(name = "Token", description = "Token", in = ParameterIn.HEADER, required = true)
    @PostMapping("/list-by-regions")
    public ResultTemplate<List<CitySimpleListVO>> listCitiesByRegions(@RequestBody List<Integer> regionIds) {
        return ResultTemplate.success(cityService.listCitiesByRegions(regionIds));
    }

    @Operation(summary = "获取城市负责人")
    @Parameter(name = "Token", description = "Token", in = ParameterIn.HEADER, required = true)
    @PostMapping("/business-head")
    public ResultTemplate<List<CityVO>> getBusinessHead(@RequestBody Set<String> cityNames) {
        return ResultTemplate.success(cityService.getBusinessHead(cityNames));
    }

}
