package com.coocaa.ad.cheese.cms.dataimport.service;

import com.alibaba.fastjson2.JSON;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractPaymentPeriodEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractPricePeriodEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractProjectEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractSubEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractPaymentPeriodService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractPricePeriodService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractProjectService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractSubService;
import com.coocaa.ad.cheese.cms.common.tools.common.enums.BooleFlagEnum;
import com.coocaa.ad.cheese.cms.common.tools.common.util.StringUtils;
import com.coocaa.ad.cheese.cms.dataimport.util.ParseContractExcel;
import com.coocaa.ad.cheese.cms.venue.service.ContractReadService;
import com.coocaa.ad.cheese.cms.venue.service.ContractWriteService;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/8
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
@Slf4j
public class ContractImportService {
    private final ContractWriteService contractWriteService;
    private final IContractPaymentPeriodService paymentPeriodService;
    private final ContractReadService contractReadService;
    private final IContractService contractService;
    private final IContractProjectService projectService;
    private final IContractPricePeriodService pricePeriodService;
    private final IContractSubService subService;

    /**
     * 取出历史有效合同,补全其付款周期的开始结束日期
     * 根据付款方式计算
     * 子合同的付款周期一样
     */
    public void fixPaymentPeriod(String contractCode) {
        //找出历史导入的合同
        List<ContractEntity> contractEntities = contractService.lambdaQuery()
                .eq(ContractEntity::getImportFlag, BooleFlagEnum.YES.getCode())
                .eq(ContractEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .eq(StringUtils.isNotBlank(contractCode), ContractEntity::getContractCode, contractCode)
                .list();
        if (CollectionUtils.isEmpty(contractEntities)) {
            return;
        }
        //挨个处理
        List<ContractPaymentPeriodEntity> paymentPeriodEntities = new ArrayList<>();
        Map<String, List<ContractPaymentPeriodEntity>> map = new HashMap<>();
        for (ContractEntity contractEntity : contractEntities) {
            List<ContractPaymentPeriodEntity> paymentPeriodEntitiesT = dealContractPaymentPeriod(contractEntity);
            log.info("{} : {}", contractEntity.getContractCode(), JSON.toJSONString(paymentPeriodEntitiesT));
            paymentPeriodEntities.addAll(paymentPeriodEntitiesT);
            map.put(contractEntity.getContractCode(), paymentPeriodEntitiesT);
        }
        List<List<ContractPaymentPeriodEntity>> pList = Lists.partition(paymentPeriodEntities, 500);
        log.info("pList: {}", JSON.toJSONString(pList));
        pList.forEach(paymentPeriodService::updateBatchById);
    }

    private List<ContractPaymentPeriodEntity> dealContractPaymentPeriod(ContractEntity contractEntity) {
        //查找项目
        List<ContractProjectEntity> projectEntities = projectService.lambdaQuery()
                .eq(ContractProjectEntity::getContractId, contractEntity.getId())
                .list();
        //处理每个项目的付款周期 以及子合同付款周期
        if (CollectionUtils.isEmpty(projectEntities)) {
            log.error("合同({})下面未找到项目", contractEntity.getId());
            return Collections.emptyList();
        }
        List<ContractPaymentPeriodEntity> paymentPeriodEntities = new ArrayList<>();
        for (ContractProjectEntity projectEntity : projectEntities) {
            paymentPeriodEntities.addAll(dealProjectPaymentPeriod(contractEntity, projectEntity));
            paymentPeriodEntities.addAll(dealSubPaymentPeriod(contractEntity, projectEntity));
        }
        return paymentPeriodEntities;
    }

    private List<ContractPaymentPeriodEntity> dealProjectPaymentPeriod(ContractEntity contractEntity, ContractProjectEntity projectEntity) {
        //处理项目的付款周期
        List<ContractPaymentPeriodEntity> paymentPeriodEntities = paymentPeriodService.lambdaQuery()
                .eq(ContractPaymentPeriodEntity::getProjectId, projectEntity.getId())
                .eq(ContractPaymentPeriodEntity::getSubContractId, BooleFlagEnum.NO.getCode())
                .orderByAsc(ContractPaymentPeriodEntity::getId)
                .list();
        if (CollectionUtils.isEmpty(paymentPeriodEntities)) {
            log.error("项目({})下面无付款周期", projectEntity.getId());
            return Collections.emptyList();
        }
        //根据项目的付款方式和 签约的年限 开始时间结束时间  免租等计算每个付款周期记录的开始结束时间
        String paymentType = projectEntity.getPaymentType();
        if (StringUtils.isBlank(paymentType)) {
            log.error("项目({})付款方式缺失", projectEntity.getId());
            return Collections.emptyList();
        }
        PayPeriodService payPeriodService = ParseContractExcel.payTypeHandleMap.get(paymentType);
        if (Objects.isNull(payPeriodService)) {
            log.error("未找到({})付款方式处理器", paymentType);
            return Collections.emptyList();
        }
        //是否有免租
        List<ContractPricePeriodEntity> pricePeriodEntities = pricePeriodService.lambdaQuery()
                .eq(ContractPricePeriodEntity::getProjectId, projectEntity.getId())
                .eq(ContractPricePeriodEntity::getSubContractId, BooleFlagEnum.NO.getCode())
                .list();
        if (CollectionUtils.isEmpty(pricePeriodEntities)) {
            log.error("项目({})价格周期缺失", projectEntity.getId());
            return Collections.emptyList();
        }
        return paymentPeriodEntities(contractEntity, projectEntity, pricePeriodEntities, paymentPeriodEntities, payPeriodService);
    }

    private List<ContractPaymentPeriodEntity> dealSubPaymentPeriod(ContractEntity contractEntity, ContractProjectEntity projectEntity) {
        //处理子合同的付款周期 默认一个项目只有一个子合同
        List<ContractPaymentPeriodEntity> paymentPeriodEntitiesSub = paymentPeriodService.lambdaQuery()
                .eq(ContractPaymentPeriodEntity::getProjectId, projectEntity.getId())
                .ne(ContractPaymentPeriodEntity::getSubContractId, BooleFlagEnum.NO.getCode())
                .orderByAsc(ContractPaymentPeriodEntity::getId)
                .list();
        if (CollectionUtils.isEmpty(paymentPeriodEntitiesSub)) {
            log.error("项目({})无子合同下的付款周期", projectEntity.getId());
            return Collections.emptyList();
        }
        //根据项目的付款方式和 签约的年限 开始时间结束时间  免租等计算每个付款周期记录的开始结束时间
        List<ContractSubEntity> subEntities = subService.lambdaQuery()
                .eq(ContractSubEntity::getProjectId, projectEntity.getId())
                .list();
        if (CollectionUtils.isEmpty(subEntities)) {
            log.error("项目({})无子合同", projectEntity.getId());
            return Collections.emptyList();
        }
        if (subEntities.size() > 1) {
            log.error("项目({})存在多个子合同", projectEntity.getId());
        }
        String paymentTypeSub = subEntities.get(0).getPaymentType();
        PayPeriodService payPeriodService = ParseContractExcel.payTypeHandleMap.get(paymentTypeSub);
        if (Objects.isNull(payPeriodService)) {
            log.error("未找到({})付款方式处理器", paymentTypeSub);
            return Collections.emptyList();
        }
        //付款方式决定间隔
        //是否有免租
        List<ContractPricePeriodEntity> pricePeriodEntitiesSub = pricePeriodService.lambdaQuery()
                .eq(ContractPricePeriodEntity::getProjectId, projectEntity.getId())
                .ne(ContractPricePeriodEntity::getSubContractId, BooleFlagEnum.NO.getCode())
                .list();
        if (CollectionUtils.isEmpty(pricePeriodEntitiesSub)) {
            log.error("项目({})无子合同价格周期", projectEntity.getId());
            return Collections.emptyList();
        }
        return paymentPeriodEntities(contractEntity, projectEntity, pricePeriodEntitiesSub, paymentPeriodEntitiesSub, payPeriodService);
    }

    private static List<ContractPaymentPeriodEntity> paymentPeriodEntities(ContractEntity contractEntity, ContractProjectEntity projectEntity, List<ContractPricePeriodEntity> pricePeriodEntities, List<ContractPaymentPeriodEntity> paymentPeriodEntities, PayPeriodService payPeriodService) {
        try {
            if (pricePeriodEntities.size() == 1) {
                //无免租
                LocalDate start = contractEntity.getStartDate();
                LocalDate end = null;
                for (ContractPaymentPeriodEntity contractPaymentPeriodEntity : paymentPeriodEntities) {
                    //设置开始结束日期 和 计划付款日
                    contractPaymentPeriodEntity.setStartDate(start);
                    end = payPeriodService.getEndDate(start, contractEntity.getEndDate());
                    contractPaymentPeriodEntity.setEndDate(end);
                    contractPaymentPeriodEntity.setPlanPaymentDate(start.minusDays(7));
                    start = end.plusDays(1);
                    end = null;
                }
                paymentPeriodEntities.get(paymentPeriodEntities.size() - 1)
                        .setEndDate(contractEntity.getEndDate());
            } else if (pricePeriodEntities.size() == 2) {
                //有免租 分为免在前和免在后
                if (pricePeriodEntities.get(0).getFreeFlag() == BooleFlagEnum.NO.getCode().intValue()) {
                    //第一条非免租 则免在后
                    LocalDate start = contractEntity.getStartDate();
                    LocalDate end = null;
                    for (ContractPaymentPeriodEntity contractPaymentPeriodEntity : paymentPeriodEntities) {
                        //设置开始结束日期 和 计划付款日
                        contractPaymentPeriodEntity.setStartDate(start);
                        end = payPeriodService.getEndDate(start, contractEntity.getEndDate());
                        contractPaymentPeriodEntity.setEndDate(end);
                        contractPaymentPeriodEntity.setPlanPaymentDate(start.minusDays(7));
                        start = end.plusDays(1);
                        end = null;
                    }
                    paymentPeriodEntities.get(paymentPeriodEntities.size() - 1)
                            .setEndDate(pricePeriodEntities.get(1).getStartDate().minusDays(1));
                } else {
                    //免在前 第一条付款周期开始日期为免租结束+1
                    LocalDate start = pricePeriodEntities.get(0).getEndDate().plusDays(1);
                    LocalDate end = null;
                    for (ContractPaymentPeriodEntity contractPaymentPeriodEntity : paymentPeriodEntities) {
                        //设置开始结束日期 和 计划付款日
                        contractPaymentPeriodEntity.setStartDate(start);
                        end = payPeriodService.getEndDate(start, contractEntity.getEndDate());
                        contractPaymentPeriodEntity.setEndDate(end);
                        contractPaymentPeriodEntity.setPlanPaymentDate(start.minusDays(7));
                        start = end.plusDays(1);
                        end = null;
                    }
                    paymentPeriodEntities.get(paymentPeriodEntities.size() - 1)
                            .setEndDate(contractEntity.getEndDate());
                }
            } else {
                log.error("项目({})存在两个以上价格周期", projectEntity.getId());
                return Collections.emptyList();
            }
            return paymentPeriodEntities;
        } catch (Exception e) {
            log.error("补全付款周期出错了,项目id:({})", projectEntity.getId());
            return Collections.emptyList();
        }
    }
}
