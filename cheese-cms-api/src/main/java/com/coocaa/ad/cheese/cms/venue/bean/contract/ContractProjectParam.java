package com.coocaa.ad.cheese.cms.venue.bean.contract;

import com.alibaba.fastjson2.annotation.JSONField;
import com.coocaa.ad.cheese.cms.common.serializer.EncryptDeserializer;
import com.coocaa.ad.cheese.cms.common.tools.common.enums.BooleFlagEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.constant.VenueConstants;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.PaymentTypeEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.PropertyTypeEnum;
import com.coocaa.ad.cheese.cms.common.validation.EnumType;
import com.coocaa.ad.cheese.cms.common.validation.ValidationGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.groups.Default;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 合同-项目 参数
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-04
 */
@Data
public class ContractProjectParam {
    @NotBlank(message = "项目编码不能为空", groups = {Default.class, ValidationGroup.ContractAgreement.class, ValidationGroup.ContractAmendment.class})
    @Schema(description = "项目编码(来自楼宇评级)", type = "String", example = "PRJ001")
    private String projectCode;

    @NotBlank(message = "项目名称不能为空", groups = {Default.class, ValidationGroup.ContractAgreement.class, ValidationGroup.ContractAmendment.class})
    @Schema(description = "项目名称", type = "String", example = "XX大厦")
    private String projectName;

    @EnumType(message = "物业类型不正确", value = PropertyTypeEnum.class)
    @NotBlank(message = "物业类型不能为空", groups = {Default.class, ValidationGroup.ContractAgreement.class, ValidationGroup.ContractAmendment.class})
    @Schema(description = "物业类型(来自楼宇评级, 转字典)", type = "String", example = "写字楼")
    private String propertyType;

    @NotNull(message = "城市不能为空", groups = {Default.class, ValidationGroup.ContractAgreement.class, ValidationGroup.ContractAmendment.class})
    @Schema(description = "城市ID(来自楼宇评级, 转城市编码)", type = "Int", example = "440300")
    private Integer cityId;

    @NotBlank(message = "省市区不能为空", groups = {Default.class, ValidationGroup.ContractAgreement.class, ValidationGroup.ContractAmendment.class})
    @Schema(description = "省市区", type = "String", example = "广东省深圳市南山区")
    private String areaName;

    // @JSONField(deserializeUsing = EncryptDeserializer.class)
    @Schema(description = "详细地址", type = "String", example = "深南大道1000号")
    private String address;

    @Schema(description = "楼宇等级", type = "String", example = "A")
    private String level;

    @NotBlank(message = "付款方式不能为空", groups = {Default.class, ValidationGroup.ContractAgreement.class, ValidationGroup.ContractAmendment.class})
    @EnumType(message = "付款方式不正确", value = PaymentTypeEnum.class)
    @Schema(description = "付款方式(字典0027 季付、半年、一年、一次性、其它)", type = "String", example = "0027-3")
    private String paymentType;

    @NotNull(message = "付款周期前N天支付不能为空", groups = {Default.class, ValidationGroup.ContractAgreement.class, ValidationGroup.ContractAmendment.class})
    @Schema(description = "付款周期前N天支付", type = "Int", example = "30")
    private Integer intervalDay;

    @NotNull(message = "项目开始日期不能为空", groups = {Default.class, ValidationGroup.ContractAgreement.class, ValidationGroup.ContractAmendment.class})
    @JsonFormat(pattern = VenueConstants.DATE_FORMAT)
    @Schema(description = "项目开始日期", type = "String", example = "2024-01-01")
    private LocalDate startDate;

    @NotNull(message = "项目结束日期不能为空", groups = {Default.class, ValidationGroup.ContractAgreement.class, ValidationGroup.ContractAmendment.class})
    @JsonFormat(pattern = VenueConstants.DATE_FORMAT)
    @Schema(description = "项目结束日期", type = "String", example = "2024-12-31")
    private LocalDate endDate;

    @NotNull(message = "项目年限不能为空", groups = {Default.class, ValidationGroup.ContractAgreement.class, ValidationGroup.ContractAmendment.class})
    @DecimalMin(value = "0.01", message = "项目年限必须大于0")
    @Schema(description = "项目年限", type = "BigDecimal", example = "1.00")
    private BigDecimal period;

    @NotNull(message = "项目金额不能为空", groups = {Default.class, ValidationGroup.ContractAgreement.class, ValidationGroup.ContractAmendment.class})
    @DecimalMin(value = "0.01", message = "项目金额必须大于0")
    @Schema(description = "项目金额", type = "BigDecimal", example = "1.00")
    private BigDecimal amount;

    @NotNull(message = "请选择是否有押金", groups = {Default.class, ValidationGroup.ContractAgreement.class, ValidationGroup.ContractAmendment.class})
    @EnumType(message = "是否有押金标记不正确", value = BooleFlagEnum.class)
    @Schema(description = "是否有押金 [0:无, 1:有]", type = "Int", example = "0")
    private Integer depositFlag;

    @Schema(description = "押金金额", type = "BigDecimal", example = "10000.00")
    private BigDecimal depositAmount;

    @JsonFormat(pattern = VenueConstants.DATE_FORMAT)
    @Schema(description = "押金付款时间", type = "String", example = "2024-01-01")
    private LocalDate depositPaymentDate;

    @Schema(description = "押金收款方", type = "Int", example = "1")
    private Integer depositSupplierId;

    @Schema(description = "押金收款方银行账号", example = "1")
    @JSONField(deserializeUsing = EncryptDeserializer.class)
    private String depositSupplierAccountNo;

    @Schema(description = "押金收款方银行账号id", example = "1")
    private Integer depositSupplierBankId;

    @Valid
    @NotEmpty(message = "项目价格不能为空", groups = {Default.class, ValidationGroup.ContractAgreement.class, ValidationGroup.ContractAmendment.class})
    @Schema(description = "项目价格申请列表")
    private List<ContractPriceApplyParam> priceApplies;

    @Valid
    @Schema(description = "子合同列表")
    private List<SubContractParam> subContracts;
    @Schema(description = "top值", type = "String", example = "1级")
    private String topLevel;

    @Schema(description = "项目ai评级", type = "String", example = "AAA")
    private String aiLevel;
}
