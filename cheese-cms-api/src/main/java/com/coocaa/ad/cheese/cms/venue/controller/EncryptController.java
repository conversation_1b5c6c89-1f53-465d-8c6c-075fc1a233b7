package com.coocaa.ad.cheese.cms.venue.controller;

import com.coocaa.ad.cheese.cms.common.tools.common.result.ResultTemplate;
import com.coocaa.ad.cheese.cms.common.tools.common.util.AesUtils;
import com.coocaa.ad.cheese.cms.venue.vo.EncryptVO;
import com.coocaa.ad.common.anno.AuthIgnore;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 验证加解密
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-12
 */
@Slf4j
@RestController
@RequestMapping("/venue/encrypt")
@Tag(name = "验证加解密", description = "验证加解密")
public class EncryptController extends BaseController {
    /**
     * 加解密
     */
    @AuthIgnore
    @Operation(summary = "加解密")
    @PostMapping
    public ResultTemplate<EncryptVO> encrypt(@RequestBody EncryptVO encrypt) {
        // 明文
        if (StringUtils.isNotBlank(encrypt.getPlain())) {
            encrypt.setWeb(encrypt.getPlain());
            encrypt.setStorage(AesUtils.encryptHex(encrypt.getPlain()));
            return ResultTemplate.success(encrypt);
        }

        // 数据库存储
        if (StringUtils.isNotBlank(encrypt.getStorage())) {
            encrypt.setPlain(AesUtils.decryptStr(encrypt.getStorage()));
            encrypt.setWeb(encrypt.getPlain());
            return ResultTemplate.success(encrypt);
        }

        // 前端交互
        if (StringUtils.isNotBlank(encrypt.getWeb())) {
            encrypt.setPlain(encrypt.getWeb());
            encrypt.setStorage(AesUtils.encryptHex(encrypt.getPlain()));
            return ResultTemplate.success(encrypt);
        }

        return ResultTemplate.success(encrypt);
    }
}
