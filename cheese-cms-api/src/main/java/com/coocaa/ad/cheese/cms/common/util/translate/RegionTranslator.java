package com.coocaa.ad.cheese.cms.common.util.translate;

import com.coocaa.ad.cheese.cms.common.db.venue.entity.RegionEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IRegionService;
import com.coocaa.ad.translate.Translator;
import com.coocaa.ad.translate.util.TransUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 大区翻译器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-30
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class RegionTranslator implements Translator<Integer> {
    private final IRegionService regionService;

    @Override
    public String getTransType() {
        return VenueTransTypes.REGION;
    }

    @Override
    public Class<Integer> getDataType() {
        return Integer.class;
    }

    @Override
    public Map<Integer, String> getMapping(Collection<Integer> sourceValues) {
        return TransUtils.toNumValMap(sourceValues, ids -> regionService.lambdaQuery()
                .select(RegionEntity::getId, RegionEntity::getName)
                .in(RegionEntity::getId, ids)
                .list().stream()
                .collect(Collectors.toMap(RegionEntity::getId, RegionEntity::getName, (o, n) -> n)));
    }
}
