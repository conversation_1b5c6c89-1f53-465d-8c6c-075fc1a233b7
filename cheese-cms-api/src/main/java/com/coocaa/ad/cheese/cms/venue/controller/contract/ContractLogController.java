package com.coocaa.ad.cheese.cms.venue.controller.contract;

import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractHistoryEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractLogEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractHistoryService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractLogService;
import com.coocaa.ad.cheese.cms.common.tools.common.enums.BooleFlagEnum;
import com.coocaa.ad.cheese.cms.venue.controller.BaseController;
import com.coocaa.ad.cheese.cms.venue.convert.contract.ContractConvert;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractHistoryVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractLogVO;
import com.coocaa.ad.common.result.ResultTemplate;
import com.coocaa.ad.translate.anno.AutoTranslate;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 物业合同日志
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-09
 */
@Slf4j
@RestController
@RequestMapping("/venue/contract/{contractId}/logs")
@Tag(name = "物业合同日志", description = "物业合同日志")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ContractLogController extends BaseController {
    private final IContractLogService contractLogService;
    private final IContractHistoryService contractHistoryService;
    private final ContractConvert contractConvert;

    /**
     * 合同操作日志列表
     */
    @Operation(summary = "合同操作日志列表")
    @Parameter(name = "contractId", description = "合同ID", required = true, example = "1")
    @GetMapping
    @AutoTranslate
    public ResultTemplate<List<ContractLogVO>> listLogs(@PathVariable(name = "contractId") Integer contractId) {
        List<ContractLogEntity> entities = contractLogService.lambdaQuery()
                .eq(ContractLogEntity::getContractId, contractId)
                .orderByDesc(ContractLogEntity::getId)
                .list().stream().toList();
        if (CollectionUtils.isEmpty(entities)) {
            return ResultTemplate.success(Collections.emptyList());
        }

        // 查询操作历史
        Map<Integer, List<ContractHistoryVO>> historyMap = contractHistoryService.lambdaQuery()
                .in(ContractHistoryEntity::getLogId, entities.stream().map(ContractLogEntity::getId).collect(Collectors.toSet()))
                .eq(ContractHistoryEntity::getHide, BooleFlagEnum.NO.getCode())
                .list().stream().collect(Collectors.groupingBy(ContractHistoryEntity::getLogId, Collectors.mapping(e -> {
                    ContractHistoryVO vo = new ContractHistoryVO();
                    BeanUtils.copyProperties(e, vo);
                    return vo;
                }, Collectors.toList())));

        return ResultTemplate.success(entities.stream().map(contractConvert::toVO)
                .peek(vo -> vo.setHistory(historyMap.getOrDefault(vo.getId(), List.of())))
                .toList());
    }
}
