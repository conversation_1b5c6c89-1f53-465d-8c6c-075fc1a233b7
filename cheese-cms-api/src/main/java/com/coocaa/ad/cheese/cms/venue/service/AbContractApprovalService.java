package com.coocaa.ad.cheese.cms.venue.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.ad.cheese.cms.common.db.venue.bean.AbContractQueryDTO;
import com.coocaa.ad.cheese.cms.common.db.venue.bean.AbContractResubmitDTO;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractAbnormalDealEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractAbnormalEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractAbnormalProjectEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractAttachmentEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractSupplierEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ExamineApproveEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractAbnormalDealService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractAbnormalService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractAttachmentService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractSupplierService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IExamineApproveService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IRegionService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.supplier.IContractAbnormalProjectService;
import com.coocaa.ad.cheese.cms.common.exception.BusinessException;
import com.coocaa.ad.cheese.cms.common.tools.common.config.ApiConfig;
import com.coocaa.ad.cheese.cms.common.tools.common.enums.BooleFlagEnum;
import com.coocaa.ad.cheese.cms.common.tools.common.result.PageRequestVo;
import com.coocaa.ad.cheese.cms.common.tools.common.result.ResultTemplate;
import com.coocaa.ad.cheese.cms.common.tools.common.util.AesUtils;
import com.coocaa.ad.cheese.cms.common.tools.venue.bean.ContractAbnormalApprovalDTO;
import com.coocaa.ad.cheese.cms.common.tools.venue.bean.ContractAbnormalDealUserDTO;
import com.coocaa.ad.cheese.cms.common.tools.venue.constant.VenueConstants;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.AbContractDealTypeEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.AbContractNodeEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.AbContractOperateEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.AbnormalOpinionEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ApplyStatusEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.BusinessBelongEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractAbnormalFlagEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.DealTypeEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ExamineApproveEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.RegionEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.StatusChangeTypeEnum;
import com.coocaa.ad.cheese.cms.common.util.converter.ConverterFactory;
import com.coocaa.ad.cheese.cms.venue.bean.contract.AbContractApprovalParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.AbContractOperateParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.AbContractQueryParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractAbnormalDealAddParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractTemplateCommonParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.OperateLogParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.StatusChangeLogParam;
import com.coocaa.ad.cheese.cms.venue.convert.contract.AbContractConvert;
import com.coocaa.ad.cheese.cms.venue.convert.contract.AbContractDetailConvert;
import com.coocaa.ad.cheese.cms.venue.convert.contract.AbContractQueryConvert;
import com.coocaa.ad.cheese.cms.venue.convert.contract.ContractAbnormalDealConvert;
import com.coocaa.ad.cheese.cms.venue.convert.contract.ContractAbnormalProjectConvert;
import com.coocaa.ad.cheese.cms.venue.convert.contract.ContractAttachmentConvert;
import com.coocaa.ad.cheese.cms.venue.rpc.FeignApprovalRpc;
import com.coocaa.ad.cheese.cms.venue.rpc.bean.ApprovalInitiateParam;
import com.coocaa.ad.cheese.cms.venue.rpc.bean.ApprovalResubmitParam;
import com.coocaa.ad.cheese.cms.venue.rpc.bean.ApprovalTaskListParam;
import com.coocaa.ad.cheese.cms.venue.rpc.bean.ApprovalTaskOperateParam;
import com.coocaa.ad.cheese.cms.venue.rpc.bean.ApprovalTaskRollbackParam;
import com.coocaa.ad.cheese.cms.venue.rpc.bean.ApprovalTaskUserListParam;
import com.coocaa.ad.cheese.cms.venue.rpc.bean.RollbackNodeParam;
import com.coocaa.ad.cheese.cms.venue.rpc.vo.ApprovalNodeVO;
import com.coocaa.ad.cheese.cms.venue.rpc.vo.ApprovalRollbackVO;
import com.coocaa.ad.cheese.cms.venue.rpc.vo.ApprovalTaskListVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.AbContractApprovalDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.AbContractApprovalVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractAbnormalDealVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractAbnormalProjectVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractAttachmentVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ExamineApproveVO;
import com.coocaa.ad.common.core.context.TokenThreadLocal;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.ad.common.result.PageResponseVO;
import com.coocaa.ad.translate.anno.AutoTranslate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 异常合同审批相关
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/3/24
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class AbContractApprovalService {
    private static final long CONSTANT_ZERO_L = 0L;
    private static final int CONSTANT_ZERO = 0;
    private static final int CONSTANT_ONE = 1;
    private static final int CONSTANT_TWO = 2;
    private static final String CONSTANT_ONE_STR = "1";
    private static final String APPROVAL_RESUBMIT_VALE = "contract:abnormal";
    private static final String ABNORMAL_CONTRACT_KEY = "0091-2";

    private final OperationLogService logService;
    private final FeignApprovalRpc feignApprovalRpc;
    private final IExamineApproveService examineApproveService;
    private final IContractAbnormalService contractAbnormalService;
    private final IContractAbnormalDealService contractAbnormalDealService;
    private final IContractAbnormalProjectService contractAbnormalProjectService;
    private final IContractAttachmentService contractAttachmentService;
    private final ConverterFactory converterFactory;
    private final StringRedisTemplate stringRedisTemplate;
    private final ThreadPoolExecutor threadPoolExecutor;
    private final StatusChangeLogService statusChangeLogService;
    private final IRegionService regionService;
    private final ApiConfig apiConfig;
    private final IContractService contractService;
    private final ApprovalMessageService approvalMessageService;
    private final IContractSupplierService contractSupplierService;

    /*
     * <AUTHOR>
     * @Description 查询待审批列表
     * @Date 2025/3/25
     * @Param [pageRequest]
     * @return com.coocaa.ad.cheese.cms.common.tools.common.result.PageResponseVo<com.coocaa.ad.cheese.cms.venue.vo.contract.AbContractApprovalVO>
     **/
    @AutoTranslate
    public PageResponseVO<AbContractApprovalVO> pageListAbContract(PageRequestVo<AbContractQueryParam> pageRequest) {
        //获取是否是普通用户
        Boolean ordinaryUser = isOrdinaryUser();
        log.info("是否是普通用户id:{}, ordinaryUser:{}", UserThreadLocal.getUserId(), ordinaryUser);
        //查询飞书待审批列表
        ResultTemplate<List<ApprovalTaskListVO.Task>> listResultTemplate = queryPendingTaskList(ordinaryUser);
        log.info("异常合同待审批任务查询权限系统列表查询结果:{}", JSONUtil.toJsonStr(listResultTemplate));
        if (!listResultTemplate.getSuccess() || CollectionUtil.isEmpty(listResultTemplate.getData())) {
            return new PageResponseVO<>(CONSTANT_ZERO_L);
        }
        Set<String> processCodes = listResultTemplate.getData().stream()
                .map(ApprovalTaskListVO.Task::getProcessCode)
                .collect(Collectors.toSet());
        //查询审批业务关联表对应异常合同id
        List<ExamineApproveEntity> examineApproveEntities = examineApproveService.queryBizId(processCodes);
        log.info("查询待审批业务关联表对应异常合同id:{}", JSONUtil.toJsonStr(examineApproveEntities));
        if (ordinaryUser && CollectionUtil.isEmpty(examineApproveEntities)) {
            return new PageResponseVO<>(CONSTANT_ZERO_L);
        }
        //后门名单进来的examineApproveEntities可能会为空
        Map<Integer, ExamineApproveEntity> examineApproveEntityMap = CollectionUtil.isEmpty(examineApproveEntities) ?
                null : examineApproveEntities.stream().collect(Collectors.toMap(ExamineApproveEntity::getBizId, entity -> entity));
        Map<String, ApprovalTaskListVO.Task> taskMap = listResultTemplate.getData().stream()
                .collect(Collectors.toMap(ApprovalTaskListVO.Task::getProcessCode, task -> task));
        // 转换查询参数
        AbContractQueryDTO queryDto = toExamineQueryDto(ordinaryUser, pageRequest.getQuery(), examineApproveEntities);
        log.info("异常合同待审批任务列表查询入参:{}", JSONUtil.toJsonStr(queryDto));
        IPage<ContractAbnormalApprovalDTO> pageContract = contractAbnormalService.pageListExamineApprove(getPage(pageRequest), queryDto);
        log.info("异常合同待审批任务列表查询结果:{}", JSONUtil.toJsonStr(pageContract));
        if (CollectionUtils.isEmpty(pageContract.getRecords())) {
            return new PageResponseVO<>(CONSTANT_ZERO_L);
        }
        //查询供应商
        Map<Integer, String> supplierMap = querySupplier(pageContract.getRecords());
        pageContract.getRecords().forEach(dto -> {
            //设置是否普通用户操作前端控制审批按钮
            dto.setOrdinaryUser(ordinaryUser);
            //设置供应商反显列表
            if (CollectionUtil.isNotEmpty(supplierMap)) {
                dto.setAgentName(supplierMap.get(dto.getContractId()));
            }
            //设置审批节点信息
            setNodeInfo(dto, examineApproveEntityMap, taskMap);
        });
        return AbContractConvert.INSTANCE.toPageResponse(pageContract);
    }

    /*
     * <AUTHOR>
     * @Description 发起审批
     * @Date 2025/3/24
     * @Param [id:异常合同id]
     * @return void
     **/
    @Transactional(rollbackFor = Exception.class)
    public Boolean notifyOnApproval(Integer id, String applyCode, Integer cityId, ContractAbnormalDealAddParam param) {

        log.info("发起审批id: {},applyCode: {},param: {},cityId: {}", id, applyCode, JSONUtil.toJsonStr(param), cityId);
        checkApprovalDate(id, applyCode, param, cityId);
        //修改异常合同
        updateAbContract(id);
        log.info("发起审批修改异常合同状态成功");
        //保存处理方式表
        saveAbContractDeal(id, param);
        log.info("发起审批保存处理方式表成功");
        //保存操作日志
        saveOperateLog(id, AbContractDealTypeEnum.ABNORMAL_CONTRACT_ADD, AbContractOperateEnum.CREATE_CONTRACT, null);
        log.info("发起审批保存操作日志成功");
        //保存状态变更日志
        saveStatusLog(id);
        log.info("发起审批保存状态变更日志成功");
        //发起飞书异常合同审批
        applyApproval(id, applyCode, cityId);
        log.info("发起审批发起飞书异常合同审批成功");
        //发起审批处理原合同异常状态
        updateContractStatus(id);

        return Boolean.TRUE;
    }

    /*
     * <AUTHOR>
     * @Description 撤回合同
     * @Date 2025/3/25
     * @Param [id]
     * @return java.lang.Boolean
     **/
    @Transactional(rollbackFor = Exception.class)
    public Boolean withdrawContract(Integer id) {
        log.info("撤回合同id: {}, userId:{}", id, UserThreadLocal.getUserId());
        String processCode = examineApproveService.queryPendingContract(id, UserThreadLocal.getUserId());
        log.info("撤回合同查询异常合同审批实例processCode:{}", processCode);
        if (Strings.isBlank(processCode)) {
            throw new BusinessException("无法撤回");
        }
        ApprovalInitiateParam approvalInitiateParam = new ApprovalInitiateParam();
        approvalInitiateParam.setApproveType(ABNORMAL_CONTRACT_KEY);
        approvalInitiateParam.setProcessCode(processCode);
        log.info("发起撤回异常合同开始id:{}, param:{}", id, JSONUtil.toJsonStr(approvalInitiateParam));
        ResultTemplate<String> stringResultTemplate = feignApprovalRpc.cancelApproval(approvalInitiateParam);
        log.info("撤回异常合同结束id:{}, param:{}", id, JSONUtil.toJsonStr(stringResultTemplate));
        if (!stringResultTemplate.getSuccess() || Objects.isNull(stringResultTemplate.getData())) {
            throw new BusinessException("异常合同飞书撤回失败");
        }
        //立即修改异常合同状态，不然web端页面刷新会存在走kafka数据延迟问题
        contractAbnormalService.lambdaUpdate()
                .set(ContractAbnormalEntity::getApplyStatus, ApplyStatusEnum.WITHDRAW.getCode())
                .eq(ContractAbnormalEntity::getId, id)
                .eq(ContractAbnormalEntity::getApplyStatus, ApplyStatusEnum.CHECKING.getCode())
                .update();
        return Boolean.TRUE;
    }

    /*
     * <AUTHOR>
     * @Description 再次发起合同
     * @Date 2025/3/25
     * @Param [id]
     * @return java.lang.Boolean
     **/
    @Transactional(rollbackFor = Exception.class)
    public Boolean resubmitContract(Integer id, ContractAbnormalDealAddParam param) {
        log.info("再次发起合同id: {},param: {}", id, JSONUtil.toJsonStr(param));
        //再次再次发起异常合同审批校验数据
        checkResubmitDate(id, param);
        log.info("再次发起合同校验数据成功");
        //只有撤回、退回、驳回的合同可以再次发起
        Set<String> resubmitStatus = Set.of(ApplyStatusEnum.WITHDRAW.getCode(), ApplyStatusEnum.GO_BACK.getCode(), ApplyStatusEnum.REJECT.getCode());
        AbContractResubmitDTO contractResubmitDTO = contractAbnormalService.queryResubmitContract(id, resubmitStatus);
        log.info("再次发起合同查询结果:{}", JSONUtil.toJsonStr(contractResubmitDTO));
        if (Objects.isNull(contractResubmitDTO)) {
            throw new BusinessException("再次发起合同异常合同不存在，不允许重新发起");
        }
        //校验再次发起合同是否为本人合同
        if (!Objects.equals(UserThreadLocal.getUserId(), contractResubmitDTO.getCreator())) {
            throw new BusinessException("再次发起合同该异常合同不属于您，无法重新发起");
        }
        log.info("再次发起合同是本人操作");
        if (Objects.equals(ApplyStatusEnum.WITHDRAW.getCode(), contractResubmitDTO.getApplyStatus())
                || Objects.equals(ApplyStatusEnum.REJECT.getCode(), contractResubmitDTO.getApplyStatus())) {
            //如果是撤回或者驳回的则调用新建合同接口
            return notifyOnApproval(id, contractResubmitDTO.getApplyCode(), contractResubmitDTO.getCityId(), param);
        }
        if (Objects.equals(ApplyStatusEnum.GO_BACK.getCode(), contractResubmitDTO.getApplyStatus())) {
            //如果是退回调用重新提交合同接口
            return resubmitApproval(id, contractResubmitDTO, param);
        }

        return Boolean.FALSE;
    }

    /*
     * <AUTHOR>
     * @Description 填写处理结果
     * @Date 2025/3/25
     * @Param [id, param]
     * @return java.lang.Boolean
     **/
    @Transactional(rollbackFor = Exception.class)
    public Boolean writeHandleResult(Integer id, AbContractOperateParam param) {
        log.info("填写处理结果id: {},param: {}", id, JSONUtil.toJsonStr(param));
        String redisKey = String.format("contract:abnormal:%s", id);
        Boolean locked = stringRedisTemplate.opsForValue().setIfAbsent(redisKey, APPROVAL_RESUBMIT_VALE, CONSTANT_TWO, TimeUnit.SECONDS);
        try {
            if (!locked) {
                throw new BusinessException("请勿重复提交");
            }
            //修改合同状态
            boolean updateAbnormal = contractAbnormalService.lambdaUpdate()
                    .set(ContractAbnormalEntity::getApplyStatus, ApplyStatusEnum.FINISH.getCode())
                    .set(ContractAbnormalEntity::getHandleTime, LocalDateTime.now())
                    .set(ContractAbnormalEntity::getHandleRemark, param.getRemark())
                    .eq(ContractAbnormalEntity::getId, id)
                    .eq(ContractAbnormalEntity::getApplyStatus, ApplyStatusEnum.PROCESSING.getCode())
                    .update();
            //保存处理方式
            ContractAbnormalDealEntity contractAbnormalDealEntity = new ContractAbnormalDealEntity();
            contractAbnormalDealEntity.setAbnormalId(id);
            contractAbnormalDealEntity.setRemark(param.getRemark());
            contractAbnormalDealEntity.setType(AbContractDealTypeEnum.WRITE_HANDLE_RESULT.getCode());
            contractAbnormalDealEntity.setVersion(generateApprovalVersion(id));
            boolean updateAbnormalDeal = contractAbnormalDealService.save(contractAbnormalDealEntity);
            if (!updateAbnormal || !updateAbnormalDeal) {
                throw new BusinessException("异常合同填写处理结果失败");
            }
            //保存操作日志
            saveOperateLog(id, AbContractDealTypeEnum.WRITE_HANDLE_RESULT, AbContractOperateEnum.WRITE_HANDLE_RESULT, param.getRemark());
            //填写处理结果处理原合同异常状态
            updateWriteContractStatus(id);
        } catch (Exception e) {
            log.error("异常合同填写处理结果失败", e);
        } finally {
            stringRedisTemplate.delete(redisKey);
        }

        return Boolean.TRUE;
    }

    /*
     * <AUTHOR>
     * @Description 余总驳回合同
     * @Date 2025/3/25
     * @Param [id]
     * @return java.lang.Boolean
     **/
    @Transactional(rollbackFor = Exception.class)
    public Boolean finalReject(Integer id) {
        log.info("余总驳回合同id: {}", id);
        String redisKey = String.format("contract:abnormal:%s", id);
        Boolean locked = stringRedisTemplate.opsForValue().setIfAbsent(redisKey, APPROVAL_RESUBMIT_VALE, CONSTANT_TWO, TimeUnit.SECONDS);
        //修改合同状态
        try {
            if (!locked) {
                throw new BusinessException("请勿重复提交");
            }
            boolean update = contractAbnormalService.lambdaUpdate()
                    .set(ContractAbnormalEntity::getApplyStatus, ApplyStatusEnum.REJECT.getCode())
                    .eq(ContractAbnormalEntity::getId, id)
                    .eq(ContractAbnormalEntity::getApplyStatus, ApplyStatusEnum.PROCESSING.getCode())
                    .update();
            //保存处理方式
            ContractAbnormalDealEntity contractAbnormalDealEntity = new ContractAbnormalDealEntity();
            contractAbnormalDealEntity.setAbnormalId(id);
            contractAbnormalDealEntity.setType(AbContractDealTypeEnum.FINAL_REJECT.getCode());
            contractAbnormalDealEntity.setVersion(generateApprovalVersion(id));
            boolean updateAbnormalDeal = contractAbnormalDealService.save(contractAbnormalDealEntity);
            if (!update || !updateAbnormalDeal) {
                throw new BusinessException("余总驳回合同失败");
            }
            //审批余总驳回处理原合同异常状态
            updateContractFinalStatus(id);
        } catch (Exception e) {
            log.error("余总驳回异常合同失败", e);
        } finally {
            stringRedisTemplate.delete(redisKey);
        }

        return Boolean.TRUE;
    }

    /*
     * <AUTHOR>
     * @Description 查询某异常合同详情
     * @Date 2025/3/26
     * @Param [id]
     * @return com.coocaa.ad.cheese.cms.venue.vo.contract.AbContractApprovalDetailVO
     **/
    public AbContractApprovalDetailVO queryAbContractDetail(Integer id) {
        String token = TokenThreadLocal.getToken();
        //查询异常合同详细信息
        CompletableFuture<AbContractApprovalDetailVO> detailFuture = queryApprovalDetail(id);
        //查询合同履约情况
        CompletableFuture<List<ContractAbnormalProjectVO>> projectFuture = queryContractAbnormalProject(id);
        //查询处理意见
        CompletableFuture<List<ContractAbnormalDealVO>> dealFuture = queryContractAbnormalDeal(id);
        //查询附件信息
        CompletableFuture<List<ContractAttachmentVO>> attachmentFuture = queryContractAttachment(id);
        //查询审批流
        CompletableFuture<List<ExamineApproveVO>> approvalFuture = queryExamineApprove(id, token);
        //查询异常合同当前审批节点
        CompletableFuture<String> nodeFuture = queryNodeCode(id, token);

        AtomicReference<AbContractApprovalDetailVO> abContractApprovalDetailVO = new AtomicReference<>(new AbContractApprovalDetailVO());
        CompletableFuture.allOf(detailFuture, projectFuture, dealFuture, attachmentFuture, approvalFuture, nodeFuture)
                .thenRun(() -> {
                    abContractApprovalDetailVO.set(detailFuture.join());
                    abContractApprovalDetailVO.get().setEncryptId(AesUtils.encryptHex(detailFuture.join().getContractId() + ""));
                    abContractApprovalDetailVO.get().setContractAbnormalProjects(projectFuture.join());
                    abContractApprovalDetailVO.get().setContractAbnormalDeals(dealFuture.join());
                    abContractApprovalDetailVO.get().setContractAttachments(attachmentFuture.join());
                    abContractApprovalDetailVO.get().setExamineApproves(approvalFuture.join());
                    abContractApprovalDetailVO.get().setNodeCode(nodeFuture.join());
                }).join();
        return abContractApprovalDetailVO.get();
    }

    /*
     * <AUTHOR>
     * @Description 审批异常合同
     * @Date 2025/3/26
     * @Param [id, abContractApprovalParam]
     * @return java.lang.Boolean
     **/
    @Transactional(rollbackFor = Exception.class)
    public Boolean approvalAbContract(Integer id, AbContractApprovalParam abContractApprovalParam) {
        log.info("审批异常合同id:{}, param:{}", id, JSONUtil.toJsonStr(abContractApprovalParam));
        //查询异常合同信息
        ContractAbnormalEntity contractAbnormalEntity = contractAbnormalService.lambdaQuery()
                .eq(ContractAbnormalEntity::getId, id)
                .eq(ContractAbnormalEntity::getApplyStatus, ApplyStatusEnum.CHECKING.getCode())
                .eq(ContractAbnormalEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .one();
        log.info("审批异常合同查询异常合同信息:{}", JSONUtil.toJsonStr(contractAbnormalEntity));
        if (Objects.isNull(contractAbnormalEntity)) {
            throw new BusinessException("异常合同审批失败");
        }
        //插入处理意见
        ContractAbnormalDealEntity entity = ContractAbnormalDealConvert.INSTANCE.toEntity(abContractApprovalParam);
        entity.setAbnormalId(id);
        entity.setVersion(generateApprovalVersion(id));
        log.info("审批异常合同插入处理意见:{}", JSONUtil.toJsonStr(entity));
        boolean save = contractAbnormalDealService.save(entity);
        if (!save) {
            throw new BusinessException("审批异常合同处理方式表插入异常");
        }
        //财务更新异常合同项目数据
        updateTreasurerProject(id, abContractApprovalParam);
        log.info("审批异常合同财务更新异常合同项目数据成功");
        //法务更新异常合同数据
        updateLegalAbContract(id, abContractApprovalParam);
        log.info("审批异常合同法务更新异常合同数据成功");
        //媒资负责人更新异常合同数据
        updateMediaAbContract(id, abContractApprovalParam);
        log.info("审批异常合同媒资更新异常合同数据成功");
        //更新操作日志表数据
        updateOperateLog(id, abContractApprovalParam);
        log.info("审批异常合同更新操作日志表数据成功");
        //发起飞书审批
        approve(id, abContractApprovalParam);
        log.info("审批异常合同发起飞书审批成功");

        return Boolean.TRUE;
    }

    /**
     * 判断是否是普通用户
     */
    private Boolean isOrdinaryUser() {

        //从飞书获取后门名单
        List<String> pendingList;
        String abContractPending = apiConfig.abContractPending;
        log.info("判断是否是普通用户,飞书后门名单:{}", abContractPending);
        if (Strings.isNotBlank(abContractPending)) {
            pendingList = Arrays.asList(abContractPending.split(","));
            if (pendingList.contains(UserThreadLocal.getUserId().toString())) {
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 查询飞书待审批列表
     */
    private ResultTemplate<List<ApprovalTaskListVO.Task>> queryPendingTaskList(Boolean adminFlag) {

        //从飞书查询待审异常合同
        ResultTemplate<List<ApprovalTaskListVO.Task>> listResultTemplate;
        if (adminFlag) {
            ApprovalTaskUserListParam approvalTaskUserListParam = new ApprovalTaskUserListParam();
            approvalTaskUserListParam.setApproveType(ABNORMAL_CONTRACT_KEY);
            approvalTaskUserListParam.setNodeNameFlag(Boolean.TRUE);
            approvalTaskUserListParam.setStatus(CONSTANT_ONE);
            listResultTemplate = feignApprovalRpc.listAllUserApprovalTasks(approvalTaskUserListParam);
            //当这个接口，status的值 = "任务状态,1:待办，2：已办,0:已退回至提交人节点"
            if (listResultTemplate.getSuccess() && CollectionUtil.isNotEmpty(listResultTemplate.getData())) {
                List<ApprovalTaskListVO.Task> list = listResultTemplate.getData().stream().filter(e -> Objects.equals(CONSTANT_ONE_STR, e.getStatus())).toList();
                listResultTemplate.setData(list);
            }
        } else {
            ApprovalTaskListParam approvalTaskListParam = new ApprovalTaskListParam();
            approvalTaskListParam.setApproveType(ABNORMAL_CONTRACT_KEY);
            approvalTaskListParam.setNodeNameFlag(Boolean.TRUE);
            approvalTaskListParam.setUserFlag(Boolean.FALSE);
            approvalTaskListParam.setTaskStatus(List.of(ExamineApproveEnum.PENDING.getCode()));
            listResultTemplate = feignApprovalRpc.listAllApprovalTasks(approvalTaskListParam);
        }

        return listResultTemplate;
    }

    /**
     * 转换查询参数
     */
    private AbContractQueryDTO toExamineQueryDto(Boolean adminFlag, AbContractQueryParam query, List<ExamineApproveEntity> examineApproveEntities) {

        log.info("转换查询参数adminFlag:{}, query:{}, examineApproveEntities:{}", adminFlag, JSONUtil.toJsonStr(query), JSONUtil.toJsonStr(examineApproveEntities));
        AbContractQueryDTO queryDto = AbContractQueryConvert.INSTANCE.toDto(query);
        if (adminFlag) {
            queryDto.setBizIds(examineApproveEntities.stream().map(e -> e.getBizId()).collect(Collectors.toSet()));
        }
        return queryDto;
    }

    /**
     * 查询供应商
     */
    private Map<Integer, String> querySupplier(List<ContractAbnormalApprovalDTO> queryList) {

        List<Integer> contractIds = queryList.stream().map(ContractAbnormalApprovalDTO::getContractId).collect(Collectors.toList());
        //获取原合同id
        List<ContractSupplierEntity> supplierList = contractSupplierService.querySupplierForAbContract(contractIds);
        log.info("待审批列表查询供应商:{}", JSONUtil.toJsonStr(supplierList));
        if (CollectionUtil.isEmpty(supplierList)) {
            return null;
        }
        return supplierList.stream()
                .collect(Collectors.toMap(ContractSupplierEntity::getContractId, entity -> entity.getSupplierName()));
    }

    /**
     * 设置审批节点信息
     */
    private void setNodeInfo(ContractAbnormalApprovalDTO dto, Map<Integer, ExamineApproveEntity> examineApproveEntityMap,
                             Map<String, ApprovalTaskListVO.Task> taskMap) {

        if (CollectionUtil.isNotEmpty(examineApproveEntityMap) && Objects.nonNull(examineApproveEntityMap.get(dto.getAbContractId()))) {
            if (CollectionUtil.isNotEmpty(taskMap)) {
                String processCode = examineApproveEntityMap.get(dto.getAbContractId()).getProcessCode();
                String nodeCode = Objects.isNull(taskMap.get(processCode)) ? "" : taskMap.get(processCode).getNodeCode();
                if (Strings.isNotBlank(nodeCode) && nodeCode.contains("_")) {
                    nodeCode = nodeCode.substring(CONSTANT_ZERO, nodeCode.lastIndexOf("_"));
                }
                dto.setNodeCode(nodeCode);
                dto.setNodeName(Objects.isNull(taskMap.get(processCode)) ? "" : taskMap.get(processCode).getNodeName());
                dto.setUserId(Objects.isNull(taskMap.get(processCode)) ? CONSTANT_ZERO : taskMap.get(processCode).getUserId());
            }
        }
    }

    /**
     * 获取分页对象
     */
    private Page<ContractAbnormalEntity> getPage(PageRequestVo<?> pageRequest) {
        // 分页查询列表，自定义统计SQL
        Page<ContractAbnormalEntity> page = new Page<>(
                Optional.ofNullable(pageRequest.getCurrentPage()).orElse(VenueConstants.DEFAULT_CURRENT_PAGE),
                Optional.ofNullable(pageRequest.getPageSize()).orElse(VenueConstants.DEFAULT_PAGE_SIZE));
        return page;
    }

    /**
     * 发起异常合同审批校验数据
     */
    private void checkApprovalDate(Integer id, String applyCode, ContractAbnormalDealAddParam param, Integer cityId) {

        if (Objects.isNull(id) || Strings.isBlank(applyCode) || Strings.isBlank(param.getDealType()) || Objects.isNull(cityId)) {
            throw new BusinessException("发起异常合同审批校验数据失败");
        }
    }

    /**
     * 再次再次发起异常合同审批校验数据
     */
    private void checkResubmitDate(Integer id, ContractAbnormalDealAddParam param) {

        if (Objects.isNull(id) || Strings.isBlank(param.getDealType())) {
            throw new BusinessException("再次发起异常合同审批校验数据失败");
        }
    }

    /**
     * 修改异常合同
     */
    private void updateAbContract(Integer id) {

        boolean updated = contractAbnormalService.lambdaUpdate()
                .set(ContractAbnormalEntity::getApplyStatus, ApplyStatusEnum.CHECKING.getCode())
                .set(ContractAbnormalEntity::getApplyTime, LocalDateTime.now())
                .eq(ContractAbnormalEntity::getId, id)
                .eq(ContractAbnormalEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .update();
        if (!updated) {
            throw new BusinessException("发起异常合同审批修改异常合同失败");
        }
    }

    /**
     * 保存处理方式表
     */
    private void saveAbContractDeal(Integer id, ContractAbnormalDealAddParam param) {

        ContractAbnormalDealEntity contractAbnormalDealEntity = new ContractAbnormalDealEntity();
        contractAbnormalDealEntity.setAbnormalId(id);
        contractAbnormalDealEntity.setType(AbContractDealTypeEnum.ABNORMAL_CONTRACT_ADD.getCode());
        contractAbnormalDealEntity.setApprovalOpinion(param.getApprovalOpinion());
        contractAbnormalDealEntity.setDealType(param.getDealType());
        contractAbnormalDealEntity.setDealDate(param.getDealDate());
        contractAbnormalDealEntity.setVersion(generateApprovalVersion(id) + CONSTANT_ONE);
        boolean updateAbnormalDeal = contractAbnormalDealService.save(contractAbnormalDealEntity);
        if (!updateAbnormalDeal) {
            throw new BusinessException("异常合同发起审批异常，保存处理方式失败");
        }
    }

    /**
     * 余总驳回处理原合同异常状态
     */
    private void updateContractFinalStatus(Integer id) {

        Integer contractId = contractService.queryApprovalDetail(id);
        boolean updated = contractService.lambdaUpdate()
                .set(ContractEntity::getAbnormalFlag, ContractAbnormalFlagEnum.NONE.getCode())
                .eq(ContractEntity::getId, contractId)
                .update();
        //需要清空主表媒资负责人填写的处理方式
        boolean updateAbContract = contractAbnormalService.lambdaUpdate()
                .set(ContractAbnormalEntity::getDealType, Strings.EMPTY)
                .eq(ContractAbnormalEntity::getId, id)
                .eq(ContractAbnormalEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .update();
        if (!updated || !updateAbContract) {
            throw new BusinessException("余总驳回修改原合同异常状态失败");
        }
    }

    /*
     * <AUTHOR>
     * @Description 查询异常合同详细信息
     * @Date 2025/4/14
     * @Param [id]
     * @return java.util.concurrent.CompletableFuture<com.coocaa.ad.cheese.cms.venue.vo.contract.AbContractApprovalDetailVO>
     **/
    private CompletableFuture<AbContractApprovalDetailVO> queryApprovalDetail(Integer id) {

        return CompletableFuture.supplyAsync(() -> {
            ContractAbnormalApprovalDTO contractAbnormalApprovalDTO = contractAbnormalService.queryAbContractDetail(id);
            log.info("查询某异常合同详情异常合同信息:{}", JSONUtil.toJsonStr(contractAbnormalApprovalDTO));
            if (Objects.isNull(contractAbnormalApprovalDTO)) {
                throw new BusinessException("查询某异常合同详情异常合同不存在");
            }
            AbContractApprovalDetailVO abContractApprovalDetailVO = AbContractDetailConvert.INSTANCE.toVo(contractAbnormalApprovalDTO);
            converterFactory.convertObject(abContractApprovalDetailVO);
            return abContractApprovalDetailVO;
        }, threadPoolExecutor);
    }

    /*
     * <AUTHOR>
     * @Description 查询合同履约情况
     * @Date 2025/4/14
     * @Param [id]
     * @return java.util.concurrent.CompletableFuture<java.util.List<com.coocaa.ad.cheese.cms.venue.vo.contract.ContractAbnormalProjectVO>>
     **/
    private CompletableFuture<List<ContractAbnormalProjectVO>> queryContractAbnormalProject(Integer id) {

        return CompletableFuture.supplyAsync(() -> {
            List<ContractAbnormalProjectVO> approvalMoneys = CollectionUtil.newArrayList();
            List<ContractAbnormalProjectEntity> projectEntityList = contractAbnormalProjectService.lambdaQuery()
                    .eq(ContractAbnormalProjectEntity::getAbnormalId, id)
                    .eq(ContractAbnormalProjectEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                    .list();
            log.info("查询某异常合同详情合同履约情况:{}", JSONUtil.toJsonStr(projectEntityList));
            if (CollectionUtil.isNotEmpty(projectEntityList)) {
                approvalMoneys = projectEntityList.stream().map(ContractAbnormalProjectConvert.INSTANCE::toVo).toList();
            }
            return approvalMoneys;
        }, threadPoolExecutor);
    }

    /*
     * <AUTHOR>
     * @Description 查询处理意见
     * @Date 2025/4/14
     * @Param [id]
     * @return java.util.concurrent.CompletableFuture<java.util.List<com.coocaa.ad.cheese.cms.venue.vo.contract.ContractAbnormalDealVO>>
     **/
    private CompletableFuture<List<ContractAbnormalDealVO>> queryContractAbnormalDeal(Integer id) {

        return CompletableFuture.supplyAsync(() -> {
            List<ContractAbnormalDealVO> abContractHandles = CollectionUtil.newArrayList();
            List<ContractAbnormalDealEntity> contractAbnormalDealEntityList = contractAbnormalDealService.queryAbnormalList(id);
            log.info("查询某异常合同详情处理意见:{}", JSONUtil.toJsonStr(contractAbnormalDealEntityList));
            if (CollectionUtil.isNotEmpty(contractAbnormalDealEntityList)) {
                abContractHandles = contractAbnormalDealEntityList.stream().map(ContractAbnormalDealConvert.INSTANCE::toVo).toList();
                converterFactory.convert(abContractHandles);
            }
            return abContractHandles;
        }, threadPoolExecutor);
    }

    /*
     * <AUTHOR>
     * @Description 查询附件信息
     * @Date 2025/4/14
     * @Param [id]
     * @return java.util.concurrent.CompletableFuture<java.util.List<com.coocaa.ad.cheese.cms.venue.vo.contract.ContractAttachmentVO>>
     **/
    private CompletableFuture<List<ContractAttachmentVO>> queryContractAttachment(Integer id) {

        return CompletableFuture.supplyAsync(() -> {
            List<ContractAttachmentVO> contractAttachments = CollectionUtil.newArrayList();
            List<ContractAttachmentEntity> attachmentEntityList = contractAttachmentService.lambdaQuery()
                    .eq(ContractAttachmentEntity::getContractId, id)
                    .eq(ContractAttachmentEntity::getBelongTo, CONSTANT_TWO)
                    .eq(ContractAttachmentEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                    .list();
            log.info("查询某异常合同详情附件信息:{}", JSONUtil.toJsonStr(attachmentEntityList));
            if (CollectionUtil.isNotEmpty(attachmentEntityList)) {
                contractAttachments = attachmentEntityList.stream().map(ContractAttachmentConvert.INSTANCE::toVo).toList();
                converterFactory.convert(contractAttachments);
            }
            return contractAttachments;
        }, threadPoolExecutor);
    }

    /*
     * <AUTHOR>
     * @Description 查询审批流
     * @Date 2025/4/14
     * @Param [id, token]
     * @return java.util.concurrent.CompletableFuture<java.util.List<com.coocaa.ad.cheese.cms.venue.vo.contract.ExamineApproveVO>>
     **/
    private CompletableFuture<List<ExamineApproveVO>> queryExamineApprove(Integer id, String token) {

        return CompletableFuture.supplyAsync(() -> {
            List<ExamineApproveVO> examineApproves = CollectionUtil.newArrayList();
            List<ContractAbnormalDealEntity> contractAbnormalDealEntityList = contractAbnormalDealService.lambdaQuery()
                    .eq(ContractAbnormalDealEntity::getAbnormalId, id)
                    .orderByDesc(ContractAbnormalDealEntity::getCreateTime)
                    .list();
            log.info("查询某异常合同详情数据库审批流程:{}", JSONUtil.toJsonStr(contractAbnormalDealEntityList));
            if (CollectionUtil.isEmpty(contractAbnormalDealEntityList)) {
                return examineApproves;
            }
            //查询下一个审批节点
            String processCode = examineApproveService.queryProcessCode(id);
            if (Strings.isNotBlank(processCode)) {
                ResultTemplate<ApprovalNodeVO> taskNode = feignApprovalRpc.getTaskNode(token, processCode, ABNORMAL_CONTRACT_KEY, Boolean.FALSE);
                log.info("查询某异常合同详情审批流下一个审批流程节点processCode:{},taskNode:{}", processCode, JSONUtil.toJsonStr(taskNode));
                if (taskNode.getSuccess() && Objects.nonNull(taskNode.getData()) && Strings.isNotBlank(taskNode.getData().getNodeCode())) {
                    String nodeCode = taskNode.getData().getNodeCode();
                    if (nodeCode.contains("_")) {
                        nodeCode = nodeCode.substring(CONSTANT_ZERO, nodeCode.lastIndexOf("_"));
                    }
                    String nodeName = AbContractNodeEnum.getDesc(nodeCode);
                    if (Strings.isNotBlank(nodeName)) {
                        ExamineApproveVO examineApproveVO = new ExamineApproveVO();
                        examineApproveVO.setApproveNode(nodeCode);
                        examineApproveVO.setApproveNodeName(nodeName);
                        examineApproveVO.setCreator(taskNode.getData().getUserId());
                        examineApproves.add(examineApproveVO);
                    }
                }
            }
            List<ExamineApproveVO> examineApproveList = contractAbnormalDealEntityList.stream().map(e -> {
                ExamineApproveVO examineApproveVO = new ExamineApproveVO();
                examineApproveVO.setApproveNode(e.getType());
                examineApproveVO.setApprovalOpinion(e.getApprovalOpinion());
                examineApproveVO.setCreator(e.getCreator());
                examineApproveVO.setOperateTime(e.getCreateTime());
                examineApproveVO.setRemark(e.getRemark());
                if (Objects.equals(e.getType(), AbContractDealTypeEnum.REGION.getCode()) || Objects.equals(e.getType(), AbContractDealTypeEnum.LEGAL.getCode())
                        || Objects.equals(e.getType(), AbContractDealTypeEnum.MEDIA.getCode())) {
                    examineApproveVO.setAbnormalReason(e.getAbnormalReason());
                    examineApproveVO.setDealType(e.getDealType());
                    examineApproveVO.setDealDate(e.getDealDate());
                }
                return examineApproveVO;
            }).toList();
            examineApproves.addAll(examineApproveList);
            //转换参数
            converterFactory.convert(examineApproves);
            return examineApproves;
        }, threadPoolExecutor);
    }

    /*
     * <AUTHOR>
     * @Description 查询异常合同当前审批节点
     * @Date 2025/4/14
     * @Param [id, token]
     * @return java.util.concurrent.CompletableFuture<java.lang.String>
     **/
    private CompletableFuture<String> queryNodeCode(Integer id, String token) {

        return CompletableFuture.supplyAsync(() -> {
            String nodeCode = Strings.EMPTY;
            String processCode = examineApproveService.queryProcessCode(id);
            if (Strings.isNotBlank(processCode)) {
                ResultTemplate<ApprovalNodeVO> taskNode = feignApprovalRpc.getTaskNode(token, processCode, ABNORMAL_CONTRACT_KEY, Boolean.TRUE);
                log.info("查询某异常合同详情审批流程节点processCode:{},taskNode:{}", processCode, JSONUtil.toJsonStr(taskNode));
                if (taskNode.getSuccess() && Objects.nonNull(taskNode.getData())) {
                    nodeCode = taskNode.getData().getNodeCode();
                    if (Strings.isNotBlank(nodeCode) && nodeCode.contains("_")) {
                        nodeCode = nodeCode.substring(CONSTANT_ZERO, nodeCode.lastIndexOf("_"));
                    }
                }
            }
            return nodeCode;
        }, threadPoolExecutor);
    }

    /**
     * 保存操作日志
     */
    private void saveOperateLog(Integer id, AbContractDealTypeEnum abContractDealTypeEnum,
                                AbContractOperateEnum abContractOperateEnum, String content) {

        OperateLogParam operateLogParam = new OperateLogParam();
        operateLogParam.setType(BusinessBelongEnum.ABNORMAL_CONTRACT.getCode());
        operateLogParam.setSubType(abContractDealTypeEnum.getCode());
        operateLogParam.setBizId(id);
        operateLogParam.setOperateTime(LocalDateTime.now());
        operateLogParam.setOperateType(abContractOperateEnum.getCode());
        operateLogParam.setContent(content);
        logService.saveLogAsync(operateLogParam);
    }

    /**
     * 保存状态变更日志
     */
    private void saveStatusLog(Integer id) {

        StatusChangeLogParam statusChangeLogParam = new StatusChangeLogParam();
        statusChangeLogParam.setType(StatusChangeTypeEnum.ABNORMAL_CONTRACT.getCode());
        statusChangeLogParam.setBizId(id);
        statusChangeLogParam.setStatus(ApplyStatusEnum.CHECKING.getCode());
        statusChangeLogParam.setChangeTime(LocalDateTime.now());
        statusChangeLogParam.setOperator(UserThreadLocal.getUserId());
        statusChangeLogService.saveChangeLogAsync(statusChangeLogParam);
    }

    /**
     * 发起飞书异常合同审批
     */
    private void applyApproval(Integer id, String applyCode, Integer cityId) {

        ApprovalInitiateParam approvalInitiateParam = new ApprovalInitiateParam();
        approvalInitiateParam.setApproveType(ABNORMAL_CONTRACT_KEY);
        approvalInitiateParam.setFormStr(assembleFormStr(applyCode, cityId));
        log.info("发起飞书异常合同审批:{}", JSONUtil.toJsonStr(approvalInitiateParam));
        ResultTemplate<String> stringResultTemplate = feignApprovalRpc.initiateApproval(approvalInitiateParam);
        log.info("异常合同审批流程创建结果:{}", JSONUtil.toJsonStr(stringResultTemplate));
        if (!stringResultTemplate.getSuccess() || Strings.isBlank(stringResultTemplate.getData())) {
            throw new BusinessException("异常合同飞书审批流程创建失败");
        }
        //保存飞书审批实例code
        ExamineApproveEntity examineApproveEntity = new ExamineApproveEntity();
        examineApproveEntity.setBizId(id);
        examineApproveEntity.setProcessCode(stringResultTemplate.getData());
        boolean save = examineApproveService.save(examineApproveEntity);
        if (!save) {
            throw new BusinessException("异常合同审批流程保存飞书审批实例失败");
        }
        //发送飞书消息
        approvalMessageService.approveSendMessage(id, stringResultTemplate.getData());
    }

    /**
     * 处理原合同异常状态
     */
    private void updateContractStatus(Integer id) {

        Integer contractId = contractService.queryApprovalDetail(id);
        boolean updated = contractService.lambdaUpdate()
                .set(ContractEntity::getAbnormalFlag, ContractAbnormalFlagEnum.SUSPECTED.getCode())
                .eq(ContractEntity::getId, contractId)
                .update();
        if (!updated) {
            throw new BusinessException("异常合同发起审批/重新发起修改原合同异常状态失败");
        }
    }

    /**
     * 飞书发起重新提交异常合同
     */
    private void applyResubmitApproval(Integer id, String processCode, AbContractResubmitDTO contractResubmitDTO) {

        ApprovalResubmitParam approvalResubmitParam = new ApprovalResubmitParam();
        approvalResubmitParam.setApproveType(ABNORMAL_CONTRACT_KEY);
        approvalResubmitParam.setProcessCode(processCode);
        approvalResubmitParam.setFormStr(assembleFormStr(contractResubmitDTO.getApplyCode(), contractResubmitDTO.getCityId()));
        log.info("再次发起异常合同开始:{}", JSONUtil.toJsonStr(approvalResubmitParam));
        ResultTemplate<String> stringResultTemplate = feignApprovalRpc.resubmitApproval(approvalResubmitParam);
        log.info("再次发起异常合同结束:{}", JSONUtil.toJsonStr(stringResultTemplate));
        if (!stringResultTemplate.getSuccess() || Objects.isNull(stringResultTemplate.getData())) {
            throw new BusinessException("再次发起异常失败");
        }
        //发送飞书消息
        approvalMessageService.approveSendMessage(id, processCode);
    }

    /**
     * 设置异常合同飞书审批模版数据
     */
    private String assembleFormStr(String applyCode, Integer cityId) {

        String regionName = regionService.queryNameByCity(cityId);
        if (Strings.isBlank(regionName)) {
            throw new BusinessException("设置异常合同飞书审批模版数据区域不存在");
        }
        String region;
        switch (RegionEnum.getByDesc(regionName)) {
            case CENTER_WEST:
                region = apiConfig.abContractCentreWest;
                break;
            case SOUTH:
                region = apiConfig.abContractSouth;
                break;
            case NORTH:
                region = apiConfig.abContractNorth;
                break;
            case EAST_ONE:
                region = apiConfig.abContractEastOne;
                break;
            case EAST_TWO:
                region = apiConfig.abContractEastTwo;
                break;
            default:
                throw new BusinessException("设置异常合同飞书审批模版nacos数据区域不存在");
        }
        //组装飞书参数
        List<ContractTemplateCommonParam> list = CollectionUtil.newArrayList();
        ContractTemplateCommonParam commonCodeParam = new ContractTemplateCommonParam();
        commonCodeParam.setId("code");
        commonCodeParam.setType("input");
        commonCodeParam.setValue(applyCode);
        ContractTemplateCommonParam commonRegionParam = new ContractTemplateCommonParam();
        commonRegionParam.setId("region");
        commonRegionParam.setType("radioV2");
        commonRegionParam.setValue(region);
        list.add(commonCodeParam);
        list.add(commonRegionParam);

        return JSONUtil.toJsonStr(list);
    }

    /*
     * <AUTHOR>
     * @Description 再次发起异常合同（退回、驳回）
     * @Date 2025/3/25
     * @Param [id]
     * @return java.lang.Boolean
     **/
    private Boolean resubmitApproval(Integer id, AbContractResubmitDTO contractResubmitDTO, ContractAbnormalDealAddParam param) {

        ExamineApproveEntity examineApproveEntity = examineApproveService.lambdaQuery()
                .eq(ExamineApproveEntity::getBizId, id)
                .orderByDesc(ExamineApproveEntity::getCreateTime)
                .last("LIMIT 1")
                .one();
        log.info("再次发起合同时异常合同查询审批实例:{}", JSONUtil.toJsonStr(examineApproveEntity));
        if (Objects.isNull(examineApproveEntity)) {
            throw new BusinessException("再次发起合同时异常合同查询审批实例失败，无法再次发起");
        }
        //修改异常合同状态
        updateAbContract(id);
        log.info("再次发起异常合同修改异常合同状态成功");
        //保存处理方式表
        saveAbContractDeal(id, param);
        log.info("再次发起异常合同保存处理方式表成功");
        //保存操作日志
        saveOperateLog(id, AbContractDealTypeEnum.ABNORMAL_CONTRACT_ADD, AbContractOperateEnum.CREATE_CONTRACT, null);
        log.info("再次发起异常合同保存操作日志成功");
        //保存状态变更日志
        saveStatusLog(id);
        log.info("再次发起异常合同保存状态变更日志成功");
        //飞书发起重新提交异常合同
        applyResubmitApproval(id, examineApproveEntity.getProcessCode(), contractResubmitDTO);
        log.info("再次发起异常合同飞书发起重新提交异常合同成功");
        //再次发起审批处理原合同异常状态
        updateContractStatus(id);

        return Boolean.TRUE;
    }

    /**
     * 填写处理结果处理原合同异常状态
     */
    private void updateWriteContractStatus(Integer id) {

        Integer contractId = contractService.queryApprovalDetail(id);
        //查询当前审批流媒资负责人处理意见
        ContractAbnormalDealEntity abnormalDealEntity = contractAbnormalDealService.lambdaQuery()
                .eq(ContractAbnormalDealEntity::getAbnormalId, id)
                .eq(ContractAbnormalDealEntity::getType, AbContractDealTypeEnum.MEDIA.getCode())
                .orderByDesc(ContractAbnormalDealEntity::getVersion)
                .last("LIMIT 1")
                .one();
        log.info("填写处理结果处理原合同异常状态查询异常处理意见:{}", JSONUtil.toJsonStr(abnormalDealEntity));
        Boolean updated = Boolean.FALSE;
        if (Objects.equals(abnormalDealEntity.getDealType(), DealTypeEnum.BUSINESS.getCode())) {
            updated = contractService.lambdaUpdate()
                    .set(ContractEntity::getAbnormalFlag, ContractAbnormalFlagEnum.NONE.getCode())
                    .eq(ContractEntity::getId, contractId)
                    .update();
        } else if (Objects.equals(abnormalDealEntity.getDealType(), DealTypeEnum.LEGAL.getCode())
                || Objects.equals(abnormalDealEntity.getDealType(), DealTypeEnum.STOP.getCode())) {
            updated = contractService.lambdaUpdate()
                    .set(ContractEntity::getAbnormalFlag, ContractAbnormalFlagEnum.ABNORMAL.getCode())
                    .eq(ContractEntity::getId, contractId)
                    .update();
        }
        if (!updated) {
            throw new BusinessException("填写处理结果处理原合同异常状态失败");
        }
    }

    /*
     * <AUTHOR>
     * @Description 财务更新异常合同项目数据
     * @Date 2025/3/26
     * @Param [abContractApprovalParam]
     * @return void
     **/
    private void updateTreasurerProject(Integer id, AbContractApprovalParam abContractApprovalParam) {

        log.info("财务更新异常合同项目数据id:{}, param:{}", id, JSONUtil.toJsonStr(abContractApprovalParam));
        if (!Objects.equals(AbContractDealTypeEnum.TREASURER.getCode(), abContractApprovalParam.getType()) ||
                CollectionUtil.isEmpty(abContractApprovalParam.getApprovalMoneyParams())) {
            return;
        }
        List<ContractAbnormalProjectEntity> contractAbnormalProjectEntities = abContractApprovalParam.getApprovalMoneyParams()
                .stream().map(ContractAbnormalProjectConvert.INSTANCE::toEntity).toList();
        boolean update = contractAbnormalProjectService.updateProject(id, contractAbnormalProjectEntities);
        if (!update) {
            throw new BusinessException("财务更新异常合同项目数据失败");
        }
    }

    /*
     * <AUTHOR>
     * @Description 法务更新异常合同数据
     * @Date 2025/4/2
     * @Param [id, abContractApprovalParam]
     * @return void
     **/
    private void updateLegalAbContract(Integer id, AbContractApprovalParam abContractApprovalParam) {

        log.info("法务更新异常合同数据id:{}, param:{}", id, JSONUtil.toJsonStr(abContractApprovalParam));
        if (!Objects.equals(AbContractDealTypeEnum.LEGAL.getCode(), abContractApprovalParam.getType())) {
            return;
        }
        if (Objects.equals(AbnormalOpinionEnum.CONFIRM.getCode(), abContractApprovalParam.getApprovalOpinion())) {
            //法务更新异常合同主表
            boolean updateAbContract = contractAbnormalService.lambdaUpdate()
                    .set(ContractAbnormalEntity::getAbnormalReason, abContractApprovalParam.getAbnormalReason())
                    .eq(ContractAbnormalEntity::getId, id)
                    .eq(ContractAbnormalEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                    .update();
            if (!updateAbContract) {
                throw new BusinessException("法务更新异常合同数据失败");
            }
        } else {
            boolean updated = contractAbnormalProjectService.lambdaUpdate()
                    .set(ContractAbnormalProjectEntity::getPaidAmount, BigDecimal.ZERO)
                    .set(ContractAbnormalProjectEntity::getReturnInvoicedAmount, BigDecimal.ZERO)
                    .eq(ContractAbnormalProjectEntity::getAbnormalId, id)
                    .update();
            if (!updated) {
                throw new BusinessException("法务更新异常合同数据修改财务填写金额失败");
            }
        }
    }

    /*
     * <AUTHOR>
     * @Description 媒资负责人更新异常合同数据
     * @Date 2025/4/7
     * @Param [id, abContractApprovalParam]
     * @return void
     **/
    private void updateMediaAbContract(Integer id, AbContractApprovalParam abContractApprovalParam) {

        log.info("媒资负责人更新异常合同数据id:{}, param:{}", id, JSONUtil.toJsonStr(abContractApprovalParam));
        if (!Objects.equals(AbContractDealTypeEnum.MEDIA.getCode(), abContractApprovalParam.getType())) {
            return;
        }
        //媒资负责人更新异常合同数据
        if (Objects.equals(AbnormalOpinionEnum.CONFIRM.getCode(), abContractApprovalParam.getApprovalOpinion())) {
            Integer handleId = getDealUser(id);
            log.info("媒资负责人确认更新异常合同数据处理人:{}", handleId);
            //确认需要修改主表的dealType字段
            boolean updateAbContract = contractAbnormalService.lambdaUpdate()
                    .set(ContractAbnormalEntity::getHandler, handleId)
                    .set(ContractAbnormalEntity::getDealType, abContractApprovalParam.getDealType())
                    .eq(ContractAbnormalEntity::getId, id)
                    .eq(ContractAbnormalEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                    .update();
            if (!updateAbContract) {
                throw new BusinessException("媒资负责人确认更新异常合同数据失败");
            }
        } else {
            //驳回、退回需要清空主表法务填写的异常原因
            boolean updateAbContract = contractAbnormalService.lambdaUpdate()
                    .set(ContractAbnormalEntity::getAbnormalRemark, Strings.EMPTY)
                    .eq(ContractAbnormalEntity::getId, id)
                    .eq(ContractAbnormalEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                    .update();
            if (!updateAbContract) {
                throw new BusinessException("媒资负责人驳回、退回更新异常合同数据失败");
            }
        }
    }

    /*
     * <AUTHOR>
     * @Description 发起飞书审批
     * @Date 2025/3/26
     * @Param [id, abContractApprovalParam]
     * @return void
     **/
    private void approve(Integer id, AbContractApprovalParam abContractApprovalParam) {

        log.info("发起异常合同审批id:{}:param:{}", id, JSONUtil.toJsonStr(abContractApprovalParam));
        //查询飞书审批process_code
        ExamineApproveEntity examineApproveEntity = examineApproveService.lambdaQuery()
                .select(ExamineApproveEntity::getProcessCode)
                .eq(ExamineApproveEntity::getBizId, id)
                .orderByDesc(ExamineApproveEntity::getCreateTime)
                .last("LIMIT 1")
                .one();
        log.info("查询飞书审批process_code:{}", JSONUtil.toJsonStr(examineApproveEntity));
        if (Objects.isNull(examineApproveEntity)) {
            throw new BusinessException("异常合同发起飞书审批查询code失败");
        }
        ResultTemplate<String> stringResultTemplate = new ResultTemplate<>();
        if (Objects.equals(AbnormalOpinionEnum.CONFIRM.getCode(), abContractApprovalParam.getApprovalOpinion())) {
            ApprovalTaskOperateParam approvalTaskOperateParam = new ApprovalTaskOperateParam();
            approvalTaskOperateParam.setApproveType(ABNORMAL_CONTRACT_KEY);
            approvalTaskOperateParam.setProcessCode(examineApproveEntity.getProcessCode());
            log.info("发起确认异常合同审批:{}", JSONUtil.toJsonStr(approvalTaskOperateParam));
            stringResultTemplate = feignApprovalRpc.agreeApproval(approvalTaskOperateParam);
            //发送飞书消息
            approvalMessageService.approveSendMessage(id, examineApproveEntity.getProcessCode());
        } else if (Objects.equals(AbnormalOpinionEnum.ROLLBACK.getCode(), abContractApprovalParam.getApprovalOpinion())) {
            //获取可回退节点
            ResultTemplate<List<ApprovalRollbackVO>> rollbackRoot = feignApprovalRpc.getRollbackRoot(examineApproveEntity.getProcessCode(), ABNORMAL_CONTRACT_KEY);
            log.info("审批异常合同获取可回退节点:{}", JSONUtil.toJsonStr(rollbackRoot));
            if (!rollbackRoot.getSuccess() || CollectionUtil.isEmpty(rollbackRoot.getData())) {
                throw new BusinessException("审批异常合同退回失败");
            }
            List<RollbackNodeParam> rollbackNodes = rollbackRoot.getData().stream().map(e -> {
                RollbackNodeParam rollbackNodeParam = new RollbackNodeParam();
                rollbackNodeParam.setNodeKey(e.getNodeKey());
                rollbackNodeParam.setCreateTime(e.getCreateTime());
                return rollbackNodeParam;
            }).toList();
            ApprovalTaskRollbackParam approvalTaskOperateParam = new ApprovalTaskRollbackParam();
            approvalTaskOperateParam.setApproveType(ABNORMAL_CONTRACT_KEY);
            approvalTaskOperateParam.setProcessCode(examineApproveEntity.getProcessCode());
            approvalTaskOperateParam.setRollbackNodes(rollbackNodes);
            log.info("发起异常合同回退审批:{}", JSONUtil.toJsonStr(approvalTaskOperateParam));
            stringResultTemplate = feignApprovalRpc.rollbackApproval(approvalTaskOperateParam);
        } else if (Objects.equals(AbnormalOpinionEnum.REJECT.getCode(), abContractApprovalParam.getApprovalOpinion())) {
            ApprovalTaskOperateParam approvalTaskOperateParam = new ApprovalTaskOperateParam();
            approvalTaskOperateParam.setApproveType(ABNORMAL_CONTRACT_KEY);
            approvalTaskOperateParam.setProcessCode(examineApproveEntity.getProcessCode());
            log.info("发起驳回异常合同审批:{}", JSONUtil.toJsonStr(approvalTaskOperateParam));
            stringResultTemplate = feignApprovalRpc.rejectApproval(approvalTaskOperateParam);
        }
        log.info("发起异常合同飞书审批结果:{}", JSONUtil.toJsonStr(stringResultTemplate));
        if (!stringResultTemplate.getSuccess() || Objects.isNull(stringResultTemplate.getData())) {
            throw new BusinessException("审批异常合同失败");
        }
    }

    /**
     * 生成此合同下的申请次数
     *
     * @param id 异常合同id
     * @return
     */
    private int generateApprovalVersion(Integer id) {
        // 查询此合同最大的已申请次数
        ContractAbnormalDealEntity entity = contractAbnormalDealService.lambdaQuery()
                .eq(ContractAbnormalDealEntity::getAbnormalId, id)
                .eq(ContractAbnormalDealEntity::getType, AbContractDealTypeEnum.ABNORMAL_CONTRACT_ADD.getCode())
                .eq(ContractAbnormalDealEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .orderByDesc(ContractAbnormalDealEntity::getVersion)
                .last("limit 1")
                .one();
        if (Objects.isNull(entity)) {
            return CONSTANT_ZERO;
        }
        return entity.getVersion();
    }

    private void updateOperateLog(Integer id, AbContractApprovalParam abContractApprovalParam) {

        OperateLogParam operateLogParam = new OperateLogParam();
        operateLogParam.setType(BusinessBelongEnum.ABNORMAL_CONTRACT.getCode());
        operateLogParam.setSubType(abContractApprovalParam.getType());
        operateLogParam.setBizId(id);
        operateLogParam.setOperateTime(LocalDateTime.now());
        operateLogParam.setContent(abContractApprovalParam.getRemark());
        switch (AbnormalOpinionEnum.parse(abContractApprovalParam.getApprovalOpinion())) {
            case CONFIRM:
                operateLogParam.setOperateType(AbContractOperateEnum.CONFIRM.getCode());
                break;
            case ROLLBACK:
                operateLogParam.setOperateType(AbContractOperateEnum.ROLLBACK.getCode());
                break;
            case REJECT:
                operateLogParam.setOperateType(AbContractOperateEnum.REJECT.getCode());
                break;
            default:
                throw new BusinessException("审批意见错误");
        }
        //根据审批类型赋值content
        if (Objects.equals(AbContractDealTypeEnum.REGION.getCode(), abContractApprovalParam.getType())
                || Objects.equals(AbContractDealTypeEnum.MEDIA.getCode(), abContractApprovalParam.getType())) {
            ContractAbnormalDealEntity contractAbnormalDealEntity = new ContractAbnormalDealEntity();
            contractAbnormalDealEntity.setDealType(abContractApprovalParam.getDealType());
            contractAbnormalDealEntity.setDealDate(abContractApprovalParam.getDealDate());
            contractAbnormalDealEntity.setRemark(abContractApprovalParam.getRemark());
            operateLogParam.setContent(JSONUtil.toJsonStr(contractAbnormalDealEntity));
        } else if (Objects.equals(AbContractDealTypeEnum.LEGAL.getCode(), abContractApprovalParam.getType())) {
            ContractAbnormalDealEntity contractAbnormalDealEntity = new ContractAbnormalDealEntity();
            contractAbnormalDealEntity.setAbnormalReason(abContractApprovalParam.getAbnormalReason());
            contractAbnormalDealEntity.setRemark(abContractApprovalParam.getRemark());
            operateLogParam.setContent(JSONUtil.toJsonStr(contractAbnormalDealEntity));
        }

        logService.saveLogAsync(operateLogParam);
    }

    /**
     * 获取处理人
     *
     * @param abnormalId
     * @return
     */
    private Integer getDealUser(Integer abnormalId) {
        ContractAbnormalDealUserDTO contractAbnormalDealUserDTO = contractAbnormalService.getAbnormalDealUser(abnormalId);
        ContractAbnormalDealEntity contractAbnormalDealEntity = contractAbnormalDealService.lambdaQuery()
                .select(ContractAbnormalDealEntity::getDealType)
                .eq(ContractAbnormalDealEntity::getAbnormalId, abnormalId)
                .eq(ContractAbnormalDealEntity::getType, AbContractDealTypeEnum.MEDIA.getCode())
                .eq(ContractAbnormalDealEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .orderByDesc(ContractAbnormalDealEntity::getVersion)
                .last("limit 1")
                .one();
        log.info("获取处理人id:{}, dto:{}, entity:{}", abnormalId, JSONUtil.toJsonStr(contractAbnormalDealUserDTO), JSONUtil.toJsonStr(contractAbnormalDealEntity));
        if (Objects.isNull(contractAbnormalDealUserDTO) || Objects.isNull(contractAbnormalDealEntity)) {
            return null;
        }
        String dealType = contractAbnormalDealEntity.getDealType();
        if (DealTypeEnum.BUSINESS.getCode().equals(dealType) || DealTypeEnum.STOP.getCode().equals(dealType)) {
            //业务负责人
            return contractAbnormalDealUserDTO.getRegionBusinessHead();
        } else if (DealTypeEnum.LEGAL.getCode().equals(dealType)) {
            //法务负责人
            return contractAbnormalDealUserDTO.getRegionLegalBp();
        }
        return null;
    }
}
