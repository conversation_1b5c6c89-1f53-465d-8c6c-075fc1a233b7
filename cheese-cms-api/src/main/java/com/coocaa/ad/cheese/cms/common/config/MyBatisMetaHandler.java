package com.coocaa.ad.cheese.cms.common.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-12
 */
@Slf4j
@Component
public class MyBatisMetaHandler implements MetaObjectHandler {
    private final static String FIELD_CREATOR = "creator";
    private final static String FIELD_CREATE_TIME = "createTime";
    private final static String FIELD_OPERATOR = "operator";
    private final static String FIELD_UPDATE_TIME = "updateTime";

    @Override
    public void insertFill(MetaObject metaObject) {
        LocalDateTime now = LocalDateTime.now();
        Integer userId = UserThreadLocal.getUserId();

        if (Objects.isNull(getFieldValByName(FIELD_CREATOR, metaObject))) {
            this.strictInsertFill(metaObject, FIELD_CREATOR, Integer.class, userId);
        }

        if (Objects.isNull(getFieldValByName(FIELD_CREATE_TIME, metaObject))) {
            this.strictInsertFill(metaObject, FIELD_CREATE_TIME, LocalDateTime.class, now);
        }

        if (Objects.isNull(getFieldValByName(FIELD_OPERATOR, metaObject))) {
            this.strictInsertFill(metaObject, FIELD_OPERATOR, Integer.class, userId);
        }

        if (Objects.isNull(getFieldValByName(FIELD_UPDATE_TIME, metaObject))) {
            this.strictInsertFill(metaObject, FIELD_UPDATE_TIME, LocalDateTime.class, now);
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        if (Objects.isNull(getFieldValByName(FIELD_OPERATOR, metaObject))) {
            this.strictUpdateFill(metaObject, FIELD_OPERATOR, Integer.class, UserThreadLocal.getUserId());
        }

        if (Objects.isNull(getFieldValByName(FIELD_UPDATE_TIME, metaObject))) {
            this.strictUpdateFill(metaObject, FIELD_UPDATE_TIME, LocalDateTime.class, LocalDateTime.now());
        }
    }
}
