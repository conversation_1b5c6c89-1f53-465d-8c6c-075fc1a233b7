package com.coocaa.ad.cheese.cms.venue.controller;

import cn.hutool.core.date.DatePattern;
import com.alibaba.fastjson2.JSON;
import com.coocaa.ad.cheese.cms.common.bean.kanban.KanbanVO;
import com.coocaa.ad.cheese.cms.common.tools.common.constant.Constants;
import com.coocaa.ad.cheese.cms.common.tools.common.result.PageRequestVo;
import com.coocaa.ad.cheese.cms.common.tools.common.result.PageResponseVo;
import com.coocaa.ad.cheese.cms.common.tools.common.result.ResultTemplate;
import com.coocaa.ad.cheese.cms.download.enums.DownLoadTypeEnum;
import com.coocaa.ad.cheese.cms.download.service.CollaborationDataService;
import com.coocaa.ad.cheese.cms.download.service.RealTimeDataService;
import com.coocaa.ad.cheese.cms.venue.service.KanbanService;
import com.coocaa.ad.cheese.cms.venue.vo.kanban.KanbanDeviceStatisticsVO;
import com.coocaa.ad.cheese.cms.venue.vo.kanban.StatisticsDeviceImportRecordVO;
import com.coocaa.ad.cheese.cms.venue.vo.kanban.ContractPointStatisticsVO;
import com.coocaa.ad.downloader.bean.dto.TaskDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import jakarta.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 看板数据展示
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-26
 */
@Slf4j
@RestController
@RequestMapping("/venue/kanban")
@Tag(name = "看板数据", description = "看板数据")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class KanbanController extends BaseController {
    private final KanbanService kanbanService;
    private final RealTimeDataService realTimeDataService;
    private final CollaborationDataService collaborationDataService;

    @Value("${device-import.excel.template:https://logs-iot.coocaa.com/cms/venue/2025/01/15/设备数据导入模板.xlsx}")
    private String deviceImportExcelTemplate;

    @Operation(summary = "根据城市统计合同状态")
    @PostMapping("/contract")
    public ResultTemplate<KanbanVO> getContractStatusInfo(@RequestParam(name = "cityId", required = false, defaultValue = "0") List<Integer> cityIds) {
        KanbanVO result = kanbanService.getContractStatusInfo(cityIds);
        return ResultTemplate.success(result);
    }

    @Operation(summary = "根据城市统计合同状态（PC）")
    @PostMapping("/contract/pc")
    public ResultTemplate<List<KanbanVO>> listForContractPC() {
        return ResultTemplate.success(kanbanService.getListForContractPC());
    }

    @Operation(summary = "执行统计点位状态")
    @GetMapping("/execute/statistics-point")
    public ResultTemplate<?> statisticsPointData(@RequestParam(name = "cityId", required = false, defaultValue = "0") List<Integer> cityIds) {
        kanbanService.statisticsPointData(null);
        return ResultTemplate.success();
    }


    @Operation(summary = "获取统计点位状态")
    @GetMapping("/info/statistics-point")
    public ResultTemplate<List<ContractPointStatisticsVO>> getStatisticsPointData(
            @RequestParam(name = "date", required = true)
            @DateTimeFormat(pattern = Constants.DATE_FORMAT) LocalDate date,
            @RequestParam(value = "days", required = true) Integer days,
            @RequestParam(value = "cityId", required = true) Integer cityId

    ) {
        return ResultTemplate.success(kanbanService.getStatisticsPointDataByCityId(date, days, cityId));
    }

    @Operation(summary = "根据城市统计合作点位数据（PC）")
    @GetMapping("/info/statistics-point/pc")
    public ResultTemplate<List<KanbanVO>> getStatisticsPointDataPC(
            @RequestParam(name = "date", required = false)
            @DateTimeFormat(pattern = Constants.DATE_FORMAT) Optional<LocalDate> date) {
        return ResultTemplate.success(kanbanService.getStatisticsPointDataPC(date.orElse(LocalDate.now())));
    }

    @Operation(summary = "合同状态数据导出")
    @PostMapping("/contract/export")
    public ResultTemplate<Long> exportForContract() {
        return ResultTemplate.success(this.realTimeDataService.downloader(null));
    }

    @Operation(summary = "合作点位数据导出")
    @PostMapping("/info/statistics-point/export")
    public ResultTemplate<Long> exportForStatisticsPointData(@RequestParam(name = "date", required = false)
                                                               @DateTimeFormat(pattern = Constants.DATE_FORMAT) Optional<LocalDate> date) {
        LocalDate taskDate =date.orElse(LocalDate.now());
        TaskDTO taskDTO = new TaskDTO().setExecuteParams(taskDate.format(DateTimeFormatter.ofPattern(Constants.DATE_FORMAT)));
        return ResultTemplate.success(this.collaborationDataService.downloader(taskDTO));
    }

    @Operation(summary = "获取设备导入记录列表(分页)")
    @PostMapping("/device/imported/list")
    public ResultTemplate<PageResponseVo<StatisticsDeviceImportRecordVO>> getDeviceImportList(@RequestBody PageRequestVo<Void> pageRequest) {
        return ResultTemplate.success(kanbanService.getDeviceImportList(pageRequest));
    }

    @Operation(summary = "新增导入记录")
    @PostMapping("/device/imported")
    public ResultTemplate<String> createDeviceImportRecord(@RequestParam(name = "statisticsDate")
                                                           @DateTimeFormat(pattern = Constants.DATE_FORMAT) LocalDate statisticsDate,
                                                           @RequestParam(name = "file") MultipartFile file) {
        String result = kanbanService.deviceRecordImport(statisticsDate, file);
        if (StringUtils.isBlank(result)) {
            return ResultTemplate.success("", String.format("【%s】文件上传成功", file.getOriginalFilename()));
        } else {
            return ResultTemplate.success(result, String.format("【%s】文件上传失败", file.getOriginalFilename()));
        }
    }

    @Operation(summary = "删除导入记录")
    @DeleteMapping("/device/imported/{id}")
    public ResultTemplate<String> deleteDeviceImportRecord(@PathVariable(name = "id") Long id) {
        return kanbanService.deleteDeviceImportRecord(id)
                ? ResultTemplate.success("删除成功")
                : ResultTemplate.fail("删除失败");
    }

    @Operation(summary = "下载设备数据导入模板")
    @GetMapping("/device/imported/template")
    public ResultTemplate<String> downloadDeviceImportTemplate(HttpServletResponse response) {
        return ResultTemplate.success(deviceImportExcelTemplate);
    }

    @Operation(summary = "上传设备数据导入模板")
    @PostMapping("/device/imported/template")
    public ResultTemplate<String> uploadDeviceImportTemplate(@RequestParam(name = "file") MultipartFile file) {
        String result = kanbanService.uploadDeviceImportTemplate(file);
        if (StringUtils.isBlank(result)) {
            return ResultTemplate.success(String.format("【%s】文件上传成功", file.getOriginalFilename()));
        } else {
            return ResultTemplate.fail(result);
        }
    }

    /**
     * @Author：TanJie
     * @Date：2025-01-17 15:47
     * @Description：获取设备统计数据
     */
    @Operation(summary = "获取设备统计数据")
    @GetMapping("/device/statistics")
    public ResultTemplate<KanbanDeviceStatisticsVO> getDeviceStatisticsData(
            @RequestParam(name = "accessType", required = false, defaultValue = "") String accessType,
            @RequestParam(name = "cityId", required = false, defaultValue = "0") List<Integer> cityIds,
            @RequestParam(name = "filterDate", required = false) @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN) LocalDate filterDate) {
        return ResultTemplate.success(kanbanService.getDeviceStatisticsData(cityIds, filterDate, accessType));
    }
}
