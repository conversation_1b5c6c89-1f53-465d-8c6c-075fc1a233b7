package com.coocaa.ad.cheese.cms.venue.service;

import com.coocaa.ad.cheese.cms.venue.vo.contract.RegionSimpleListVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/3/24
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class NacosRegionService {
    private final RegionService regionService;


    /**
     * key -> cityName
     * value -> regionName
     * 根据城市名称获取城市大区信息
     *
     * @param cityName
     * @return
     */
    public String getRegionInfoByCityName(String cityName) {
        return regionService.getCityReginMapping().get(cityName);
    }

    /**
     * 根据城市名称批量获取城市大区信息
     *
     * @param cityNames
     * @return
     */
    public Map<String, String> getRegionInfoByCityNames(List<String> cityNames) {
        Map<String, String> mapping = regionService.getCityReginMapping();
        // 从mapping中筛选cityNames中存在的key 并返回map
        return mapping.entrySet().stream()
                .filter(entry -> cityNames.contains(entry.getKey()))
                .collect(java.util.stream.Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    /**
     * 获取所有大区信息
     *
     * @return
     */
    public List<String> getAllRegion() {
        return regionService.listAllRegions().stream()
                .map(RegionSimpleListVO::getName).distinct().toList();
    }

    /**
     * 根据大区信息获取下属城市
     *
     * @param regionNames 大区名称
     * @return 城市名称
     */
    public List<String> getCityNameByRegions(List<String> regionNames) {
        return regionService.getCityReginMapping().entrySet().stream()
                .filter(entry -> regionNames.contains(entry.getValue()))
                .map(Map.Entry::getKey)
                .toList();
    }
}
