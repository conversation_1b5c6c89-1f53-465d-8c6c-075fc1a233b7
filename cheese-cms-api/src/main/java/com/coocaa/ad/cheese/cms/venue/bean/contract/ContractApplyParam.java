package com.coocaa.ad.cheese.cms.venue.bean.contract;

import com.alibaba.fastjson2.annotation.JSONField;
import com.coocaa.ad.cheese.cms.common.config.annotation.ProjectsLevelNotBlank;
import com.coocaa.ad.cheese.cms.common.serializer.EncryptDeserializer;
import com.coocaa.ad.cheese.cms.common.tools.venue.constant.VenueConstants;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.BusinessTypeEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.CooperateTypeEnum;
import com.coocaa.ad.cheese.cms.common.validation.EnumType;
import com.coocaa.ad.cheese.cms.common.validation.ValidationGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.groups.Default;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 合同申请参数
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-04
 */
@Data
@ProjectsLevelNotBlank(groups = {Default.class, ValidationGroup.ContractAgreement.class, ValidationGroup.ContractAmendment.class})
public class ContractApplyParam {
    @Schema(description = "合同ID", type = "Int", example = "1")
    private Integer id;

    @NotNull(message = "合作模式不能为空", groups = {Default.class, ValidationGroup.ContractAgreement.class, ValidationGroup.ContractAmendment.class})
    @EnumType(message = "合作模式不正确", value = CooperateTypeEnum.class)
    @Schema(description = "合作模式 [1:租赁]", type = "Int", example = "1")
    private Integer cooperateType;

    @NotNull(message = "业务类型不能为空", groups = {Default.class, ValidationGroup.ContractAgreement.class, ValidationGroup.ContractAmendment.class})
    @EnumType(message = "业务类型不正确", value = BusinessTypeEnum.class)
    @Schema(description = "业务类型 [1:自营, 2:代理]", type = "Int", example = "1")
    private Integer businessType;

    @NotNull(message = "合同开始日期不能为空", groups = {Default.class, ValidationGroup.ContractAgreement.class, ValidationGroup.ContractAmendment.class})
    @JsonFormat(pattern = VenueConstants.DATE_FORMAT)
    @Schema(description = "开始日期", type = "String", example = "2024-01-01")
    private LocalDate startDate;

    @NotNull(message = "合同结束日期不能为空", groups = {Default.class, ValidationGroup.ContractAgreement.class, ValidationGroup.ContractAmendment.class})
    @JsonFormat(pattern = VenueConstants.DATE_FORMAT)
    @Schema(description = "结束日期", type = "String", example = "2024-12-31")
    private LocalDate endDate;

    @NotNull(message = "合同年限不能为空", groups = {Default.class, ValidationGroup.ContractAgreement.class, ValidationGroup.ContractAmendment.class})
    @DecimalMin(value = "0.01", message = "合同年限不能小于0")
    @Schema(description = "合同年限（年）", type = "BigDecimal", example = "1.0")
    private BigDecimal period;

    @NotNull(message = "合同总金额不能为空", groups = {Default.class, ValidationGroup.ContractAgreement.class, ValidationGroup.ContractAmendment.class})
    @DecimalMin(value = "0.01", message = "合同总金额不能小于0")
    @Schema(description = "合同总金额（元）", type = "BigDecimal", example = "10000.00")
    private BigDecimal totalAmount;

    @Schema(description = "我方签约主体ID", type = "Int", example = "1", hidden = true)
    private Integer signPartyId;

    @Schema(description = "我方签约主体名称", type = "String", example = "创维集团", hidden = true)
    private String signPartyName;

    @Schema(description = "代理商ID (业务类型为代理时必填)", type = "Int", example = "1")
    @NotNull(message = "公司id不能为空", groups = {Default.class})
    private Integer agentId;

    @Schema(description = "代理商名称（业务类型为代理时必填）", type = "String", example = "深圳市XX广告有限公司")
    @NotNull(message = "公司不能为空", groups = {Default.class})
    private String agentName;

    @NotNull(message = "发票类型不能为空", groups = {Default.class, ValidationGroup.ContractAgreement.class, ValidationGroup.ContractAmendment.class})
    @Schema(description = "发票类型(字典0044)", type = "String", example = "0040-1")
    private String invoiceType;

    @NotNull(message = "税点不能为空", groups = {ValidationGroup.ContractAmendment.class})
    @Schema(description = "税点(字典0045)", type = "String", example = "0040-1")
    private String taxPoint;

    // @NotBlank(message = "联系人不能为空")
    // @Size(max = 50, message = "联系人长度不能超过50个字符")
    @Schema(description = "联系人姓名", type = "String", example = "张三")
    private String contactPerson;

    @JSONField(deserializeUsing = EncryptDeserializer.class)
    @Schema(description = "联系电话", type = "String", example = "13800138000")
    private String contactPhone;

    @JSONField(deserializeUsing = EncryptDeserializer.class)
    @Schema(description = "联系邮箱", type = "String", example = "<EMAIL>")
    private String contactEmail;

    // @JSONField(deserializeUsing = EncryptDeserializer.class)
    @Schema(description = "联系地址", type = "String", example = "广东省深圳市南山区科技园")
    private String contactAddress;

    @Schema(description = "签约说明", type = "String", example = "特殊合同条款说明")
    private String description;

    @NotNull(message = "请选择是否为标准合同", groups = {Default.class, ValidationGroup.ContractAgreement.class, ValidationGroup.ContractAmendment.class})
    @Schema(description = "标准合同 [0:否, 1:是]", type = "Int", example = "1")
    private Integer normalFlag;

    @NotNull(message = "请选择是否为三方合同", groups = {Default.class, ValidationGroup.ContractAgreement.class, ValidationGroup.ContractAmendment.class})
    @Schema(description = "三方合同 [0:否, 1:是]", type = "Int", example = "0")
    private Integer thirdFlag;

    @NotNull(message = "请选择是否为赠播", groups = {Default.class, ValidationGroup.ContractAgreement.class, ValidationGroup.ContractAmendment.class})
    @Schema(description = "赠播标记 [0:否, 1:是]", type = "Int", example = "0")
    private Integer giftFlag;

    @NotNull(message = "楼宇评级不能为空", groups = {Default.class})
    @Schema(description = "楼宇评级", type = "String", example = "AAA")
    private String buildingLevel;

    @NotNull(message = "印章类型不能为空", groups = {Default.class, ValidationGroup.ContractAmendment.class})
    @Schema(description = "印章类型(字典0040)", type = "String", example = "0040-1")
    private String sealType;

    @Schema(description = "是否关联飞书合同 0否 1是 为1则需要提供关联的原始合同编号 oldContractCode", type = "Int", example = "0")
    private Integer oldFlag;

    @Schema(description = "关联的原始合同编号 oldFlag为1 则需提供", type = "String", example = "cssssss")
    private String oldContractCode;

    @NotNull(message = "是否意向金合同不能为空",groups = {Default.class})
    @Schema(description = "是否意向金合同 0否 1是", type = "Integer", example = "1")
    private Integer intentionalDepositFlag;

    @Schema(description = "意向金金额", type = "BigDecimal", example = "10000.00")
    // @NotNull(message = "意向金金额不能为空",groups = {Default.class})
    private BigDecimal intentionalDeposit;

    @Schema(description = "付款要求", type = "String", example = " ADVANCE_TICKET(先票),POST_TICKET(后票)")
    @NotNull(message = "付款要求不能为空",groups = {Default.class, ValidationGroup.ContractAmendment.class})
    private String claim;

    @Schema(description = "盖章顺序", type = "String", example = "WE_STAMP_FIRST(我方先盖),THEY_STAMP_FIRST(对方先盖)")
    @NotNull(message = "盖章顺序不能为空",groups = {Default.class})
    private String stampOrder;

    @Valid
    @Schema(description = "供应商列表")
    @NotEmpty(message = "供应商不能为空", groups = {Default.class, ValidationGroup.ContractAgreement.class, ValidationGroup.ContractAmendment.class})
    private List<ContractSupplierParam> suppliers;

    @Valid
    @Schema(description = "项目列表")
    @NotEmpty(message = "项目不能为空", groups = {Default.class, ValidationGroup.ContractAgreement.class, ValidationGroup.ContractAmendment.class})
    private List<ContractProjectParam> projects;

    @Valid
    @Schema(description = "合同附件列表")
    @NotEmpty(message = "附件不能为空", groups = {ValidationGroup.ContractAmendment.class})
    private List<ContractAttachmentParam> attachments;
}
