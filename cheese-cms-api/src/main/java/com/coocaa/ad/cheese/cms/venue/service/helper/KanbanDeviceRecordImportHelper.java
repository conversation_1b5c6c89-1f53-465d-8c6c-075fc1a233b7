package com.coocaa.ad.cheese.cms.venue.service.helper;

import cn.hutool.core.date.DatePattern;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.coocaa.ad.cheese.cms.common.db.venue.bean.StatisticsDeviceCityWithImportRecordDTO;
import com.coocaa.ad.cheese.cms.common.db.venue.bean.StatisticsDeviceProductionQuantityDTO;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.StatisticsDeviceCityDataQuantityEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.StatisticsDeviceImportRecordEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.StatisticsDeviceProductionQuantityEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IStatisticsDeviceCityDataQuantityService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IStatisticsDeviceImportRecordService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IStatisticsDeviceProductionQuantityService;
import com.coocaa.ad.cheese.cms.common.rpc.FeignAuthorityRpc;
import com.coocaa.ad.cheese.cms.common.tools.common.bean.common.CodeNameVO;
import com.coocaa.ad.cheese.cms.common.tools.common.cos.ObjectUtils;
import com.coocaa.ad.cheese.cms.common.tools.common.exception.CommonException;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.DataAccessTypeEnum;
import com.coocaa.ad.cheese.cms.venue.bean.kanban.StatisticsDeviceImportCityExcelParam;
import com.coocaa.ad.cheese.cms.venue.bean.kanban.StatisticsDeviceImportProdExcelParam;
import com.coocaa.ad.cheese.cms.venue.vo.kanban.BaseDataItemVO;
import com.coocaa.ad.cheese.cms.venue.vo.kanban.DataItemVO;
import com.coocaa.ad.cheese.cms.venue.vo.kanban.KanbanDeviceStatisticsVO;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @file KanbanDeviceRecordImportHelper
 * @date 2025/1/15 9:56
 * @description This is a java file.
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class KanbanDeviceRecordImportHelper {
    private final FeignAuthorityRpc feignAuthorityRpc;
    private final IStatisticsDeviceImportRecordService statisticsDeviceImportRecordService;
    private final IStatisticsDeviceCityDataQuantityService statisticsDeviceCityDataQuantityService;
    private final IStatisticsDeviceProductionQuantityService statisticsDeviceProductionQuantityService;

    private final static String INDEX_DEFAULT_PLACEHOLDER = "-";
    private static final Map<String, Map<String, String>> FILE_SHEET_INFO_MAP = Map.of(
            "city", Map.of("name", "sheet1", "index", "0"),
            "prod", Map.of("name", "sheet2", "index", "1")
    );

    /**
     * @Author：TanJie
     * @Date：2025-01-15 16:09
     * @Description：数据导入处理
     */
    public String deviceRecordImport(LocalDate statisticsDate, MultipartFile file) {
        // 非空判定
        if (file == null || file.isEmpty()) {
            throw new CommonException("上传文件不能为空");
        }

        // 文件格式简单校验：必须为excel
        if (!checkFileType(file)) {
            throw new CommonException("文件格式错误，请上传excel文件");
        }

        // 读取Excel数据 - 城市设备统计数据
        List<StatisticsDeviceImportCityExcelParam> cityDataList = new ArrayList<>();
        // 读取Excel数据 - 设备生产数据
        List<StatisticsDeviceImportProdExcelParam> prodDataList = new ArrayList<>();

        // 文件内容校验
        String checkResult = checkFileContent(file, cityDataList, prodDataList);
        if (StringUtils.isNotBlank(checkResult)) {
            return checkResult;
        }

        // 数据持久化
        try {
            saveData(statisticsDate, file, cityDataList, prodDataList);
        } catch (Exception e) {
            throw new CommonException("设备数据导入失败");
        }
        return "";
    }

    /**
     * @Author：TanJie
     * @Date：2025-01-15 10:05
     * @Description：文件格式简单校验：必须为excel
     */
    private boolean checkFileType(MultipartFile file) {
        // 判断文件的 MIME 类型
        String contentType = file.getContentType();
        if (contentType != null) {
            // 判断文件的 MIME 类型
            if (contentType.equals("application/vnd.ms-excel") || contentType.equals("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")) {
                return true;
            } else {
                return false;
            }
        }

        // 判断文件扩展名
        String fileName = file.getOriginalFilename();
        if (fileName != null && (fileName.endsWith(".xls") || fileName.endsWith(".xlsx"))) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * @Author：TanJie
     * @Date：2025-01-15 10:06
     * @Description：文件内容校验
     */
    private String checkFileContent(MultipartFile file, List<StatisticsDeviceImportCityExcelParam> cityDataList, List<StatisticsDeviceImportProdExcelParam> prodDataList) {
        // 读取Excel数据 - 城市设备统计数据
        cityDataList.addAll(readExcelDeviceImportCityInfo(file));
        // 读取Excel数据 - 设备生产数据
        prodDataList.addAll(readExcelDeviceImportProdInfo(file));

        // 有没有1000条的限制？

        // 数据校验
        boolean allCityPass = checkDeviceImportCityData(cityDataList);
        boolean allProdPass = checkDeviceImportProdData(prodDataList);
        if (!allCityPass || !allProdPass) {
            // 返回：判定异常记录的文件地址
            try {
                // 创建临时文件
                File tempFile = File.createTempFile(UUID.randomUUID().toString(), ".xlsx");
                tempFile.deleteOnExit();

                // 使用同一个ExcelWriter写入多个sheet
                try (ExcelWriter excelWriter = EasyExcel.write(tempFile).build()) {
                    // 写入 - 城市设备统计数据
                    excelWriter.write(cityDataList,
                            EasyExcel.writerSheet(Integer.valueOf(FILE_SHEET_INFO_MAP.get("city").get("index")), FILE_SHEET_INFO_MAP.get("city").get("name"))
                                    .head(StatisticsDeviceImportCityExcelParam.class).build());
                    // 写入 - 设备生产数据
                    excelWriter.write(prodDataList,
                            EasyExcel.writerSheet(Integer.valueOf(FILE_SHEET_INFO_MAP.get("prod").get("index")), FILE_SHEET_INFO_MAP.get("prod").get("name"))
                                    .head(StatisticsDeviceImportProdExcelParam.class).build());
                } catch (Exception e) {
                    log.error("设备导入数据写入Excel文件失败", e);
                    throw new CommonException("设备导入数据写入Excel文件失败: " + e.getMessage());
                }

                String fileName = String.format("%s_%s%s",
                        file.getOriginalFilename().substring(0, file.getOriginalFilename().lastIndexOf(".")),
                        LocalDateTime.now().format(DateTimeFormatter.ofPattern(DatePattern.PURE_DATETIME_PATTERN)), ".xlsx");

                return uploadToCloud(tempFile, fileName);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }

        return "";
    }

    /**
     * @Author：TanJie
     * @Date：2025-01-15 11:19
     * @Description：读取按城市统计的设备记录 TODO：抽取到一个工具类中
     */
    private List<StatisticsDeviceImportCityExcelParam> readExcelDeviceImportCityInfo(MultipartFile file) {
        ArrayList<StatisticsDeviceImportCityExcelParam> cityDataList = new ArrayList<>();
        //
        try {
            InputStream fileInputStream = file.getInputStream();
            EasyExcel.read(fileInputStream, StatisticsDeviceImportCityExcelParam.class, new AnalysisEventListener<StatisticsDeviceImportCityExcelParam>() {
                private int rowIndex = 0;

                @Override
                public void invoke(StatisticsDeviceImportCityExcelParam data, AnalysisContext context) {
                    rowIndex++;
                    cityDataList.add(data);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                    log.info("设备城市导入数据读取完成，总行数：{}", rowIndex);
                }
            }).sheet(0).doRead();
        } catch (IOException e) {
            String tip = "读取Excel设备城市数据失败";
            log.error(tip, e);
            throw new RuntimeException(tip, e);
        }

        return cityDataList;
    }

    /**
     * @Author：TanJie
     * @Date：2025-01-15 11:07
     * @Description：设备城市导入数据校验
     */
    private boolean checkDeviceImportCityData(List<StatisticsDeviceImportCityExcelParam> cityDataList) {
        // sheet为空时的校验
        if (CollectionUtils.isEmpty(cityDataList)) {
            cityDataList.add(StatisticsDeviceImportCityExcelParam.builder()
                    .cityName(null)
                    .warehouseLogisticsCount(null)
                    .workOrderDispatchCount(null)
                    .siteSurveyCount(null)
                    .mountingPlateCount(null)
                    .installationAndLightingCount(null)
                    .checkStatus("不通过")
                    .remark("城市不能为空、仓储物流数不能为空、派工数不能为空、踏勘数不能为空、挂板数不能为空、安装点亮数不能为空")
                    .build());
            return false;
        }

        // 一次性获取所有启用的城市信息
        List<CodeNameVO> availableCities = feignAuthorityRpc.getAvailableCities().getData();
        if (CollectionUtils.isEmpty(availableCities)) {
            throw new CommonException("获取所有启用的城市信息为null，请核对权限系统的接口。");
        }
        Map<String, CodeNameVO> availableCitiesByNameMap = availableCities.stream().collect(Collectors.toMap(CodeNameVO::getName, e -> e));

        Set<String> existingCities = new HashSet<>(); // 记录已经出现的了城市
        boolean allPass = true;
        for (StatisticsDeviceImportCityExcelParam data : cityDataList) {
            List<String> remarkMsgList = new ArrayList<>(6);
            // 城市名称校验 - 城市信息错误
            if (!availableCitiesByNameMap.containsKey(data.getCityName())) {
                remarkMsgList.add("城市信息错误");
            }
            // 城市名称校验 - 城市名称重复
            if (existingCities.contains(data.getCityName())) {
                remarkMsgList.add("城市名称重复");
            }
            existingCities.add(data.getCityName());
            // 指标字段校验 - 「指标名称」为空、「指标名称」不为大于等于0的整数
            // 仓储物流数
            checkDeviceImportCityDataField(data.getWarehouseLogisticsCount(), "仓储物流数", remarkMsgList);
            // 派工数
            checkDeviceImportCityDataField(data.getWorkOrderDispatchCount(), "派工数", remarkMsgList);
            // 踏勘数
            checkDeviceImportCityDataField(data.getSiteSurveyCount(), "踏勘数", remarkMsgList);
            // 挂板数
            checkDeviceImportCityDataField(data.getMountingPlateCount(), "挂板数", remarkMsgList);
            // 安装点亮数
            checkDeviceImportCityDataField(data.getInstallationAndLightingCount(), "安装点亮数", remarkMsgList);

            // 补cityId字段
            CodeNameVO codeNameVO = availableCitiesByNameMap.get(data.getCityName());
            if (codeNameVO != null) {
                data.setCityId(codeNameVO.getId().longValue());
            }

            // 通过状态的判定
            if (remarkMsgList.isEmpty()) {
                data.setCheckStatus("通过");
            } else {
                if (allPass) allPass = false;
                data.setCheckStatus("不通过");
                data.setRemark(remarkMsgList.stream().collect(Collectors.joining("、")));
            }
        }

        return allPass;
    }

    /**
     * @Author：TanJie
     * @Date：2025-01-15 14:50
     * @Description：读取设备生产数据
     */
    private List<StatisticsDeviceImportProdExcelParam> readExcelDeviceImportProdInfo(MultipartFile file) {
        ArrayList<StatisticsDeviceImportProdExcelParam> prodDataList = new ArrayList<>();
        //
        try {
            InputStream fileInputStream = file.getInputStream();
            EasyExcel.read(fileInputStream, StatisticsDeviceImportProdExcelParam.class, new AnalysisEventListener<StatisticsDeviceImportProdExcelParam>() {
                private int rowIndex = 0;

                @Override
                public void invoke(StatisticsDeviceImportProdExcelParam data, AnalysisContext context) {
                    rowIndex++;
                    prodDataList.add(data);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                    log.info("设备生产数据读取完成，总行数：{}", rowIndex);
                }
            }).sheet(1).doRead();
        } catch (IOException e) {
            String tip = "读取Excel设备生产数据失败";
            log.error(tip, e);
            throw new RuntimeException(tip, e);
        }

        return prodDataList;
    }

    /**
     * @Author：TanJie
     * @Date：2025-01-15 14:49
     * @Description：设备生产数据校验
     */
    private boolean checkDeviceImportProdData(List<StatisticsDeviceImportProdExcelParam> prodDataList) {
        boolean allPass = true;
        // sheet为空时的校验
        if (CollectionUtils.isEmpty(prodDataList)) {
            prodDataList.add(StatisticsDeviceImportProdExcelParam.builder()
                    .orderCount(null)
                    .productionDeliveryCount(null)
                    .checkStatus("不通过")
                    .remark("下单数不能为空、生产交付数不能为空")
                    .build());
            return false;
        }
        //
        for (int i = 0; i < prodDataList.size(); i++) {
            List<String> remarkMsgList = new ArrayList<>(2);
            StatisticsDeviceImportProdExcelParam data = prodDataList.get(i);
            // 仅第一条数据有效
            if (i == 0) {
                // 下单数
                checkDeviceImportCityDataField(data.getOrderCount(), "下单数", remarkMsgList);
                // 生产交付数
                checkDeviceImportCityDataField(data.getProductionDeliveryCount(), "生产交付数", remarkMsgList);
            } else {
                remarkMsgList.add("数据无效，仅允许填写一条数据");
            }
            // 通过状态的判定
            if (remarkMsgList.isEmpty()) {
                data.setCheckStatus("通过");
            } else {
                if (allPass) allPass = false;
                data.setCheckStatus("不通过");
                data.setRemark(remarkMsgList.stream().collect(Collectors.joining("、")));
            }
        }

        return allPass;
    }

    /**
     * 复用 - 指标字段校验逻辑
     */
    private void checkDeviceImportCityDataField(String fieldValue, String fieldName, List<String> remarkMsgList) {
        if (StringUtils.isBlank(fieldValue)) {
            remarkMsgList.add(fieldName + "不能为空");
        } else {
            try {
                int num = Integer.parseInt(fieldValue);
                if (num < 0) {
                    remarkMsgList.add(fieldName + "不为大于等于0的整数");
                }
            } catch (NumberFormatException e) {
                remarkMsgList.add(fieldName + "不为大于等于0的整数");
            }
        }
    }

    /**
     * @Author：TanJie
     * @Date：2025-01-15 15:21
     * @Description：数据持久化（一个事务）
     */
    @Transactional(rollbackFor = Exception.class)
    public String saveData(LocalDate statisticsDate, MultipartFile file, List<StatisticsDeviceImportCityExcelParam> cityDataList, List<StatisticsDeviceImportProdExcelParam> prodDataList) {
        // 获取当前用户信息
        Long userId = UserThreadLocal.getUserId().longValue();

        // 覆盖当天历史导入的记录
        statisticsDeviceImportRecordService.update(new LambdaUpdateWrapper<StatisticsDeviceImportRecordEntity>()
                .eq(StatisticsDeviceImportRecordEntity::getStatisticsDate, statisticsDate)
                .set(StatisticsDeviceImportRecordEntity::getDeleted, 1));

        // 创建设备导入记录
        StatisticsDeviceImportRecordEntity importRecordEntity = StatisticsDeviceImportRecordEntity.builder()
                .statisticsDate(statisticsDate)
                .importTime(LocalDateTime.now())
                .fileUrl("")
                .creator(userId)
                .build();
        statisticsDeviceImportRecordService.save(importRecordEntity);

        // 批量创建城市设备记录
        if (!cityDataList.isEmpty()) {
            List<StatisticsDeviceCityDataQuantityEntity> cityDataQuantityEntityList = cityDataList.stream().map(data -> StatisticsDeviceCityDataQuantityEntity.builder()
                    .importNo(importRecordEntity.getId())
                    .cityId(data.getCityId())
                    .cityName(data.getCityName())
                    .warehouseLogisticsCount(Long.valueOf(data.getWarehouseLogisticsCount()))
                    .workOrderDispatchCount(Long.valueOf(data.getWorkOrderDispatchCount()))
                    .siteSurveyCount(Long.valueOf(data.getSiteSurveyCount()))
                    .mountingPlateCount(Long.valueOf(data.getMountingPlateCount()))
                    .installationAndLightingCount(Long.valueOf(data.getInstallationAndLightingCount()))
                    .build()
            ).toList();
            statisticsDeviceCityDataQuantityService.saveBatch(cityDataQuantityEntityList);
        }

        // 批量创建产品设备记录
        if (!prodDataList.isEmpty()) {
            List<StatisticsDeviceProductionQuantityEntity> prodDataQuantityEntityList = prodDataList.stream().map(data -> StatisticsDeviceProductionQuantityEntity.builder()
                    .importNo(importRecordEntity.getId())
                    .orderCount(Long.valueOf(data.getOrderCount()))
                    .productionDeliveryCount(Long.valueOf(data.getProductionDeliveryCount()))
                    .build()
            ).toList();
            statisticsDeviceProductionQuantityService.saveBatch(prodDataQuantityEntityList);
        }

        // 文件上传到云端
        try {
            // 创建临时文件
            File tempSourceFile = File.createTempFile(UUID.randomUUID().toString(), ".xlsx");
            tempSourceFile.deleteOnExit();
            file.transferTo(tempSourceFile);
            String fileName = String.format("设备数据_%s.xlsx", LocalDate.now().format(DateTimeFormatter.ofPattern(DatePattern.PURE_DATE_PATTERN)));
            String fileUrl = uploadToCloud(tempSourceFile, fileName);
            if (StringUtils.isBlank(fileUrl)) {
                throw new RuntimeException(String.format("【%s】文件上传失败", file.getOriginalFilename()));
            }
            log.debug("【{}】文件上传成功，文件地址：{}", file.getOriginalFilename(), fileUrl);
            // 更新文件地址信息
            statisticsDeviceImportRecordService.updateById(StatisticsDeviceImportRecordEntity.builder()
                    .id(importRecordEntity.getId())
                    .fileUrl(fileUrl)
                    .operator(userId)
                    .build());

            return fileUrl;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * @Author：TanJie
     * @Date：2025-01-15 16:55
     * @Description：上传文件到cos
     */
    private String uploadToCloud(File tempFile, String fileName) {
        String feature = "venue";
        ObjectUtils.uploadFile(ObjectUtils.getCosFileName(feature, fileName), tempFile);
        return ObjectUtils.getAccessUrl(feature, fileName);
    }

    /**
     * @Author：TanJie
     * @Date：2025-01-17 20:06
     * @Description：获取指标项的数据
     */
    public List<BaseDataItemVO<Long>> getDeviceItemDataList(StatisticsDeviceCityWithImportRecordDTO cityDataTotal,
                                                            StatisticsDeviceProductionQuantityDTO ProdDataTotal,
                                                            String accessType) {
        List<BaseDataItemVO<Long>> deviceDataList = new ArrayList<>(7);
        if (accessType.equals(DataAccessTypeEnum.ALL.getCode())) {
            deviceDataList.add(new BaseDataItemVO<>("下单数", ProdDataTotal != null ? ProdDataTotal.getOrderCount() : 0L));
            deviceDataList.add(new BaseDataItemVO<>("生产交付数", ProdDataTotal != null ? ProdDataTotal.getProductionDeliveryCount() : 0L));
        }
        deviceDataList.add(new BaseDataItemVO<>("仓储物流数", cityDataTotal.getWarehouseLogisticsCount()));
        deviceDataList.add(new BaseDataItemVO<>("派工数", cityDataTotal.getWorkOrderDispatchCount()));
        deviceDataList.add(new BaseDataItemVO<>("踏勘数", cityDataTotal.getSiteSurveyCount()));
        deviceDataList.add(new BaseDataItemVO<>(" 挂板数", cityDataTotal.getMountingPlateCount()));
        deviceDataList.add(new BaseDataItemVO<>("安装点亮数", cityDataTotal.getInstallationAndLightingCount()));

        return deviceDataList;
    }

    /**
     * @Author：TanJie
     * @Date：2025-01-17 20:11
     * @Description：获取指标漏斗的数据
     */
    public List<DataItemVO<Long>> getDeviceFunnelDataList(StatisticsDeviceCityWithImportRecordDTO cityDataTotal,
                                                          StatisticsDeviceProductionQuantityDTO ProdDataTotal,
                                                          String accessType, int initListSize) {
        List<DataItemVO<Long>> ratioData = Lists.newArrayListWithCapacity(initListSize);
        if (accessType.equals(DataAccessTypeEnum.ALL.getCode())) {
            if (ProdDataTotal != null) {
                ratioData.add(new DataItemVO("下单数", ProdDataTotal.getOrderCount(), null));
                ratioData.add(new DataItemVO("生产交付数", ProdDataTotal.getProductionDeliveryCount(),
                        getDeviceFunnelRatio(ProdDataTotal.getProductionDeliveryCount(), ProdDataTotal.getOrderCount())));
                ratioData.add(new DataItemVO("仓储物流数", cityDataTotal.getWarehouseLogisticsCount(),
                        getDeviceFunnelRatio(cityDataTotal.getWarehouseLogisticsCount(), ProdDataTotal.getProductionDeliveryCount())));
            } else {
                ratioData.add(new DataItemVO("下单数", ProdDataTotal.getOrderCount(), null));
                ratioData.add(new DataItemVO("生产交付数", ProdDataTotal.getProductionDeliveryCount(), BigDecimal.ZERO));
                ratioData.add(new DataItemVO("仓储物流数", cityDataTotal.getWarehouseLogisticsCount(), BigDecimal.ZERO));
            }
        } else {
            ratioData.add(new DataItemVO("仓储物流数", cityDataTotal.getWarehouseLogisticsCount(), null));
        }
        ratioData.add(new DataItemVO("派工数", cityDataTotal.getWorkOrderDispatchCount(),
                getDeviceFunnelRatio(cityDataTotal.getWorkOrderDispatchCount(), cityDataTotal.getWarehouseLogisticsCount())));
        ratioData.add(new DataItemVO("踏勘数", cityDataTotal.getSiteSurveyCount(),
                getDeviceFunnelRatio(cityDataTotal.getSiteSurveyCount(), cityDataTotal.getWorkOrderDispatchCount())));
        ratioData.add(new DataItemVO("挂板数", cityDataTotal.getMountingPlateCount(),
                getDeviceFunnelRatio(cityDataTotal.getMountingPlateCount(), cityDataTotal.getSiteSurveyCount())));
        ratioData.add(new DataItemVO("安装点亮数", cityDataTotal.getInstallationAndLightingCount(),
                getDeviceFunnelRatio(cityDataTotal.getInstallationAndLightingCount(), cityDataTotal.getMountingPlateCount())));

        return ratioData;
    }

    /**
     * @Author：TanJie
     * @Date：2025-01-17 20:23
     * @Description：计算转换率
     */
    private BigDecimal getDeviceFunnelRatio(Long numerator, Long denominator, int scale) {
        if (numerator == null || denominator == null) {
            log.warn("分母或分子为空，转换率强制设定为0！");
            return BigDecimal.ZERO;
        }
        if (denominator == 0) {
            log.warn("分母为0，转换率强制设定为0！");
            return BigDecimal.ZERO;
        } else {
            return new BigDecimal(numerator * 100).divide(new BigDecimal(denominator), scale, RoundingMode.HALF_UP);
        }
    }

    private BigDecimal getDeviceFunnelRatio(Long numerator, Long denominator) {
        return getDeviceFunnelRatio(numerator, denominator, 1);
    }

    /**
     * @Author：TanJie
     * @Date：2025-01-20 11:24
     * @Description：设备数据的默认返回对象
     */
    public KanbanDeviceStatisticsVO getDefaultKanbanDeviceStatisticsVO(LocalDate filterDate, String accessType) {
        KanbanDeviceStatisticsVO<String> vo = new KanbanDeviceStatisticsVO<>();
        vo.setLatestUpdateDate(filterDate);
        //
        ArrayList<BaseDataItemVO<String>> deviceDataList = Lists.newArrayListWithCapacity(7);
        ArrayList<DataItemVO<String>> ratioDataList = Lists.newArrayListWithCapacity(deviceDataList.size());
        if (accessType.equals(DataAccessTypeEnum.ALL.getCode())) {
            deviceDataList.add(new BaseDataItemVO<>("下单数", INDEX_DEFAULT_PLACEHOLDER));
            deviceDataList.add(new BaseDataItemVO<>("生产交付数", INDEX_DEFAULT_PLACEHOLDER));
            //
            ratioDataList.add(new DataItemVO("下单数", INDEX_DEFAULT_PLACEHOLDER, null));
            ratioDataList.add(new DataItemVO("生产交付数", INDEX_DEFAULT_PLACEHOLDER, null));
        }
        //
        deviceDataList.add(new BaseDataItemVO<>("仓储物流数", INDEX_DEFAULT_PLACEHOLDER));
        deviceDataList.add(new BaseDataItemVO<>("派工数", INDEX_DEFAULT_PLACEHOLDER));
        deviceDataList.add(new BaseDataItemVO<>("踏勘数", INDEX_DEFAULT_PLACEHOLDER));
        deviceDataList.add(new BaseDataItemVO<>("挂板数", INDEX_DEFAULT_PLACEHOLDER));
        deviceDataList.add(new BaseDataItemVO<>("安装点亮数", INDEX_DEFAULT_PLACEHOLDER));
        vo.setDeviceDataList(deviceDataList);
        //
        ratioDataList.add(new DataItemVO("仓储物流数", INDEX_DEFAULT_PLACEHOLDER, null));
        ratioDataList.add(new DataItemVO("派工数", INDEX_DEFAULT_PLACEHOLDER, null));
        ratioDataList.add(new DataItemVO("踏勘数", INDEX_DEFAULT_PLACEHOLDER, null));
        ratioDataList.add(new DataItemVO("挂板数", INDEX_DEFAULT_PLACEHOLDER, null));
        ratioDataList.add(new DataItemVO("安装点亮数", INDEX_DEFAULT_PLACEHOLDER, null));
        vo.setRatioData(ratioDataList);

        return vo;
    }
}
