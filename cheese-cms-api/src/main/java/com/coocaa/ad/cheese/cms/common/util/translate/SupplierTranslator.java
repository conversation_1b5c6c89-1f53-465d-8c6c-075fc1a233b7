package com.coocaa.ad.cheese.cms.common.util.translate;

import com.coocaa.ad.cheese.cms.common.db.venue.entity.SupplierEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.service.supplier.ISupplierService;
import com.coocaa.ad.translate.Translator;
import com.coocaa.ad.translate.util.TransUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 供应商翻译器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-30
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class SupplierTranslator implements Translator<Integer> {
    private final ISupplierService supplierService;

    @Override
    public String getTransType() {
        return VenueTransTypes.SUPPLIER;
    }

    @Override
    public Class<Integer> getDataType() {
        return Integer.class;
    }

    @Override
    public Map<Integer, String> getMapping(Collection<Integer> sourceValues) {
        return TransUtils.toNumValMap(sourceValues, ids -> supplierService.lambdaQuery()
                .select(SupplierEntity::getId, SupplierEntity::getSupplierName)
                .in(SupplierEntity::getId, ids)
                .list().stream()
                .collect(Collectors.toMap(SupplierEntity::getId, SupplierEntity::getSupplierName, (o, n) -> n)));
    }
}
