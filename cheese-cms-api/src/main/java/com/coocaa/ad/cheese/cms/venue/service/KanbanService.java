package com.coocaa.ad.cheese.cms.venue.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.ad.cheese.cms.common.db.venue.bean.StatisticsDeviceProductionQuantityDTO;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.StatisticsDeviceImportRecordEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IStatisticsDeviceCityDataQuantityService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IStatisticsDeviceImportRecordService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IStatisticsDeviceProductionQuantityService;
import com.coocaa.ad.cheese.cms.common.tools.common.result.PageRequestVo;
import com.coocaa.ad.cheese.cms.common.tools.common.result.PageResponseVo;
import com.coocaa.ad.cheese.cms.common.tools.venue.constant.VenueConstants;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractTypeEnum;
import com.coocaa.ad.cheese.cms.venue.service.helper.KanbanDeviceRecordImportHelper;
import com.coocaa.ad.cheese.cms.venue.vo.kanban.KanbanDeviceStatisticsVO;
import com.coocaa.ad.cheese.cms.common.db.venue.bean.StatisticsDeviceCityWithImportRecordDTO;
import com.coocaa.ad.cheese.cms.venue.vo.kanban.StatisticsDeviceImportRecordVO;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.coocaa.ad.cheese.cms.common.bean.kanban.KanbanVO;
import com.coocaa.ad.cheese.cms.common.bean.kanban.SectionVO;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractPointStatisticsEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractDeviceService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractPointStatisticsService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractService;
import com.coocaa.ad.cheese.cms.common.rpc.FeignAuthorityRpc;
import com.coocaa.ad.cheese.cms.common.tools.common.bean.common.CodeNameVO;
import com.coocaa.ad.cheese.cms.common.tools.common.cos.ObjectUtils;
import com.coocaa.ad.cheese.cms.common.tools.common.enums.BooleFlagEnum;
import com.coocaa.ad.cheese.cms.common.tools.common.result.ResultTemplate;
import com.coocaa.ad.cheese.cms.common.tools.common.util.StringUtils;
import com.coocaa.ad.cheese.cms.common.tools.common.util.TableNameUtil;
import com.coocaa.ad.cheese.cms.common.tools.venue.bean.UserDataAccessDTO;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractApplyStatusEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractStatusEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.DataAccessTypeEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.PointStatusEnum;
import com.coocaa.ad.cheese.cms.venue.convert.statistics.ContractPointStatisticsConvert;
import com.coocaa.ad.cheese.cms.venue.rpc.FeignSspRpc;
import com.coocaa.ad.cheese.cms.venue.vo.kanban.ContractPointStatisticsVO;
import com.coocaa.ad.cheese.cms.common.bean.kanban.IndexVO;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.KanbanContractIndexEnum;
import com.coocaa.ad.cheese.cms.venue.vo.kanban.SspPointStatusDto;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ThreadLocalRandom;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisStringCommands;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.types.Expiration;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.stream.Collectors;
import java.util.concurrent.TimeUnit;

import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class KanbanService {
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private final static String INDEX_DEFAULT_PLACEHOLDER = "-";
    private final static Integer RECENT_DAYS = 14;
    private final static String PERCENT_UNIT = "%";

    private final StringRedisTemplate stringRedisTemplate;
    private final IContractService contractService;
    private final IContractDeviceService contractDeviceService;
    private final FeignSspRpc feignSspRpc;
    private final FeignAuthorityRpc feignAuthorityRpc;
    private final IContractPointStatisticsService contractPointStatisticsService;
    private final DataAccessService dataAccessService;
    private final IStatisticsDeviceImportRecordService statisticsDeviceImportRecordService;
    private final KanbanDeviceRecordImportHelper kanbanDeviceRecordImportHelper;
    private final IStatisticsDeviceCityDataQuantityService statisticsDeviceCityDataQuantityService;
    private final IStatisticsDeviceProductionQuantityService statisticsDeviceProductionQuantityService;

    /**
     * 查询城市的数据（PC） - 支持多个城市数据的聚合
     */
    public List<KanbanVO> getListForContractPC() {
        // 获取该用户的cityIdList
        List<Integer> cityIdList = getUserKanbanCities(0);
        if (CollectionUtils.isEmpty(cityIdList)) {
            KanbanVO vo = generateKanbanVO(initCityMap(), INDEX_DEFAULT_PLACEHOLDER);
            return List.of(BeanUtil.toBean(vo, KanbanVO.class).setMainTitle("全部"));
        }
        //
        Map<Integer, KanbanVO> ExistCacheCityMap = new HashMap<>(cityIdList.size());
        KanbanVO kanbanAllVO = getContractStatusInfo(cityIdList, ExistCacheCityMap);
        if (kanbanAllVO == null) {
            KanbanVO vo = generateKanbanVO(initCityMap(), INDEX_DEFAULT_PLACEHOLDER);
            return List.of(BeanUtil.toBean(vo, KanbanVO.class).setMainTitle("全部"));
        }
        // 对象进行聚合
        List<KanbanVO> kanbanVOList = new ArrayList<>(ExistCacheCityMap.size() + 1);
        kanbanVOList.add(BeanUtil.toBean(kanbanAllVO, KanbanVO.class).setMainTitle("全部"));
        Map<Integer, String> cityNameMap = getCityNameMap(ExistCacheCityMap.keySet());
        ExistCacheCityMap.forEach((cityId, kanbanVO) -> {
            kanbanVOList.add(kanbanVO.setMainTitle(cityNameMap.get(cityId)));
        });

        return kanbanVOList;
    }

    /**
     * 查询城市的数据 - 支持多个城市数据的聚合
     */
    public KanbanVO getContractStatusInfo(List<Integer> cityIdList) {
        KanbanVO kanbanVO = getContractStatusInfo(cityIdList, null);
        return kanbanVO != null ? kanbanVO : generateKanbanVO(initCityMap(), INDEX_DEFAULT_PLACEHOLDER);
    }

    public KanbanVO getContractStatusInfo(List<Integer> cityIdList, Map<Integer, KanbanVO> ExistCacheCityMap) {
        if (CollectionUtils.isEmpty(cityIdList)) {
            return null;
        }
        if (ExistCacheCityMap == null) {
            ExistCacheCityMap = new HashMap<>(cityIdList.size());
        }
        // 1. 查询缓存 - 验证是否所有的城市数据都存在
        String cacheKeyFormat = "kanban:city:%d:contract_status:contract_quantity";
        List<String> cacheKeyList = cityIdList.stream().map(cityId -> String.format(cacheKeyFormat, cityId)).toList();
        List<String> resultList = stringRedisTemplate.opsForValue().multiGet(cacheKeyList);
        List<Integer> notExistCacheKeyList = new ArrayList<>(cityIdList.size());
        //
        for (int i = 0; i < cityIdList.size(); i++) {
            Integer cityId = cityIdList.get(i);
            if (CollectionUtils.isNotEmpty(resultList) && StringUtils.isNotBlank(resultList.get(i))) {
                try {
                    ExistCacheCityMap.put(cityId, OBJECT_MAPPER.readValue(resultList.get(i), KanbanVO.class));
                } catch (JsonProcessingException e) {
                    log.error("城市[{}]缓存数据转换失败！详情：{}", cityId, e.getMessage());
                    throw new RuntimeException(e);
                }
            } else {
                notExistCacheKeyList.add(cityId);
            }
        }

        // 单独处理单个城市的情况
        if (cityIdList.size() == 1 && ExistCacheCityMap.size() == 1) {
            try {
                // 使用缓存的数据
                return ExistCacheCityMap.get(cityIdList.get(0));
            } catch (Exception e) {
                log.error("从缓存中获取city[{}]的合同指标数据反序列化异常！详情为：{}", cityIdList.get(0), e.getMessage());
                return null;
            }
        }

        // 2. 对于不存在的城市数据，需要从数据库查询对应的数据，并更新缓存
        if (CollectionUtils.isNotEmpty(notExistCacheKeyList)) {
            Map<Integer, KanbanVO> kanbanVOByCityIdMap = getContractIndexes(notExistCacheKeyList);
            if (MapUtils.isNotEmpty(kanbanVOByCityIdMap)) {
                ExistCacheCityMap.putAll(kanbanVOByCityIdMap);
            }
        }

        // 3. 返回数据
        if (MapUtils.isEmpty(ExistCacheCityMap)) {
            try {
                if (cityIdList.size() == 1) stringRedisTemplate.opsForValue()
                        .set(String.format(cacheKeyFormat, cityIdList.get(0)), OBJECT_MAPPER.writeValueAsString(null), 60L, TimeUnit.SECONDS);
                return null;
            } catch (Exception e) {
                log.error("待写入缓存的city[{}]的合同指标数据序列化异常！详情为：{}", cityIdList.get(0), e.getMessage());
            }
        }
        // 更新城市数据缓存
        saveResultToCache(ExistCacheCityMap.entrySet().stream()
                .filter(entry -> notExistCacheKeyList.contains(entry.getKey()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)), cacheKeyFormat);

        // 单个城市数据直接返回
        if (ExistCacheCityMap.size() == 1) {
            return ExistCacheCityMap.entrySet().iterator().next().getValue();
        }
        // 多城市数据需要数据聚合
        return aggregateMultiCityData(ExistCacheCityMap);
    }

    /**
     * 处理所有城市的合同记录数据
     */
    private Map<Integer, KanbanVO> getContractIndexes(List<Integer> cityIdList) {
        LambdaQueryWrapper<ContractEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContractEntity::getDeleteFlag, 0).in(ContractEntity::getCityId, cityIdList)
                .ne(ContractEntity::getContractType, ContractTypeEnum.AGREEMENT.getCode())
                .select(ContractEntity::getFormalFlag, ContractEntity::getCityId,
                        ContractEntity::getFormalStatus, ContractEntity::getApplyStatus, ContractEntity::getEndDate);
        Map<Integer, List<ContractEntity>> cityEntityMap = contractService.list(queryWrapper).stream()
                .collect(Collectors.groupingBy(ContractEntity::getCityId));
        //
        return cityEntityMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> {
            Map<String, Integer> resultMap = contractRecordByCityHandle(entry.getValue());
            return generateKanbanVO(resultMap);
        }));
    }

    /**
     * 多城市数据的聚合
     */
    private KanbanVO aggregateMultiCityData(Map<Integer, KanbanVO> kanbanVOByCityCodeMap) {
        Map<String, Integer> allCityMap = initCityMap();
        kanbanVOByCityCodeMap.forEach((cityCode, kanbanVO) -> {
            if (kanbanVO == null || CollectionUtils.isEmpty(kanbanVO.getSections())) return;
            kanbanVO.getSections().forEach(sectionVo -> {
                sectionVo.getIndices().forEach(indexVo -> {
                    if (StringUtils.isNotBlank(indexVo.getValue1())) {
                        int val = Integer.parseInt(indexVo.getValue1());
                        if (val != 0) allCityMap.compute(indexVo.getKey(), (k, v) -> v == null ? val : v + val);
                    }
                });
            });
        });

        return generateKanbanVO(allCityMap);
    }

    /**
     * 初始化按城市统计的指标数据项
     */
    private Map<String, Integer> initCityMap() {
        Map<String, Integer> _initCityMap = new LinkedHashMap<>(KanbanContractIndexEnum.values().length);
        for (KanbanContractIndexEnum e : KanbanContractIndexEnum.values()) {
            _initCityMap.put(e.getCode(), null);
        }
        return _initCityMap;
    }

    /**
     * 按城市进行数据记录的处理
     */
    private Map<String, Integer> contractRecordByCityHandle(List<ContractEntity> contractEntityList) {
        Map<String, Integer> resultMap = initCityMap();
        LocalDate after30days = LocalDate.now().plusDays(30);
        for (ContractEntity entity : contractEntityList) {

            // 补充“审批中”逻辑的判定：提交审批、审核中、代理合同预审核
            if (entity.getApplyStatus().equals(ContractApplyStatusEnum.SUBMIT.getCode())
                    || entity.getApplyStatus().equals(ContractApplyStatusEnum.APPROVING.getCode())
                    || entity.getApplyStatus().equals(ContractApplyStatusEnum.PRE_APPROVING.getCode())) {
                resultMap.compute(KanbanContractIndexEnum.APPROVING.getCode(), (k, v) -> v == null ? 1 : v + 1);
            }

            // 审核通过时
            if (entity.getApplyStatus().equals(ContractApplyStatusEnum.APPROVED.getCode())) {
                if (!entity.getFormalFlag().equals(BooleFlagEnum.YES.getCode())) continue;

                // 归档中
                if (entity.getFormalStatus().equals(ContractStatusEnum.PENDING.getCode())) {
                    resultMap.compute(KanbanContractIndexEnum.ARCHIVED.getCode(), (k, v) -> v == null ? 1 : v + 1);
                }

                // 待执行
                if (entity.getFormalStatus().equals(ContractStatusEnum.WAIT_EXECUTE.getCode())) {
                    resultMap.compute(KanbanContractIndexEnum.TO_BE_EXECUTED.getCode(), (k, v) -> v == null ? 1 : v + 1);
                }

                // 执行中
                if (entity.getFormalStatus().equals(ContractStatusEnum.EXECUTING.getCode())) {
                    resultMap.compute(KanbanContractIndexEnum.EXECUTING.getCode(), (k, v) -> v == null ? 1 : v + 1);

                    // 30天内到期
                    if (entity.getEndDate() != null && !entity.getEndDate().isAfter(after30days)) {
                        resultMap.compute(KanbanContractIndexEnum.EXPIRES_IN_SEVERAL_DAYS.getCode(), (k, v) -> v == null ? 1 : v + 1);
                    }
                }
            }

            if (!entity.getFormalFlag().equals(BooleFlagEnum.YES.getCode())) continue;

            // 已到期
            if (entity.getFormalStatus().equals(ContractStatusEnum.EXPIRED.getCode())) {
                resultMap.compute(KanbanContractIndexEnum.EXPIRED.getCode(), (k, v) -> v == null ? 1 : v + 1);
            }
        }

        return resultMap;
    }

    /**
     * 指标数据封装、适配
     */
    private KanbanVO generateKanbanVO(Map<String, ?> resultMap) {
        return generateKanbanVO(resultMap, "0");
    }

    private KanbanVO generateKanbanVO(Map<String, ?> resultMap, String defaultValue) {
        List<SectionVO> sections = new ArrayList<>(1);
        KanbanVO kanbanVO = new KanbanVO().setMainTitle("合同状态").setSubTitle(INDEX_DEFAULT_PLACEHOLDER)
                .setShowType(1)
                .setTips("展示鼠标移动上去提示").setShow(true).setSections(sections);
        //
        List<IndexVO> indices = new ArrayList<>(resultMap.size());
        sections.add(new SectionVO().setMainTitle("合同数").setSubTitle(INDEX_DEFAULT_PLACEHOLDER)
                .setShowCount(resultMap.size()).setDataType("index").setShow(true).setIndices(indices));
        //
        for (Map.Entry<String, ?> entry : resultMap.entrySet()) {
            if ("default".equals(entry.getKey())) continue;
            indices.add(new IndexVO().setKey(entry.getKey()).setName(KanbanContractIndexEnum.getDesc(entry.getKey()))
                    .setValue1(entry.getValue() == null ? defaultValue : String.valueOf(entry.getValue()))
                    .setValue2(INDEX_DEFAULT_PLACEHOLDER)
                    .setUnitType("number"));
        }

        return kanbanVO;
    }

    /**
     * 将结果数据放进缓存
     */
    private void saveResultToCache(Map<Integer, KanbanVO> kanbanVOByCityCodeMap, String cacheKeyFormat) {
        if (kanbanVOByCityCodeMap.isEmpty()) return;
        // 按城市保存结果数据
        stringRedisTemplate.executePipelined((RedisCallback<Void>) conn -> {
            kanbanVOByCityCodeMap.forEach((cityCode, kanbanVO) -> {
                String cacheKey = String.format(cacheKeyFormat, cityCode);
                try {
                    long timeout = 300 + ThreadLocalRandom.current().nextInt(1, 10);
                    conn.stringCommands()
                            .set(cacheKey.getBytes(StandardCharsets.UTF_8), OBJECT_MAPPER.writeValueAsBytes(kanbanVO),
                                    Expiration.seconds(timeout), RedisStringCommands.SetOption.upsert());
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
            });
            return null;
        });

        log.info("[{}]下{}个城市的缓存数据统计结果已更新！", cacheKeyFormat, kanbanVOByCityCodeMap.size());
    }

    /**
     * 获取统计数据
     *
     * @param date 查询日期
     */
    public List<ContractPointStatisticsVO> getStatisticsPointData(LocalDate date, List<Integer> cityIds) {
        // 2. 构建查询条件
        LambdaQueryWrapper<ContractPointStatisticsEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContractPointStatisticsEntity::getStatisticsDate, date);
        queryWrapper.in(CollectionUtils.isNotEmpty(cityIds), ContractPointStatisticsEntity::getCityId, cityIds);

        // 3. 查询并转换数据
        return contractPointStatisticsService.list(queryWrapper).stream()
                .map(ContractPointStatisticsConvert.INSTANCE::toVO).collect(Collectors.toList());
    }


    /**
     * 用户权限校验
     */
    private UserDataAccessDTO validateUserAccess() {
        UserDataAccessDTO userDataAccess = dataAccessService.getUserDataAccess();
        if (Objects.isNull(userDataAccess)) {
            log.warn("用户:{}, 无数据访问权限", UserThreadLocal.getUserId());
            return null;
        }

        DataAccessTypeEnum accessType = userDataAccess.getAccessType();
        if (DataAccessTypeEnum.SELF == accessType || DataAccessTypeEnum.SELF_AND_SUB == accessType) {
            log.warn("用户:{}, 无数据访问权限", UserThreadLocal.getUserId());
            return null;
        }

        return userDataAccess;
    }

    /**
     * 构建查询条件
     *
     * @param startDate      开始日期
     * @param endDate        结束日期
     * @param userDataAccess 城市ID集合
     * @return 查询条件
     */
    private LambdaQueryWrapper<ContractPointStatisticsEntity> buildQueryWrapper(LocalDate startDate, LocalDate endDate, UserDataAccessDTO userDataAccess) {
        LambdaQueryWrapper<ContractPointStatisticsEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.between(ContractPointStatisticsEntity::getStatisticsDate, startDate, endDate);
        Set<Integer> cityIds = new HashSet<>(userDataAccess.getCityIds());
        queryWrapper.in(CollectionUtils.isNotEmpty(cityIds), ContractPointStatisticsEntity::getCityId, cityIds);
        return queryWrapper;
    }

    private LambdaQueryWrapper<ContractPointStatisticsEntity> buildQueryWrapper(LocalDate startDate, LocalDate endDate, List<Integer> cityIds) {
        LambdaQueryWrapper<ContractPointStatisticsEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.between(ContractPointStatisticsEntity::getStatisticsDate, startDate, endDate);
        queryWrapper.in(CollectionUtils.isNotEmpty(cityIds), ContractPointStatisticsEntity::getCityId, cityIds);
        return queryWrapper;
    }

    /**
     * 持久化统计数据
     *
     * @param date
     */
    public void statisticsPointData(LocalDate date) {
        LocalDate statisticsDate = Optional.ofNullable(date).orElse(LocalDate.now());
        log.info("开始统计点位数据，统计日期：{}", statisticsDate);
        // 获取统计数据
        List<ContractPointStatisticsEntity> statisticsEntities = assembleStatisticsPointData(statisticsDate);
        // 删除统计日期相同的数据
        contractPointStatisticsService.remove(new LambdaQueryWrapper<ContractPointStatisticsEntity>().eq(ContractPointStatisticsEntity::getStatisticsDate, statisticsDate));
        // 批量入库
        contractPointStatisticsService.saveBatch(statisticsEntities);
        // 存入Redis todo
        log.info("统计点位数据结束，统计日期：{}", statisticsDate);
    }

    /**
     * 根据日期统计点位数据
     */
    public List<ContractPointStatisticsEntity> assembleStatisticsPointData(LocalDate statisticsDate) {
        log.info("开始统计点位数据，统计日期：{}", statisticsDate);

        try {
            // 获取所有城市信息
            ResultTemplate<List<CodeNameVO>> availableCities = feignAuthorityRpc.getAvailableCities();
            if (availableCities == null || CollectionUtils.isEmpty(availableCities.getData())) {
                log.warn("未找到需要统计的城市");
                return Collections.emptyList();
            }
            // cityId->cityName
            Map<Integer, String> cityNameMap = availableCities.getData().stream()
                    .collect(Collectors.toMap(CodeNameVO::getId, CodeNameVO::getName, (v1, v2) -> v1));
            // 所有城市的cityId Set
            Set<Integer> allCityIds = cityNameMap.keySet();


            // 获取合同数据
            Map<Integer, List<ContractEntity>> cityStatusMap = getContractsByCityId(statisticsDate);

            // 获取SSP点位状态数据
            Map<Integer, List<SspPointStatusDto>> sspCityMap = getSspPointStatusByCityId();

            // 并行处理提升性能
            List<ContractPointStatisticsEntity> result = allCityIds.parallelStream()
                    .map(cityId -> buildPointStatistics(cityId, cityNameMap.get(cityId), sspCityMap.get(cityId), cityStatusMap.get(cityId), statisticsDate))
                    .collect(Collectors.toList());

            log.info("点位数据统计完成，统计日期：{}，处理城市数：{}", statisticsDate, result.size());
            return result;
        } catch (Exception e) {
            log.error("点位数据统计异常，统计日期：{}", statisticsDate, e);
            throw new RuntimeException("点位数据统计失败", e);
        }
    }

    /**
     * 获取合同数据 待执行和执行中的合同
     */
    private Map<Integer, List<ContractEntity>> getContractsByCityId(LocalDate date) {
        LocalDate paramDate = date.plusDays(1);
        // 如果没有合同数据，则返回空集合
        List<ContractEntity> contracts = contractService.lambdaQuery()
                .eq(ContractEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .in(ContractEntity::getContractType, Arrays.asList(ContractTypeEnum.NORMAL.getCode(), ContractTypeEnum.CHANGE.getCode(), ContractTypeEnum.HISTORY.getCode()))
                .in(ContractEntity::getFormalStatus, Arrays.asList(ContractStatusEnum.WAIT_EXECUTE.getCode(), ContractStatusEnum.EXECUTING.getCode()))
                .lt(ContractEntity::getCreateTime, paramDate).list();

        return contracts.stream().collect(Collectors.groupingBy(ContractEntity::getCityId));
    }

    /**
     * 获取SSP点位状态数据
     */
    private Map<Integer, List<SspPointStatusDto>> getSspPointStatusByCityId() {
        ResultTemplate<List<SspPointStatusDto>> response = feignSspRpc.countPointStatus();
        List<SspPointStatusDto> cityPointStatus = Optional.ofNullable(response).filter(ResultTemplate::getSuccess)
                .map(ResultTemplate::getData).orElseGet(() -> {
                    log.warn("获取SSP点位状态失败");
                    return Collections.emptyList();
                });

        return cityPointStatus.stream().collect(Collectors.groupingBy(SspPointStatusDto::getCity));
    }


    private Map<Integer, String> getCityNameMap(Set<Integer> cityIds) {
        ResultTemplate<List<CodeNameVO>> response = feignAuthorityRpc.listCityByIds(new ArrayList<>(cityIds));

        return Optional.ofNullable(response).filter(ResultTemplate::getSuccess).map(ResultTemplate::getData)
                .map(cityList -> cityList.stream()
                        .collect(Collectors.toMap(CodeNameVO::getId, CodeNameVO::getName, (v1, v2) -> v1)))
                .orElse(Collections.emptyMap());
    }

    private ContractPointStatisticsEntity buildPointStatistics(Integer cityId, String cityName, List<SspPointStatusDto> sspData, List<ContractEntity> contractData, LocalDate date) {

        ContractPointStatisticsEntity entity = new ContractPointStatisticsEntity();
        entity.setCityId(cityId);
        entity.setCityName(cityName);
        entity.setStatisticsDate(date);

        // 组装SSP数据
        assembleSspData(entity, sspData);

        // 组装合同数据
        assembleContractData(entity, contractData);

        // 计算计费可售比
        calculateChargedAvailableRatio(entity);

        return entity;
    }

    private void calculateChargedAvailableRatio(ContractPointStatisticsEntity entity) {
        entity.setChargedAvailableRatio(calculateRate(entity.getAvailableCount(), entity.getChargedCount()));
    }

    private BigDecimal calculateRate(Integer numerator, Integer denominator) {
        if (denominator == null || numerator == null || denominator == 0) {
            return null;
        }
        return BigDecimal.valueOf(numerator)
                .divide(BigDecimal.valueOf(denominator), 3, RoundingMode.HALF_UP)  // 保留3位小数
                .multiply(BigDecimal.valueOf(100))
                .setScale(1, RoundingMode.HALF_UP);
    }

    /**
     * 组装合同数据
     *
     * @param entity
     * @param contractList
     */
    private void assembleContractData(ContractPointStatisticsEntity entity, List<ContractEntity> contractList) {
        // 1. 参数校验和默认值设置
        if (entity == null) {
            log.warn("PointStatisticsEntity is null, skip contract data assembly");
            return;
        }

        // 2. 使用 Optional 处理空集合，设置默认值
        if (CollectionUtils.isEmpty(contractList)) {
            setDefaultContractCounts(entity);
            return;
        }

        try {
            // 3. 合同状态分组获取ID集合  cityId -> [合同ID集合]
            Map<String, List<Integer>> statusContractIdMap = contractList.stream()
                    .collect(Collectors.groupingBy(ContractEntity::getFormalStatus, Collectors.mapping(ContractEntity::getId, Collectors.toList())));

            // 4. 获取各状态合同数量
            Integer waitExecuteCount = getContractCount(statusContractIdMap, ContractStatusEnum.WAIT_EXECUTE.getCode());
            Integer executingCount = getContractCount(statusContractIdMap, ContractStatusEnum.EXECUTING.getCode());

            // 5. 设置统计结果
            entity.setChargedCount(executingCount);
            entity.setSignedCount(waitExecuteCount + executingCount);

        } catch (Exception e) {
            log.error("Failed to assemble contract data", e);
            setDefaultContractCounts(entity);
        }
    }

    private void setDefaultContractCounts(ContractPointStatisticsEntity entity) {
        entity.setChargedCount(0);
        entity.setSignedCount(0);
    }

    private Integer getContractCount(Map<String, List<Integer>> statusMap, String status) {
        return Optional.ofNullable(statusMap.get(status)).map(contractDeviceService::querySignCountByContractIds)
                .orElse(0);
    }

    /**
     * 组装SSP数据
     *
     * @param entity
     * @param sspCityPointStatusList
     */
    private void assembleSspData(ContractPointStatisticsEntity entity, List<SspPointStatusDto> sspCityPointStatusList) {
        // 1. 参数校验
        if (entity == null || CollectionUtils.isEmpty(sspCityPointStatusList)) {
            setDefaultValues(entity);
            return;
        }

        // 2. 一次性计算所有状态的点位数
        Map<String, Integer> statusCountMap = sspCityPointStatusList.stream()
                .collect(Collectors.groupingBy(SspPointStatusDto::getPointStatus, Collectors.summingInt(SspPointStatusDto::getPointStatusCount)));

        // 可售数
        entity.setAvailableCount(statusCountMap.getOrDefault(PointStatusEnum.SALEABLE.getCode(), 0));
        // 故障数
        entity.setFaultCount(statusCountMap.getOrDefault(PointStatusEnum.MAINTAINING.getCode(), 0));

        // 安装数 -> 暂时不处理，前端置灰
        entity.setInstalledCount(null);

    }


    private void setDefaultValues(ContractPointStatisticsEntity entity) {
        if (entity != null) {
            entity.setAvailableCount(0);
            entity.setInstalledCount(0);
            entity.setFaultCount(0);
        }
    }

    public List<Map<String, String>> getColumnComments(String tableName) {
        return contractPointStatisticsService.getColumnComments(tableName);
    }

    /**
     * 根据城市统计合作点位数据（PC）
     */
    public List<KanbanVO> getStatisticsPointDataPC(LocalDate date) {
        // 获取vo对象对应数据表的字段信息
        String tableName = TableNameUtil.getTableName(ContractPointStatisticsEntity.class);
        List<Map<String, String>> columnComments = getColumnComments(tableName);
        // 解析出map中包含count和ratio的字段，放进一个新的map中
        Map<String, String> columnCommentMap = columnComments.stream()
                .filter(map -> map.get("COLUMN_NAME").contains("count") || map.get("COLUMN_NAME").contains("ratio"))
                .collect(Collectors.toMap(map -> map.get("COLUMN_NAME"), map -> map.get("COLUMN_COMMENT"), (v1, v2) -> v1, LinkedHashMap::new));
        // 1. 参数校验
        List<Integer> cityIds = getUserKanbanCities(0);
        // 获取原始的点位数据
        List<ContractPointStatisticsVO> contractPointStatisticsVOList = getStatisticsPointData(date, cityIds);
        if (CollectionUtils.isEmpty(contractPointStatisticsVOList)) {
            return getDefaultPointDataKanbanVO(columnCommentMap);
        }

        Map<String, Integer> cityAllResultMap = columnCommentMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> 0, (v1, v2) -> v1, LinkedHashMap::new));
        List<KanbanVO> kanbanVOList = new ArrayList<>(contractPointStatisticsVOList.size() + 1);

        // 计算每一个城市的指标
        assembleContractPointStatisticsVO(contractPointStatisticsVOList, columnCommentMap, cityAllResultMap, kanbanVOList);

        // 汇总全部城市的指标
        assembleContractPointStatisticsVO(columnCommentMap, cityAllResultMap, kanbanVOList);

        // 封装数据
        return kanbanVOList;
    }

    /**
     * @Author：TanJie
     * @Date：2025-01-20 17:51
     * @Description：生成默认的合作数据返回结构
     */
    private List<KanbanVO> getDefaultPointDataKanbanVO(Map<String, String> columnCommentMap) {
        List<SectionVO> sectionAllVO = new ArrayList<>(1);
        KanbanVO kanbanVO = new KanbanVO()
                .setMainTitle("全部")
                .setSubTitle("-")
                .setShowType(1)
                .setTips("")
                .setShow(true)
                .setSections(sectionAllVO);
        // 遍历指标
        List<IndexVO> indices = new ArrayList<>(columnCommentMap.size());
        SectionVO sectionVO = new SectionVO()
                .setMainTitle("点位数")
                .setIndices(indices);
        sectionAllVO.add(sectionVO);
        // 解析指标
        columnCommentMap.forEach((enName, zhName) -> {
            indices.add(new IndexVO().setKey(enName).setName(zhName).setValue1(INDEX_DEFAULT_PLACEHOLDER)
                    .setUnitType(enName.contains("count") ? "number" : "percent"));
        });

        return List.of(kanbanVO);
    }

    /**
     * 从合作点位VO数据中封装数据
     */
    private void assembleContractPointStatisticsVO(List<ContractPointStatisticsVO> contractPointStatisticsVOList,
                                                   Map<String, String> columnCommentMap,
                                                   Map<String, Integer> cityAllResultMap,
                                                   List<KanbanVO> kanbanVOList) {
        Class<?> clazz = ContractPointStatisticsVO.class;
        // 遍历城市数据
        for (var contractPointStatisticsVO : contractPointStatisticsVOList) {
            List<SectionVO> sectionAllVO = new ArrayList<>(1);
            KanbanVO kanbanVO = new KanbanVO()
                    .setMainTitle(contractPointStatisticsVO.getCityName())
                    .setSubTitle("-")
                    .setShowType(1)
                    .setTips("")
                    .setShow(true)
                    .setSections(sectionAllVO);
            kanbanVOList.add(kanbanVO);
            // 遍历指标
            List<IndexVO> indices = new ArrayList<>(columnCommentMap.size());
            SectionVO sectionVO = new SectionVO()
                    .setMainTitle("点位数")
                    .setIndices(indices);
            sectionAllVO.add(sectionVO);
            // 解析指标
            columnCommentMap.forEach((enName, zhName) -> {
                String methodName = StrUtil.toCamelCase("get_" + enName);
                try {
                    Method method = clazz.getMethod(methodName);
                    Object o = method.invoke(contractPointStatisticsVO);
                    IndexVO indexVO = new IndexVO().setKey(enName).setName(zhName);
                    // 百分比处理
                    if (enName.contains("ratio")) {
                        o = ((BigDecimal) o).setScale(1, RoundingMode.HALF_UP);
                        indexVO.setValue1(o + PERCENT_UNIT).setUnitType("percent");
                    } else {
                        indexVO.setValue1(o.toString()).setUnitType("number");
                    }
                    indices.add(indexVO);
                    //
                    if (enName.contains("count") && o instanceof Integer)
                        cityAllResultMap.put(enName, Integer.sum(cityAllResultMap.get(enName), (Integer) o));
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });
        }
    }

    private void assembleContractPointStatisticsVO(Map<String, String> columnCommentMap,
                                                   Map<String, Integer> cityAllResultMap,
                                                   List<KanbanVO> kanbanVOList) {
        List<SectionVO> sectionAllVO = new ArrayList<>(1);
        KanbanVO kanbanVO = new KanbanVO()
                .setMainTitle("全部")
                .setSubTitle("-")
                .setShowType(1)
                .setTips("")
                .setShow(true)
                .setSections(sectionAllVO);
        kanbanVOList.add(0, kanbanVO);
        // 遍历指标
        List<IndexVO> indices = new ArrayList<>(cityAllResultMap.size());
        SectionVO sectionVO = new SectionVO()
                .setMainTitle("点位数")
                .setIndices(indices);
        sectionAllVO.add(sectionVO);
        // 解析指标
        cityAllResultMap.forEach((enName, num) -> {
            // 单独汇总count的值
            if (enName.contains("count")) {
                indices.add(new IndexVO().setKey(enName).setName(columnCommentMap.get(enName))
                        .setValue1(num.toString()).setUnitType("number"));
            }
        });
        // 单独处理ratio的值
        String ratioKey = "charged_available_ratio";
        IndexVO indexVO = new IndexVO().setKey(ratioKey).setName(columnCommentMap.get(ratioKey)).setUnitType("percent");
        if (cityAllResultMap.get("charged_count") == null) {
            indexVO.setValue1("0" + PERCENT_UNIT);
        } else {
            BigDecimal numerator = new BigDecimal(cityAllResultMap.get("available_count") * 100);
            BigDecimal denominator = new BigDecimal(cityAllResultMap.get("charged_count"));
            BigDecimal result = denominator.equals(BigDecimal.ZERO) ? BigDecimal.ZERO : numerator.divide(denominator, 1, RoundingMode.HALF_UP);
            indexVO.setValue1(result + PERCENT_UNIT);
        }
        indices.add(indexVO);
    }

    /**
     * 导出合同数据
     */
    public String exportDataForContract(Integer flag, LocalDate date, String fileNameFlag) {
        List<KanbanVO> exportDataList=new ArrayList<>();
        if (flag == 1){
            exportDataList = getListForContractPC();
        }else if (flag == 2){
           exportDataList = getStatisticsPointDataPC(date);
        }
        // 待写入excel文件的数据绑定
        List<List<String>> headList2R = new ArrayList<>();
        List<List<String>> dataList = new ArrayList<>();
        kanbanExportDataBinding(exportDataList, headList2R, dataList);

        try {
            // 创建临时文件
            File tempFile = File.createTempFile(UUID.randomUUID().toString(), ".xlsx");
            tempFile.deleteOnExit();
            // 将excel数据上传到cloud
            EasyExcel.write(tempFile)
                    .sheet("sheet")
                    .head(headList2R)
                    .doWrite(dataList);

            return uploadToCloud(tempFile, fileNameFlag);
        } catch (IOException e) {
            log.error("导出合同看板数据异常！", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 生成可以被导出的数据
     */
    private void kanbanExportDataBinding(List<KanbanVO> exportDataList, List<List<String>> headList2R, List<List<String>> dataList) {
        // 解析出head
        headList2R.add(List.of("城市", "城市"));
        exportDataList.get(0).getSections().forEach(sectionVo -> {
            sectionVo.getIndices().forEach(indexVo -> {
                headList2R.add(List.of(sectionVo.getMainTitle(), indexVo.getName()));
            });
        });
        // 解析出body
        exportDataList.forEach(kanbanVO -> {
            List<String> rowData = new ArrayList<>();
            dataList.add(rowData);
            rowData.add(kanbanVO.getMainTitle());
            kanbanVO.getSections().forEach(sectionVo -> {
                sectionVo.getIndices().forEach(indexVo -> {
                    rowData.add(indexVo.getValue1());
                });
            });
        });
    }

    /**
     * 根据城市id获取点位数据
     *
     * @param date
     * @param days
     * @param cityId
     * @return
     */
    public List<ContractPointStatisticsVO> getStatisticsPointDataByCityId(LocalDate date, Integer days, Integer cityId) {
        // 1. 参数校验
        List<Integer> cityIds = getUserKanbanCities(cityId);

        LocalDate startDate = date.minusDays(days - 1);
        LambdaQueryWrapper<ContractPointStatisticsEntity> queryWrapper = buildQueryWrapper(startDate, date, cityIds);
        List<ContractPointStatisticsEntity> pointStatisticsEntityList = contractPointStatisticsService.list(queryWrapper);

        // 单个城市，直接返回
        if (cityId != 0) {
            List<ContractPointStatisticsVO> result = pointStatisticsEntityList.stream()
                    .map(ContractPointStatisticsConvert.INSTANCE::toVO).collect(Collectors.toList());
            result.sort(Comparator.comparing(ContractPointStatisticsVO::getStatisticsDate,
                    Comparator.nullsLast(Comparator.naturalOrder())));
            // 日期格式化 字符串 yyyy-MM-dd -> MM/dd
            result.forEach(contractPointStatisticsVO -> {
                contractPointStatisticsVO.setStatisticsDate(convertDate(contractPointStatisticsVO.getStatisticsDate()));
            });
            return result;
        }

        // 多个城市，合并数据
        List<ContractPointStatisticsVO> totalList = new ArrayList<>();
        // pointStatisticsEntityList 按照日期进行分组
        Map<LocalDate, List<ContractPointStatisticsEntity>> dateMap = pointStatisticsEntityList.stream()
                .collect(Collectors.groupingBy(ContractPointStatisticsEntity::getStatisticsDate));
        dateMap.forEach((statisticsDate, entityList) -> {
            ContractPointStatisticsVO contractPointStatisticsVO = new ContractPointStatisticsVO();
            contractPointStatisticsVO.setStatisticsDate(statisticsDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            contractPointStatisticsVO.setCityId(0);
            contractPointStatisticsVO.setCityName("全部");
            contractPointStatisticsVO.setAvailableCount(entityList.stream()
                    .mapToInt(ContractPointStatisticsEntity::getAvailableCount).sum());
            contractPointStatisticsVO.setInstalledCount(entityList.stream()
                    .mapToInt(ContractPointStatisticsEntity::getInstalledCount).sum());
            contractPointStatisticsVO.setFaultCount(entityList.stream()
                    .mapToInt(ContractPointStatisticsEntity::getFaultCount).sum());
            contractPointStatisticsVO.setChargedCount(entityList.stream()
                    .mapToInt(ContractPointStatisticsEntity::getChargedCount).sum());
            contractPointStatisticsVO.setSignedCount(entityList.stream()
                    .mapToInt(ContractPointStatisticsEntity::getSignedCount).sum());
            contractPointStatisticsVO.setChargedAvailableRatio(calculateRate(contractPointStatisticsVO.getAvailableCount(), contractPointStatisticsVO.getChargedCount()));
            totalList.add(contractPointStatisticsVO);
        });
        totalList.sort(Comparator.comparing(ContractPointStatisticsVO::getStatisticsDate,
                Comparator.nullsLast(Comparator.naturalOrder())));

        return totalList;
    }

    /**
     * 日期格式化 字符串 yyyy-MM-dd -> MM/dd
     */
    private String convertDate(String date) {
        return date;
    }

    /**
     * 获取用户的城市列表
     *
     * @param cityId cityId=0表示全部城市，其他值表示指定城市
     * @return
     */
    private List<Integer> getUserKanbanCities(Integer cityId) {
        // 1. 参数校验和初始化
        UserDataAccessDTO userAccess = validateUserAccess();
        if (userAccess == null) {
            throw new IllegalStateException("用户数据访问权限验证失败");
        }

        // 2. 获取可用城市列表
        List<Integer> availableCityIds = Optional.ofNullable(feignAuthorityRpc.getAvailableCities())
                .map(ResultTemplate::getData)
                .map(cities -> cities.stream().map(CodeNameVO::getId).toList())
                .orElseThrow(() -> new IllegalStateException("获取可用城市列表失败"));

        List<Integer> userCityIds = userAccess.getCityIds();

        // 3. 处理全部城市权限的情况
        if (userCityIds.contains(0)) {
            return handleAllCitiesAccess(cityId, availableCityIds);
        }

        // 4. 处理指定城市权限的情况
        return handleSpecificCitiesAccess(cityId, userCityIds, availableCityIds);
    }

    private List<Integer> handleAllCitiesAccess(Integer cityId, List<Integer> availableCityIds) {
        if (cityId == 0) {
            return availableCityIds;
        }

        if (availableCityIds.contains(cityId)) {
            return List.of(cityId);
        }

        throw new IllegalStateException("无此城市权限");
    }

    private List<Integer> handleSpecificCitiesAccess(Integer cityId, List<Integer> userCityIds, List<Integer> availableCityIds) {
        if (cityId == 0) {
            return userCityIds.stream()
                    .filter(availableCityIds::contains)
                    .toList();
        }

        if (userCityIds.contains(cityId) && availableCityIds.contains(cityId)) {
            return List.of(cityId);
        }

        throw new IllegalStateException("无此城市权限");
    }

    /**
     * 上传文件到cos
     */
    private String uploadToCloud(File tempFile, String fileNameFlag) {
        // 实时数据_导出年月日时分秒
        String fileName = String.format("%s_%s.xlsx", fileNameFlag, LocalDateTime.now()
                .format(DateTimeFormatter.ofPattern(DatePattern.PURE_DATETIME_PATTERN)));
        String feature = "venue";
        ObjectUtils.uploadFile(ObjectUtils.getCosFileName(feature, fileName), tempFile);
        return ObjectUtils.getAccessUrl(feature, fileName);
    }

    /**
     * @Author：TanJie
     * @Date：2025-01-14 16:10
     * @Description：获取设备导入记录列表(分页)
     */
    public PageResponseVo<StatisticsDeviceImportRecordVO> getDeviceImportList(PageRequestVo<Void> pageRequest) {
        //如果当前页码小于零或者为空，则设置为1
        if (pageRequest.getCurrentPage() == null || pageRequest.getCurrentPage() <= 0) {
            pageRequest.setCurrentPage(1L);
        }
        // 如果每页大小小于等于或者为空，则设置为20
        if (pageRequest.getPageSize() == null || pageRequest.getPageSize() <= 0) {
            pageRequest.setPageSize(VenueConstants.DEFAULT_PAGE_SIZE);
        }

        QueryWrapper<StatisticsDeviceImportRecordEntity> queryWrapper = new QueryWrapper<>();
        // 排序处理
        if (pageRequest.getAscColumnNames() != null && !pageRequest.getAscColumnNames().isEmpty()) {
            pageRequest.getAscColumnNames().forEach(columnName -> {
                queryWrapper.orderByAsc(StrUtil.toUnderlineCase(columnName));
            });
        }
        if (pageRequest.getDescColumnNames() != null && !pageRequest.getDescColumnNames().isEmpty()) {
            pageRequest.getDescColumnNames().forEach(columnName -> {
                queryWrapper.orderByDesc(StrUtil.toUnderlineCase(columnName));
            });
        }
        // 使用lambda
        queryWrapper.lambda().eq(StatisticsDeviceImportRecordEntity::getDeleted, 0);

        if (pageRequest.getQuery() == null) {
            // 后续可能会添加其他查询条件，暂时先不处理
        }
        Page<StatisticsDeviceImportRecordEntity> page = statisticsDeviceImportRecordService.page(
                new Page<>(pageRequest.getCurrentPage(), pageRequest.getPageSize()), queryWrapper);

        // 数据转换
        List<StatisticsDeviceImportRecordVO> list = page.getRecords().stream()
                .map(e -> BeanUtil.toBean(e, StatisticsDeviceImportRecordVO.class)).collect(Collectors.toList());
        return new PageResponseVo<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), list, page.getTotal());
    }

    /**
     * @Author：TanJie
     * @Date：2025-01-15 09:51
     * @Description：设备数据导入操作
     */
    public String deviceRecordImport(LocalDate statisticsDate, MultipartFile file) {
        // 数据校验：导入成功 or 判定异常记录的文件地址
        return kanbanDeviceRecordImportHelper.deviceRecordImport(statisticsDate, file);
    }

    /**
     * @Author：TanJie
     * @Date：2025-01-15 18:57
     * @Description：删除设备导入记录
     */
    public boolean deleteDeviceImportRecord(Long id) {
        return statisticsDeviceImportRecordService.updateById(StatisticsDeviceImportRecordEntity.builder()
                .id(id)
                .deleted(1)
                .operator(UserThreadLocal.getUserId().longValue())
                .build());
    }

    /**
     * @Author：TanJie
     * @Date：2025-01-15 18:58
     * @Description：上传设备导入记录模板
     */
    public String uploadDeviceImportTemplate(MultipartFile file) {
        try {
            String fileName = "设备数据导入模板.xlsx";
            File tempSourceFile = File.createTempFile(UUID.randomUUID().toString(), ".xlsx");
            tempSourceFile.deleteOnExit();
            file.transferTo(tempSourceFile);
            String feature = "venue";
            ObjectUtils.uploadFile(ObjectUtils.getCosFileName(feature, fileName), tempSourceFile);
            return ObjectUtils.getAccessUrl(feature, fileName);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * @Author：TanJie
     * @Date：2025-01-17 15:59
     * @Description：获取设备统计数据
     */
    public KanbanDeviceStatisticsVO getDeviceStatisticsData(List<Integer> cityIds, LocalDate filterDate, String accessType) {
        // 数据记录获取
        StatisticsDeviceCityWithImportRecordDTO cityDataTotal = statisticsDeviceCityDataQuantityService.getCityTotalByStatisticsDateAndCityId(filterDate, cityIds);
        if (cityDataTotal == null) {
            return kanbanDeviceRecordImportHelper.getDefaultKanbanDeviceStatisticsVO(filterDate, accessType);
        }

        // 数据权限为"全部"时，获取对应批次的生产数据
        StatisticsDeviceProductionQuantityDTO ProdDataTotal = null;
        if (accessType.equals(DataAccessTypeEnum.ALL.getCode())) {
            Long importNo = cityDataTotal.getImportNo();
            ProdDataTotal = statisticsDeviceProductionQuantityService.getProdTotalByImportNo(importNo);
        }

        // 组装返回数据
        KanbanDeviceStatisticsVO vo = new KanbanDeviceStatisticsVO<Long>();
        vo.setLatestUpdateDate(cityDataTotal.getStatisticsDate());
        vo.setDeviceDataList(kanbanDeviceRecordImportHelper.getDeviceItemDataList(cityDataTotal, ProdDataTotal, accessType));
        vo.setRatioData(kanbanDeviceRecordImportHelper.getDeviceFunnelDataList(cityDataTotal, ProdDataTotal, accessType, vo.getDeviceDataList().size()));

        return vo;
    }
}
