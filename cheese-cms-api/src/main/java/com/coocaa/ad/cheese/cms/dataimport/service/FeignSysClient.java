package com.coocaa.ad.cheese.cms.dataimport.service;

import com.coocaa.ad.cheese.cms.common.tools.common.result.ResultTemplate;
import com.coocaa.ad.cheese.cms.dataimport.pojo.DistrictVo;
import com.coocaa.ad.cheese.cms.dataimport.pojo.DistrictVoRet;
import com.coocaa.ad.common.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "cheese-authority-api",
        // url = "http://localhost:8001",
        configuration = FeignConfig.class)
public interface FeignSysClient {
    @PostMapping("/sys/city/name/county")
    ResultTemplate<DistrictVoRet> county(@RequestBody DistrictVo districtVo);

    @GetMapping("/sys/city/name/{name}")
    ResultTemplate<DistrictVoRet> city(@RequestParam("name") String name);

}
