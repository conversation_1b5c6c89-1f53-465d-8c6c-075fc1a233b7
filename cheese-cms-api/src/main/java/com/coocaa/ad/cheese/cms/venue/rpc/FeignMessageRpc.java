package com.coocaa.ad.cheese.cms.venue.rpc;

import com.coocaa.ad.cheese.cms.common.tools.common.result.ResultTemplate;
import com.coocaa.ad.cheese.cms.venue.rpc.bean.ApprovalMessageParam;
import com.coocaa.ad.common.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 飞书消息通知系统接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-04
 */
@FeignClient(
//        name = "cheese-authority-api",
        value = "cheese-authority-api", path = "/sys/message",
//        url = "http://beta-cheese-ssp.coocaa.com",
        configuration = FeignConfig.class)
public interface FeignMessageRpc {

    /**
     * 发送通知
     */
    @PostMapping("/approve-handle")
    ResultTemplate<String> approveSendMessage(@RequestBody ApprovalMessageParam param);
} 