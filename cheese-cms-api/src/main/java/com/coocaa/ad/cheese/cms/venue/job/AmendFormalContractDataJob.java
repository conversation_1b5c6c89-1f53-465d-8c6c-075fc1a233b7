package com.coocaa.ad.cheese.cms.venue.job;

import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractAmendmentChangeTypeEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractDepositSupplierEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractDeviceEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractDevicePointEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractLogEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractPaymentPeriodEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractPriceApplyEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractPricePeriodEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractProjectEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractSubEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractSupplierBankEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractSupplierEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractAmendmentChangeTypeService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractDepositSupplierService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractDevicePointService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractDeviceService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractLogService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractPaymentPeriodService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractPriceApplyService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractPricePeriodService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractProjectService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractSubService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractSupplierBankService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractSupplierService;
import com.coocaa.ad.cheese.cms.common.tools.common.enums.BooleFlagEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractApplyStatusEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractChangeFlagEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractChangeTypeEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractStatusEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractTypeEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.SnapshotSourceTypeEnum;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractSnapshotParam;
import com.coocaa.ad.cheese.cms.venue.convert.contract.ContractDeepClone;
import com.coocaa.ad.cheese.cms.venue.service.ContractSnapshotService;
import com.coocaa.ad.cheese.cms.venue.service.LedgerService;
import com.coocaa.ad.cheese.cms.venue.service.StatusChangeLogService;
import com.coocaa.ad.cheese.cms.venue.util.LogWrapper;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.ad.common.user.bean.CachedUser;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.event.Level;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 修正有效合同数据
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-19 17:36
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class AmendFormalContractDataJob implements ApplicationContextAware {
    private final IContractService contractService;
    private final IContractLogService contractLogService;
    private final IContractSupplierService contractSupplierService;
    private final IContractProjectService contractProjectService;
    private final IContractPriceApplyService contractPriceApplyService;
    private final IContractDeviceService contractDeviceService;
    private final IContractDevicePointService contractDevicePointService;
    private final IContractPricePeriodService contractPricePeriodService;
    private final IContractPaymentPeriodService contractPaymentPeriodService;
    private final IContractSubService contractSubService;
    private final IContractSupplierBankService contractSupplierBankService;
    private final IContractDepositSupplierService contractDepositSupplierService;
    private final IContractAmendmentChangeTypeService contractAmendmentChangeTypeService;
    private final LedgerService ledgerService;
    private final ContractSnapshotService snapshotService;
    private final ContractDeepClone contractDeepClone;
    private final StatusChangeLogService statusChangeLogService;

    private static final String IDENTITY_NAME = "变更合同";
    private static final String LOG_FLAG = "定时任务。";
    private final AtomicReference<String> logFlagHolder = new AtomicReference<>(LOG_FLAG);

    private AmendFormalContractDataJob selfBean;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.selfBean = applicationContext.getBean(AmendFormalContractDataJob.class);
    }

    /**
     * 根据变更合同修正原合同数据
     */
    @XxlJob("amendFormalContractDataJob")
    public void useAmendmentData() {
        long start = System.currentTimeMillis();
        log.info("根据{}修正原合同数据任务开始...", IDENTITY_NAME);
        try {
            // 修正原始合同的数据
            UserThreadLocal.setUser(new CachedUser().setId(0));
            this.logFlagHolder.set(LOG_FLAG);
            amendFormalContractData();

            // 定时任务执行状态反馈
            String logMsg = "根据%s修正原合同数据任务执行成功, 耗时: %sms".formatted(IDENTITY_NAME, System.currentTimeMillis() - start);
            log.info(logMsg);
            XxlJobHelper.log(logMsg);
            XxlJobHelper.handleSuccess();
        } catch (Exception ex) {
            log.error("根据{}修正原合同数据任务执行失败", IDENTITY_NAME, ex);
            XxlJobHelper.handleFail("根据%s修正原合同数据任务执行: %s".formatted(IDENTITY_NAME, ex.getMessage()));
        }
    }

    /**
     * 获取当前执行人
     */
    private Integer getCurrentExecutor() {
        CachedUser user = UserThreadLocal.getUser();
        if (Objects.nonNull(user)) {
            return user.getId();
        }
        return 0;
    }

    /**
     * 修正有效合同的数据
     */
    // @PostConstruct
    public void amendFormalContractData() {
        // 获取所有生效的变更合同记录
        Map<String, List<ContractEntity>> amendmentContractByChangeTypeMap = getAmendmentContractByChangeType();
        if (MapUtils.isEmpty(amendmentContractByChangeTypeMap)) {
            log.info("没有找到任何已到生效日期的{}记录", IDENTITY_NAME);
            return;
        }

        // 分类处理 - 不履约终止
        selfBean.handleNotPerformTermination(amendmentContractByChangeTypeMap.get(ContractChangeTypeEnum.TERMINATE_NOT_PERFORM.getCode()),
                SnapshotSourceTypeEnum.SCHEDULE.getCode());

        // 分类处理 - 提前终止
        selfBean.handleEarlyTermination(amendmentContractByChangeTypeMap.get(ContractChangeTypeEnum.TERMINATE_EARLY.getCode()),
                SnapshotSourceTypeEnum.SCHEDULE.getCode());

        // 分类处理 - 变更
        selfBean.handleChange(amendmentContractByChangeTypeMap.get(ContractChangeTypeEnum.CHANGE.getCode()), SnapshotSourceTypeEnum.SCHEDULE.getCode());
    }

    /**
     * 按变更类型获取变更合同数据
     */
    private Map<String, List<ContractEntity>> getAmendmentContractByChangeType() {
        // 获取所有生效的变更合同记录
        Map<Integer, ContractEntity> contractMap = contractService.lambdaQuery()
                .eq(ContractEntity::getContractType, ContractTypeEnum.AMENDMENT.getCode())
                .eq(ContractEntity::getEffectFlag, BooleFlagEnum.NO.getCode())
                .eq(ContractEntity::getArchiveFlag, BooleFlagEnum.YES.getCode())
                .in(ContractEntity::getApplyStatus, List.of(ContractApplyStatusEnum.APPROVED.getCode(), ContractApplyStatusEnum.PRE_APPROVING.getCode()))
                .le(ContractEntity::getEffectDate, LocalDate.now())
                .eq(ContractEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .list()
                .stream()
                .collect(Collectors.toMap(ContractEntity::getId, Function.identity(), (v1, v2) -> v1));
        if (MapUtils.isEmpty(contractMap)) {
            return null;
        }

        // 获取变更合同对应的变更类型记录
        List<ContractAmendmentChangeTypeEntity> changeTypeEntities = contractAmendmentChangeTypeService.lambdaQuery()
                .in(ContractAmendmentChangeTypeEntity::getContractId, contractMap.keySet())
                .eq(ContractAmendmentChangeTypeEntity::getDeleted, BooleFlagEnum.NO.getCode())
                .list();
        if (CollectionUtils.isEmpty(changeTypeEntities)) {
            return null;
        }

        // 数据组装
        Map<String, List<ContractEntity>> contractByChangeTypeMap = new HashMap<>(changeTypeEntities.size());
        BiConsumer<Integer, String> consumer = (contractId, changeTypeCode) -> {
            if (CollectionUtils.isEmpty(contractByChangeTypeMap.get(changeTypeCode))) {
                contractByChangeTypeMap.put(changeTypeCode, Lists.newArrayList());
            }
            contractByChangeTypeMap.get(changeTypeCode).add(contractMap.get(contractId));
        };
        Set<Integer> existContractIds = new HashSet<>(contractMap.size());
        changeTypeEntities.forEach(changeTypeEntity -> {
            if (existContractIds.contains(changeTypeEntity.getContractId())) {
                return;
            }
            existContractIds.add(changeTypeEntity.getContractId());
            // 提前终止
            if (ContractChangeTypeEnum.TERMINATE_EARLY.getCode().equals(changeTypeEntity.getSubChangeType())) {
                consumer.accept(changeTypeEntity.getContractId(), ContractChangeTypeEnum.TERMINATE_EARLY.getCode());
            }
            // 不履约终止
            else if (ContractChangeTypeEnum.TERMINATE_NOT_PERFORM.getCode().equals(changeTypeEntity.getSubChangeType())) {
                consumer.accept(changeTypeEntity.getContractId(), ContractChangeTypeEnum.TERMINATE_NOT_PERFORM.getCode());
            }
            // 变更
            else {
                consumer.accept(changeTypeEntity.getContractId(), ContractChangeTypeEnum.CHANGE.getCode());
            }
        });

        return contractByChangeTypeMap;
    }

    /**
     * 更新合同“生效标识”
     */
    private boolean updateContractEffectFlag(Integer contractId) {
        return contractService.lambdaUpdate()
                .set(ContractEntity::getEffectFlag, BooleFlagEnum.YES.getCode())
                .eq(ContractEntity::getId, contractId)
                .eq(ContractEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .update();
    }

    /**
     * 更新原合同“变更标识”
     */
    private boolean updateContractChangeFlag(Integer contractId) {
        return contractService.lambdaUpdate()
                .eq(ContractEntity::getId, contractId)
                .set(ContractEntity::getChangeFlag, ContractChangeFlagEnum.EFFECT.getCode())
                .update();
    }

    /**
     * 分类处理 - 不履约终止
     */
    @LogWrapper(name = "不履约终止")
    public void handleNotPerformTermination(List<ContractEntity> notPerformTerminationAmendments, String snapshotSourceType) {
        if (CollectionUtils.isEmpty(notPerformTerminationAmendments)) {
            return;
        }

        for (ContractEntity amendment : notPerformTerminationAmendments) {
            try {
                // 同步前，保存原合同数据快照
                boolean savedSnapshot = snapshotService.saveSnapshot(new ContractSnapshotParam(
                        snapshotSourceType, amendment.getParentId(), amendment.getId(), getCurrentExecutor()));
                if (!savedSnapshot) {
                    throw new RuntimeException("[%s]原合同的快照数据保存失败！".formatted(amendment.getParentId()));
                }
                // 数据同步
                this.selfBean.updateChangedDataAll(amendment, List.of(
                        // 原合同状态更新为：已关闭
                        () -> contractService.lambdaUpdate()
                                .set(ContractEntity::getFormalStatus, ContractStatusEnum.CLOSED.getCode())
                                .eq(ContractEntity::getId, amendment.getParentId())
                                .eq(ContractEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                                .update()
                ));

                // 删除原合同台账
                try {
                    ledgerService.terminateLedger(amendment.getParentId());
                } catch (Exception e) {
                    log.error("{}不履约终止的[{}]{}已到生效日期，删除[{}]原合同台账失败！", this.logFlagHolder.get(),
                            amendment.getId(), IDENTITY_NAME, amendment.getParentId(), e);
                }

                // 记录日志
                saveContractLog(amendment.getId(), contractLog ->
                        contractLog.setContent("%s不履约终止的%s已到生效日期，原合同状态更新为已关闭，关联台账已删除。"
                                .formatted(this.logFlagHolder.get(), IDENTITY_NAME)));
            } catch (Exception e) {
                log.error("{}已到生效日期的不履约终止的[{}]{}更新失败！", this.logFlagHolder.get(), amendment.getId(), IDENTITY_NAME, e);
            }
        }
    }

    /**
     * 分类处理 - 提前终止
     */
    @LogWrapper(name = "提前终止")
    public void handleEarlyTermination(List<ContractEntity> earlyTerminationAmendments, String snapshotSourceType) {
        if (CollectionUtils.isEmpty(earlyTerminationAmendments)) {
            return;
        }

        for (ContractEntity amendment : earlyTerminationAmendments) {
            try {
                // 同步前，保存原合同数据快照
                boolean savedSnapshot = snapshotService.saveSnapshot(new ContractSnapshotParam(
                        snapshotSourceType, amendment.getParentId(), amendment.getId(), getCurrentExecutor()));
                if (!savedSnapshot) {
                    throw new RuntimeException("[%s]原合同的快照数据保存失败！".formatted(amendment.getParentId()));
                }
                // 数据同步
                this.selfBean.updateChangedDataAll(amendment, null);

                // 生成台账
                try {
                    ledgerService.createLedger(amendment.getParentId());
                } catch (Exception e) {
                    log.error("{}已到生效日期的提前终止的[{}]{}生成台账失败！", this.logFlagHolder.get(), amendment.getId(), IDENTITY_NAME, e);
                }

                // 记录日志
                saveContractLog(amendment.getId(), contractLog -> contractLog.setContent(
                        "%s提前终止的%s已到生效日期，更新生效标识成功。".formatted(this.logFlagHolder.get(), IDENTITY_NAME)));
            } catch (Exception e) {
                log.error("{}已到生效日期的提前终止的[{}]{}更新失败！", this.logFlagHolder.get(), amendment.getId(), IDENTITY_NAME, e);
            }
        }
    }

    /**
     * 分类处理 - 变更
     */
    @LogWrapper(name = "变更记录")
    public void handleChange(List<ContractEntity> changeTypeAmendments, String snapshotSourceType) {
        if (CollectionUtils.isEmpty(changeTypeAmendments)) {
            return;
        }

        for (ContractEntity amendment : changeTypeAmendments) {
            // 变更合同同步
            try {
                // 同步前，保存原合同数据快照
                boolean savedSnapshot = snapshotService.saveSnapshot(new ContractSnapshotParam(
                        snapshotSourceType, amendment.getParentId(), amendment.getId(), getCurrentExecutor()));
                if (!savedSnapshot) {
                    throw new RuntimeException("[%s]原合同的快照数据保存失败！".formatted(amendment.getParentId()));
                }

                // 数据同步
                this.selfBean.updateChangedDataAll(amendment, null);
            } catch (Exception e) {
                log.error("{}已到生效日期的变更的[{}]{}更新失败！", this.logFlagHolder.get(), amendment.getId(), IDENTITY_NAME, e);
                continue;
            }

            // 生成台账
            try {
                ledgerService.createLedger(amendment.getParentId());
            } catch (Exception e) {
                log.error("{}已到生效日期的变更的[{}]{}生成台账失败！", this.logFlagHolder.get(), amendment.getId(), IDENTITY_NAME, e);
            }

            // 记录日志
            saveContractLog(amendment.getId(), contractLog ->
                    contractLog.setContent("%s%s已到生效日期，更新原合同数据成功。".formatted(this.logFlagHolder.get(), IDENTITY_NAME)));
        }
    }

    /**
     * 更新变更的所有数据（事务）
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateChangedDataAll(ContractEntity amendment, List<Supplier<Boolean>> extraUpdateActions) {
        Integer contractId = amendment.getParentId();
        Integer amendmentId = amendment.getId();

        // 更新原合同对象
        selfBean.updateContractSelfData(contractDeepClone.copyProperties(amendment));

        // 更新合同项目对象
        Map<Integer, Integer> amendmentProjectIdMap = selfBean.updateContractProjectData(contractId, amendmentId);

        // 更新项目押金供应商对象
        selfBean.updateContractDepositSupplierData(contractId, amendmentId, amendmentProjectIdMap);

        // 更新子合同的数据
        Integer subContractId = selfBean.updateSubContractData(contractId, amendmentId, amendmentProjectIdMap);

        // 更新价格申请数据
        selfBean.updateContractPriceApplyData(contractId, amendmentId);

        // 更新设备数据
        Map<Integer, Integer> amendmentDeviceIdMap = selfBean.updateContractDeviceData(contractId, amendmentId, amendmentProjectIdMap);

        // 更新设备点位对象
        selfBean.updateContractDevicePointData(contractId, amendmentId, amendmentProjectIdMap, amendmentDeviceIdMap);

        // 更新单价周期对象
        selfBean.updateContractPricePeriodData(contractId, amendmentId, subContractId, amendmentProjectIdMap, amendmentDeviceIdMap);

        // 更新付款周期对象
        selfBean.updateContractPaymentPeriodData(contractId, amendmentId, subContractId, amendmentProjectIdMap, amendmentDeviceIdMap);

        // 更新合同供应商对象
        selfBean.updateContractSupplierData(contractId, amendmentId, subContractId, amendmentProjectIdMap);

        // 更新合同供应商银行对象
        selfBean.updateContractSupplierBankData(contractId, amendmentId, subContractId);

        // 更新变更合同的生效标识
        if (!updateContractEffectFlag(amendmentId)) {
            saveLogAndThrowException("[%s]%s更新生效标识失败！".formatted(amendmentId, IDENTITY_NAME), Level.ERROR);
        }

        // 更新原合同的变更标识
        if (!updateContractChangeFlag(contractId)) {
            saveLogAndThrowException("[%s]原合同更新变更标识失败！".formatted(contractId), Level.ERROR);
        }

        // 更新原合同下项目的签约数量
        selfBean.updateContractProjectSignCount(contractId, amendmentId, true);

        // 额外的更新操作
        if (CollectionUtils.isEmpty(extraUpdateActions)) {
            return;
        }
        for (Supplier<Boolean> extraUpdateAction : extraUpdateActions) {
            if (extraUpdateAction.get()) {
                continue;
            }
            saveLogAndThrowException("[%s]%s额外操作的更新失败！".formatted(amendmentId, IDENTITY_NAME), Level.ERROR);
        }
    }

    /**
     * 更新原合同的数据
     */
    @LogWrapper(name = "原合同数据更新")
    public void updateContractSelfData(ContractEntity amendmentContract) {
        // 设置可修改的属性
        ContractEntity entity = new ContractEntity();
        entity.setId(amendmentContract.getParentId());
        entity.setContactPerson(amendmentContract.getContactPerson());
        entity.setContactPhone(amendmentContract.getContactPhone());
        entity.setContactEmail(amendmentContract.getContactEmail());
        entity.setContactAddress(amendmentContract.getContactAddress());
        entity.setGiftFlag(amendmentContract.getGiftFlag());
        entity.setSealType(amendmentContract.getSealType());
        entity.setInvoiceType(amendmentContract.getInvoiceType());
        entity.setTaxPoint(amendmentContract.getTaxPoint());
        entity.setStartDate(amendmentContract.getStartDate());
        entity.setEndDate(amendmentContract.getEndDate());
        entity.setPeriod(amendmentContract.getPeriod());
        entity.setTotalAmount(amendmentContract.getTotalAmount());
        entity.setClaim(amendmentContract.getClaim());
        entity.setStampOrder(amendmentContract.getStampOrder());
        // 更新原合同数据
        if (!contractService.updateById(entity)) {
            saveLogAndThrowException("[%s]原合同数据更新失败！".formatted(entity.getId()), Level.ERROR);
        }
        // 涉及到日期变更时，修正原合同的状态
        if (Objects.nonNull(entity.getStartDate()) || Objects.nonNull(entity.getEndDate())) {
            updateContractFormalStatus(entity);
        }
    }

    /**
     * 涉及到日期变更时，更新原合同的状态
     */
    private void updateContractFormalStatus(ContractEntity contract) {
        LocalDate today = LocalDate.now();
        Consumer<String> updateContractStatusConsumer = (status) -> {
            boolean result = contractService.lambdaUpdate()
                    .set(ContractEntity::getFormalStatus, status)
                    .eq(ContractEntity::getId, contract.getId())
                    .in(ContractEntity::getFormalStatus,
                            Stream.of(ContractStatusEnum.WAIT_EXECUTE, ContractStatusEnum.EXECUTING, ContractStatusEnum.EXPIRED)
                                    .map(ContractStatusEnum::getCode).filter(code -> !Objects.equals(code, status)).toList())
                    .eq(ContractEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                    .update();
            if (result) {
                statusChangeLogService.recordContractStatusChange(contract.getId(), contract.getApplyCode(), status);
            }
        };
        // 当前日期 < 合同开始日期                    => 待执行
        if (Objects.nonNull(contract.getStartDate()) && today.isBefore(contract.getStartDate())) {
            updateContractStatusConsumer.accept(ContractStatusEnum.WAIT_EXECUTE.getCode());
        }
        // 合同开始日期 <= 当前日期 <= 合同结束日期     => 执行中
        else if (Objects.nonNull(contract.getStartDate()) && Objects.nonNull(contract.getEndDate())
                && !today.isBefore(contract.getStartDate()) && !today.isAfter(contract.getEndDate())) {
            updateContractStatusConsumer.accept(ContractStatusEnum.EXECUTING.getCode());
        }
        // 当前日期 > 合同结束日期                    => 已过期
        else if (Objects.nonNull(contract.getEndDate()) && today.isAfter(contract.getEndDate())) {
            updateContractStatusConsumer.accept(ContractStatusEnum.EXPIRED.getCode());
        }
    }

    /**
     * 更新合同项目对象
     */
    @LogWrapper(name = "合同项目数据更新")
    public Map<Integer, Integer> updateContractProjectData(Integer contractId, Integer amendmentId) {
        // 获取项目数据
        Map<Integer, List<ContractProjectEntity>> projectEntityMap = contractProjectService.lambdaQuery()
                .in(ContractProjectEntity::getContractId, List.of(contractId, amendmentId))
                .list()
                .stream()
                .collect(Collectors.groupingBy(ContractProjectEntity::getContractId));
        var parentProjectEntities = projectEntityMap.getOrDefault(contractId, List.of());
        if (CollectionUtils.isEmpty(parentProjectEntities)) {
            saveLogAndThrowException("[%s]合同没有关联项目，请注意核对".formatted(contractId), Level.ERROR);
        }

        //
        Map<String, ContractProjectEntity> amendmentMap = projectEntityMap.getOrDefault(amendmentId, List.of())
                .stream().collect(Collectors.toMap(ContractProjectEntity::getProjectCode, e -> e, (v1, v2) -> v1));
        Set<String> addedProjectCodes = amendmentMap.keySet();
        Map<Integer, Integer> amendmentProjectIdMap = Maps.newHashMapWithExpectedSize(amendmentMap.size());
        List<ContractProjectEntity> delProjectEntities = Lists.newArrayListWithCapacity(parentProjectEntities.size());
        for (ContractProjectEntity parentProject : parentProjectEntities) {
            ContractProjectEntity amendmentProject = amendmentMap.get(parentProject.getProjectCode());
            // 删除原合同下的项目关系
            if (Objects.isNull(amendmentProject)) {
                delProjectEntities.add(parentProject);
                continue;
            }
            // 更新原合同下的项目数据
            ContractProjectEntity entity = new ContractProjectEntity();
            entity.setId(parentProject.getId());
            entity.setPaymentType(amendmentProject.getPaymentType());
            entity.setIntervalDay(amendmentProject.getIntervalDay());
            entity.setStartDate(amendmentProject.getStartDate());
            entity.setEndDate(amendmentProject.getEndDate());
            entity.setPeriod(amendmentProject.getPeriod());
            entity.setAmount(amendmentProject.getAmount());
            entity.setDepositFlag(amendmentProject.getDepositFlag());
            entity.setDepositAmount(amendmentProject.getDepositAmount());
            entity.setDepositPaymentDate(amendmentProject.getDepositPaymentDate());
            if (!contractProjectService.updateById(entity)) {
                saveLogAndThrowException("[id=%s]合同下[id=%s]项目数据更新失败".formatted(contractId, parentProject.getId()),
                        Level.ERROR);
            }
            // 移除已经出现的项目code
            addedProjectCodes.remove(parentProject.getProjectCode());
            // 记录映射关系
            amendmentProjectIdMap.put(amendmentProject.getId(), parentProject.getId());
        }

        // 不能删除原合同下的所有子项目
        if (Objects.equals(delProjectEntities.size(), parentProjectEntities.size())) {
            saveLogAndThrowException("[id=%s]合同下所有子项目不能全部删除".formatted(contractId), Level.ERROR);
        }

        // 删除记录
        if (CollectionUtils.isNotEmpty(delProjectEntities) && !contractProjectService.removeByIds(delProjectEntities)) {
            saveLogAndThrowException("[id=%s]合同下项目数据删除失败".formatted(contractId), Level.ERROR);
        }

        // 新增记录
        if (CollectionUtils.isEmpty(addedProjectCodes)) {
            return amendmentProjectIdMap;
        }
        addedProjectCodes.forEach(projectCode -> {
            ContractProjectEntity entity = amendmentMap.get(projectCode);
            Integer amendmentProjectId = entity.getId();
            entity.setId(null);
            entity.setContractId(contractId);
            entity.setCreator(getCurrentExecutor());
            // 保存到数据库
            if (!contractProjectService.save(entity)) {
                saveLogAndThrowException("[id=%s]合同下[id=%s]项目数据保存失败".formatted(contractId, entity.getId()),
                        Level.ERROR);
            }
            amendmentProjectIdMap.put(amendmentProjectId, entity.getId());
        });

        return amendmentProjectIdMap;
    }

    /**
     * 更新项目押金供应商对象
     */
    @LogWrapper(name = "合同项目押金供应商数据更新")
    public void updateContractDepositSupplierData(Integer contractId, Integer amendmentId,
                                                  Map<Integer, Integer> amendmentProjectIdMap) {
        // 查询合同下的押金供应商数据，按照合同ID分组
        Map<Integer, List<ContractDepositSupplierEntity>> depositSupplierMap = contractDepositSupplierService.lambdaQuery()
                .in(ContractDepositSupplierEntity::getContractId, List.of(contractId, amendmentId))
                .list()
                .stream()
                .collect(Collectors.groupingBy(ContractDepositSupplierEntity::getContractId));
        if (MapUtils.isEmpty(depositSupplierMap)) {
            return;
        }

        var parentEntities = depositSupplierMap.getOrDefault(contractId, List.of());
        Map<Integer, ContractDepositSupplierEntity> amendmentByProjectMap = depositSupplierMap.getOrDefault(amendmentId, List.of())
                .stream().collect(Collectors.toMap(e -> amendmentProjectIdMap.getOrDefault(e.getProjectId(), 0), e -> e, (v1, v2) -> v1));
        var delDepositSupplierIds = new HashSet<Integer>(parentEntities.size());
        for (ContractDepositSupplierEntity parentEntity : parentEntities) {
            var amendmentEntity = amendmentByProjectMap.get(parentEntity.getProjectId());
            // 保存待删除的记录
            if (Objects.isNull(amendmentEntity)) {
                delDepositSupplierIds.add(parentEntity.getId());
                continue;
            }
            // 记录更新
            ContractDepositSupplierEntity entity = new ContractDepositSupplierEntity();
            entity.setId(parentEntity.getId());
            entity.setSupplierId(amendmentEntity.getSupplierId());
            entity.setSupplierCode(amendmentEntity.getSupplierCode());
            entity.setSupplierName(amendmentEntity.getSupplierName());
            entity.setAccountNo(amendmentEntity.getAccountNo());
            entity.setAccountName(amendmentEntity.getAccountName());
            entity.setBankCode(amendmentEntity.getBankCode());
            entity.setBankName(amendmentEntity.getBankName());
            entity.setSupplierBankId(amendmentEntity.getSupplierBankId());
            if (!contractDepositSupplierService.updateById(entity)) {
                saveLogAndThrowException("[id=%s]合同下项目押金供应商数据更新失败".formatted(contractId), Level.ERROR);
            }
            // 移除
            amendmentByProjectMap.remove(parentEntity.getProjectId());
        }

        // 记录删除
        if (CollectionUtils.isNotEmpty(delDepositSupplierIds) && !contractDepositSupplierService.removeByIds(delDepositSupplierIds)) {
            saveLogAndThrowException("[id=%s]合同下押金供应商数据删除失败".formatted(contractId), Level.ERROR);
        }

        // 记录新增
        List<ContractDepositSupplierEntity> addDepositSuppliers = Lists.newArrayListWithExpectedSize(amendmentByProjectMap.size());
        amendmentByProjectMap.forEach((projectId, entity) -> {
            if (0 == projectId) {
                return;
            }
            // 记录新增
            entity.setId(null);
            entity.setContractId(contractId);
            entity.setProjectId(projectId);
            addDepositSuppliers.add(entity);
        });
        if (CollectionUtils.isNotEmpty(addDepositSuppliers) && !contractDepositSupplierService.saveBatch(addDepositSuppliers)) {
            saveLogAndThrowException("[id=%s]合同下押金供应商数据更新失败".formatted(contractId), Level.ERROR);
        }
    }

    /**
     * 更新子合同的数据
     */
    @LogWrapper(name = "子合同数据更新")
    public Integer updateSubContractData(Integer contractId, Integer amendmentId,
                                         Map<Integer, Integer> amendmentProjectIdMap) {
        // 获取项目下的子合同数据
        Map<Integer, List<ContractSubEntity>> subContractMap = contractSubService.lambdaQuery()
                .in(ContractSubEntity::getContractId, List.of(contractId, amendmentId))
                .list()
                .stream()
                .collect(Collectors.groupingBy(ContractSubEntity::getContractId));
        if (MapUtils.isEmpty(subContractMap)) {
            return 0;
        }

        // 删除子合同
        List<ContractSubEntity> parentSubContractEntities = subContractMap.getOrDefault(contractId, List.of());
        List<ContractSubEntity> amendmentSubContractEntities = subContractMap.getOrDefault(amendmentId, List.of());
        if (CollectionUtils.isEmpty(amendmentSubContractEntities)) {
            if (CollectionUtils.isNotEmpty(parentSubContractEntities) && !contractSubService.removeByIds(parentSubContractEntities)) {
                saveLogAndThrowException("[id=%s]合同下的子合同数据移除失败".formatted(contractId), Level.ERROR);
            }
            return 0;
        }

        // 创建新子合同
        ContractSubEntity amendmentSubContract = amendmentSubContractEntities.get(0);
        if (CollectionUtils.isEmpty(parentSubContractEntities)) {
            ContractSubEntity entity = contractDeepClone.copyProperties(amendmentSubContract);
            entity.setId(null);
            entity.setContractId(contractId);
            entity.setProjectId(amendmentProjectIdMap.getOrDefault(amendmentSubContract.getProjectId(), 0));
            if (contractSubService.save(entity)) {
                return entity.getId();
            }
            saveLogAndThrowException("[id=%s]合同下的子合同数据创建失败".formatted(contractId), Level.ERROR);
        }

        // 更新子合同数据
        ContractSubEntity entity = new ContractSubEntity();
        entity.setId(parentSubContractEntities.get(0).getId());
        entity.setContactPerson(amendmentSubContract.getContactPerson());
        entity.setContactPhone(amendmentSubContract.getContactPhone());
        entity.setContactEmail(amendmentSubContract.getContactEmail());
        entity.setContactAddress(amendmentSubContract.getContactAddress());
        entity.setPaymentType(amendmentSubContract.getPaymentType());
        entity.setIntervalDay(amendmentSubContract.getIntervalDay());
        entity.setAmount(amendmentSubContract.getAmount());
        entity.setInvoiceType(amendmentSubContract.getInvoiceType());
        entity.setTaxPoint(amendmentSubContract.getTaxPoint());
        if (!contractSubService.updateById(entity)) {
            saveLogAndThrowException("[id=%s]合同下的子合同数据更新失败".formatted(contractId), Level.ERROR);
        }

        return entity.getId();
    }

    /**
     * 更新价格申请对象
     */
    @LogWrapper(name = "价格申请数据更新")
    public void updateContractPriceApplyData(Integer contractId, Integer amendmentId) {
        // 获取价格申请数据
        Map<Integer, List<ContractPriceApplyEntity>> priceApplyEntityMap = contractPriceApplyService.lambdaQuery()
                .in(ContractPriceApplyEntity::getContractId, List.of(contractId, amendmentId))
                .list().stream()
                .collect(Collectors.groupingBy(ContractPriceApplyEntity::getContractId));
        if (MapUtils.isEmpty(priceApplyEntityMap)) {
            return;
        }
        // 更新价格申请数据
        List<ContractPriceApplyEntity> parentEntities = priceApplyEntityMap.getOrDefault(contractId, List.of());
        Map<String, ContractPriceApplyEntity> amendmentMap = priceApplyEntityMap.getOrDefault(amendmentId, List.of()).stream()
                .collect(Collectors.toMap(ContractPriceApplyEntity::getApplyCode, e -> e, (v1, v2) -> v1));
        List<ContractPriceApplyEntity> updateEntities = Lists.newArrayListWithExpectedSize(parentEntities.size());
        List<ContractPriceApplyEntity> delEntities = Lists.newArrayListWithExpectedSize(parentEntities.size());
        for (ContractPriceApplyEntity parentEntity : parentEntities) {
            var amendmentEntity = amendmentMap.get(parentEntity.getApplyCode());
            if (Objects.isNull(amendmentEntity)) {
                delEntities.add(parentEntity);
                continue;
            }
            var entity = new ContractPriceApplyEntity();
            entity.setId(parentEntity.getId());
            entity.setDeviceCount(amendmentEntity.getDeviceCount());
            entity.setAmount(amendmentEntity.getAmount());
            updateEntities.add(entity);
        }
        // 删除记录
        if (CollectionUtils.isNotEmpty(delEntities) && !contractPriceApplyService.removeBatchByIds(delEntities)) {
            saveLogAndThrowException("[id=%s]合同下的价格申请数据移除失败".formatted(contractId), Level.ERROR);
        }
        // 更新记录
        if (CollectionUtils.isEmpty(updateEntities) || !contractPriceApplyService.updateBatchById(updateEntities)) {
            saveLogAndThrowException("[id=%s]合同下的价格申请数据更新失败".formatted(contractId), Level.ERROR);
        }
    }

    /**
     * 更新设备对象
     */
    @LogWrapper(name = "设备数据更新")
    public Map<Integer, Integer> updateContractDeviceData(Integer contractId, Integer amendmentId,
                                                          Map<Integer, Integer> amendmentProjectIdMap) {
        // 获取设备数据
        Map<Integer, Map<Integer, List<ContractDeviceEntity>>> deviceEntityMap = contractDeviceService.lambdaQuery()
                .in(ContractDeviceEntity::getContractId, List.of(contractId, amendmentId))
                .list()
                .stream()
                .collect(Collectors.groupingBy(ContractDeviceEntity::getContractId,
                        Collectors.groupingBy(e -> Objects.equals(e.getContractId(), amendmentId)
                                        ? amendmentProjectIdMap.getOrDefault(e.getProjectId(), 0)
                                        : e.getProjectId(),
                                Collectors.toList())));
        if (MapUtils.isEmpty(deviceEntityMap)) {
            return Collections.emptyMap();
        }
        Map<Integer, List<ContractDeviceEntity>> parentByProjectMap = deviceEntityMap.get(contractId);
        if (MapUtils.isEmpty(parentByProjectMap)) {
            return Collections.emptyMap();
        }
        Map<Integer, List<ContractDeviceEntity>> amendmentByProjectMap = deviceEntityMap.getOrDefault(amendmentId, Map.of());
        if (MapUtils.isEmpty(amendmentByProjectMap)) {
            return Collections.emptyMap();
        }

        //
        Map<Integer, Integer> amendmentDeviceIdMap = new HashMap<>();
        List<ContractDeviceEntity> updateEntities = new ArrayList<>();
        List<ContractDeviceEntity> delEntities = new ArrayList<>();
        parentByProjectMap.forEach((pPid, parentEntities) -> {
            var amendmentEntities = amendmentByProjectMap.get(pPid);
            if (CollectionUtils.isEmpty(amendmentEntities)) {
                delEntities.addAll(parentEntities);
                return;
            }
            //
            for (int i = 0; i < parentEntities.size(); i++) {
                if (i >= amendmentEntities.size()) {
                    continue;
                }
                var parentEntity = parentEntities.get(i);
                var amendmentEntity = amendmentEntities.get(i);
                var entity = new ContractDeviceEntity();
                entity.setId(parentEntity.getId());
                entity.setSignCount(amendmentEntity.getSignCount());
                updateEntities.add(entity);
                amendmentDeviceIdMap.put(amendmentEntity.getId(), parentEntity.getId());
            }

        });
        // 删除记录
        if (CollectionUtils.isNotEmpty(delEntities) && !contractDeviceService.removeBatchByIds(delEntities)) {
            saveLogAndThrowException("[id=%s]合同下设备数据移除失败".formatted(contractId), Level.ERROR);
        }
        // 更新记录
        if (CollectionUtils.isEmpty(updateEntities) || !contractDeviceService.updateBatchById(updateEntities)) {
            saveLogAndThrowException("[id=%s]合同下的设备数据更新失败".formatted(contractId), Level.ERROR);
        }

        return amendmentDeviceIdMap;
    }

    /**
     * 更新设备点位对象
     */
    @LogWrapper(name = "设备点位数据更新")
    public void updateContractDevicePointData(Integer contractId, Integer amendmentId,
                                              Map<Integer, Integer> amendmentProjectIdMap,
                                              Map<Integer, Integer> amendmentDeviceIdMap) {
        // 删除原合同项目下的设备点位数据
        if (!contractDevicePointService.lambdaUpdate().eq(ContractDevicePointEntity::getContractId, contractId).remove()) {
            saveLogAndThrowException("[id=%s]合同下设备点位数据删除失败".formatted(contractId), Level.ERROR);
        }

        // 新增原合同项目下的设备点位数据
        List<ContractDevicePointEntity> addDevicePoints = contractDevicePointService.lambdaQuery()
                .eq(ContractDevicePointEntity::getContractId, amendmentId)
                .list()
                .stream()
                .peek(e -> {
                    e.setId(null);
                    e.setContractId(contractId);
                    e.setProjectId(amendmentProjectIdMap.getOrDefault(e.getProjectId(), 0));
                    e.setDeviceId(amendmentDeviceIdMap.getOrDefault(e.getDeviceId(), 0));
                }).toList();
        if (CollectionUtils.isNotEmpty(addDevicePoints) && !contractDevicePointService.saveBatch(addDevicePoints)) {
            saveLogAndThrowException("[id=%s]合同下设备点位数据更新失败".formatted(contractId), Level.ERROR);
        }
    }

    /**
     * 更新单价周期对象
     */
    @LogWrapper(name = "单价周期数据更新")
    public void updateContractPricePeriodData(Integer contractId, Integer amendmentId, Integer subContractId,
                                              Map<Integer, Integer> amendmentProjectIdMap,
                                              Map<Integer, Integer> amendmentDeviceIdMap) {
        // 删除原合同项目下的设备价格周期数据
        if (!contractPricePeriodService.lambdaUpdate().eq(ContractPricePeriodEntity::getContractId, contractId).remove()) {
            saveLogAndThrowException("[id=%s]合同下设备价格周期数据删除失败".formatted(contractId), Level.ERROR);
        }

        // 新增原合同项目下的设备价格周期数据
        List<ContractPricePeriodEntity> addPricePeriods = contractPricePeriodService.lambdaQuery()
                .eq(ContractPricePeriodEntity::getContractId, amendmentId)
                .list()
                .stream()
                .peek(e -> {
                    e.setId(null);
                    e.setContractId(contractId);
                    e.setProjectId(amendmentProjectIdMap.getOrDefault(e.getProjectId(), 0));
                    // 特殊处理子合同
                    if (e.getSubContractId() > 0) {
                        e.setSubContractId(subContractId);
                        e.setDeviceId(0);
                    } else {
                        e.setDeviceId(amendmentDeviceIdMap.getOrDefault(e.getDeviceId(), 0));
                    }
                }).toList();
        if (CollectionUtils.isNotEmpty(addPricePeriods) && !contractPricePeriodService.saveBatch(addPricePeriods)) {
            saveLogAndThrowException("[id=%s]合同下设备价格周期数据更新失败".formatted(contractId), Level.ERROR);
        }
    }

    /**
     * 更新付款周期对象
     */
    @LogWrapper(name = "付款周期数据更新")
    public void updateContractPaymentPeriodData(Integer contractId, Integer amendmentId, Integer subContractId,
                                                Map<Integer, Integer> amendmentProjectIdMap,
                                                Map<Integer, Integer> amendmentDeviceIdMap) {
        // 获取所有的付款周期记录，按合同ID进行分组
        Map<Integer, List<ContractPaymentPeriodEntity>> paymentPeriodMap = contractPaymentPeriodService.lambdaQuery()
                .in(ContractPaymentPeriodEntity::getContractId, List.of(contractId, amendmentId))
                .list()
                .stream()
                .collect(Collectors.groupingBy(ContractPaymentPeriodEntity::getContractId));
        if (MapUtils.isEmpty(paymentPeriodMap)) {
            return;
        }

        List<ContractPaymentPeriodEntity> parentEntities = paymentPeriodMap.getOrDefault(contractId, List.of());
        List<ContractPaymentPeriodEntity> amendmentEntities = paymentPeriodMap.getOrDefault(amendmentId, List.of());
        Map<Integer, ContractPaymentPeriodEntity> addedAmendmentMap = new HashMap<>(amendmentEntities.size());
        Map<Integer, ContractPaymentPeriodEntity> amendmentByParentMap = new HashMap<>(amendmentEntities.size());
        amendmentEntities.forEach(e -> {
            addedAmendmentMap.put(e.getId(), e);
            if (!Objects.equals(e.getParentId(), 0)) {
                amendmentByParentMap.put(e.getParentId(), e);
            }
        });
        List<ContractPaymentPeriodEntity> delPaymentPeriods = Lists.newArrayListWithExpectedSize(parentEntities.size());
        for (ContractPaymentPeriodEntity parentEntity : parentEntities) {
            var amendmentEntity = amendmentByParentMap.get(parentEntity.getId());
            // 保存待删除的记录
            if (Objects.isNull(amendmentEntity)) {
                delPaymentPeriods.add(parentEntity);
                continue;
            }
            // 记录更新
            ContractPaymentPeriodEntity entity = new ContractPaymentPeriodEntity();
            entity.setId(parentEntity.getId());
            entity.setFeeType(amendmentEntity.getFeeType());
            entity.setStartDate(amendmentEntity.getStartDate());
            entity.setEndDate(amendmentEntity.getEndDate());
            entity.setSupplierId(amendmentEntity.getSupplierId());
            entity.setSupplierCode(amendmentEntity.getSupplierCode());
            entity.setSupplierName(amendmentEntity.getSupplierName());
            entity.setAmount(amendmentEntity.getAmount());
            entity.setPlanPaymentDate(amendmentEntity.getPlanPaymentDate());
            entity.setAccountNo(amendmentEntity.getAccountNo());
            entity.setAccountName(amendmentEntity.getAccountName());
            entity.setBankCode(amendmentEntity.getBankCode());
            entity.setBankName(amendmentEntity.getBankName());
            entity.setRefundAmount(amendmentEntity.getRefundAmount());
            if (!contractPaymentPeriodService.updateById(entity)) {
                saveLogAndThrowException("[id=%s]合同下付款周期数据更新失败".formatted(contractId), Level.ERROR);
            }
            // 移除已经出现原付款周期记录ID
            addedAmendmentMap.remove(amendmentEntity.getId());
        }

        // 记录删除
        if (CollectionUtils.isNotEmpty(delPaymentPeriods) && !contractPaymentPeriodService.removeBatchByIds(delPaymentPeriods)) {
            saveLogAndThrowException("[id=%s]合同下付款周期数据删除失败".formatted(contractId), Level.ERROR);
        }

        // 记录新增
        List<ContractPaymentPeriodEntity> addPaymentPeriods = Lists.newArrayListWithExpectedSize(addedAmendmentMap.size());
        addedAmendmentMap.forEach((k, e) -> {
            e.setId(null);
            e.setContractId(contractId);
            e.setProjectId(amendmentProjectIdMap.getOrDefault(e.getProjectId(), 0));
            // 特殊处理子合同
            if (e.getSubContractId() > 0) {
                e.setSubContractId(subContractId);
                e.setDeviceId(0);
            } else {
                e.setDeviceId(amendmentDeviceIdMap.getOrDefault(e.getDeviceId(), 0));
            }
            addPaymentPeriods.add(e);
        });
        if (CollectionUtils.isNotEmpty(addPaymentPeriods) && !contractPaymentPeriodService.saveBatch(addPaymentPeriods)) {
            saveLogAndThrowException("[id=%s]合同下付款周期数据更新失败".formatted(contractId), Level.ERROR);
        }
    }

    /**
     * 更新合同供应商对象
     */
    @LogWrapper(name = "合同供应商数据更新")
    public void updateContractSupplierData(Integer contractId, Integer amendmentId, Integer subContractId,
                                           Map<Integer, Integer> amendmentProjectIdMap) {
        // 删除原合同下的供应商关系
        if (!contractSupplierService.lambdaUpdate().eq(ContractSupplierEntity::getContractId, contractId).remove()) {
            saveLogAndThrowException("[id=%s]合同下供应商关系删除失败".formatted(contractId), Level.ERROR);
        }

        // 新增原合同下的供应商关系
        List<ContractSupplierEntity> supplierEntities = contractSupplierService.lambdaQuery()
                .eq(ContractSupplierEntity::getContractId, amendmentId)
                .list()
                .stream()
                .peek(e -> {
                    e.setId(null);
                    e.setContractId(contractId);
                    // 特殊处理子合同
                    if (e.getSubContractId() > 0) {
                        e.setSubContractId(subContractId);
                        e.setProjectId(amendmentProjectIdMap.getOrDefault(e.getProjectId(), 0));
                    }
                })
                .toList();
        if (CollectionUtils.isNotEmpty(supplierEntities) && !contractSupplierService.saveBatch(supplierEntities)) {
            saveLogAndThrowException("[id=%s]合同下供应商关系更新失败".formatted(contractId), Level.ERROR);
        }
    }

    /**
     * 更新合同供应商银行对象
     */
    @LogWrapper(name = "合同供应商银行数据更新")
    public void updateContractSupplierBankData(Integer contractId, Integer amendmentId, Integer subContractId) {
        // 删除供应商银行信息
        if (!contractSupplierBankService.lambdaUpdate().eq(ContractSupplierBankEntity::getContractId, contractId).remove()) {
            saveLogAndThrowException("[id=%s]合同下设备供应商银行数据删除失败".formatted(contractId), Level.ERROR);
        }

        // 新增供应商银行信息
        List<ContractSupplierBankEntity> addSupplierBanks = contractSupplierBankService.lambdaQuery()
                .eq(ContractSupplierBankEntity::getContractId, amendmentId)
                .list()
                .stream()
                .peek(e -> {
                    e.setId(null);
                    e.setContractId(contractId);
                    // 特殊处理子合同
                    if (e.getSubContractId() > 0) {
                        e.setSubContractId(subContractId);
                    }
                }).toList();
        if (CollectionUtils.isNotEmpty(addSupplierBanks) && !contractSupplierBankService.saveBatch(addSupplierBanks)) {
            saveLogAndThrowException("[id=%s]合同下设备供应商银行数据更新失败".formatted(contractId), Level.ERROR);
        }
    }

    /**
     * 更新合同下项目的签约数量
     */
    @LogWrapper(name = "合同下项目签约数量更新")
    public void updateContractProjectSignCount(Integer contractId, Integer amendmentId, boolean synced) {
        // 验证补充协议是否已生效
        if (!synced) {
            ContractEntity amendment = contractService.getById(amendmentId);
            if (Objects.isNull(amendment) || !BooleFlagEnum.YES.getCode().equals(amendment.getEffectFlag())) {
                saveLogAndThrowException("[id=%s]%s未生效，无法同步数据".formatted(amendmentId, IDENTITY_NAME), Level.ERROR);
            }
        }
        // 更新合同下项目的签约数量
        if (!contractProjectService.syncContractSignNumToProject(List.of(contractId))) {
            saveLogAndThrowException("[id=%s]合同下项目的签约数量同步失败".formatted(contractId), Level.ERROR);
        }
    }

    /**
     * 提供变更外部调用的处理方法
     */
    @LogWrapper(name = "外部调用合同变更数据同步操作")
    public void handleAmendment(String changeType, List<ContractEntity> amendmentEntities, Integer userId) {
        if (CollectionUtils.isEmpty(amendmentEntities)) {
            return;
        }
        ContractChangeTypeEnum changeTypeEnum = ContractChangeTypeEnum.parse(changeType);
        if (Objects.isNull(changeTypeEnum)) {
            saveLogAndThrowException("[changeType=%s]%s变更类型解析失败".formatted(changeType, IDENTITY_NAME), Level.ERROR);
        }
        UserThreadLocal.setUser(new CachedUser().setId(userId));
        this.logFlagHolder.set("");
        //
        switch (changeTypeEnum) {
            // 不履约终止
            case TERMINATE_NOT_PERFORM:
                selfBean.handleNotPerformTermination(amendmentEntities, SnapshotSourceTypeEnum.ARCHIVE.getCode());
                break;
            // 提前终止
            case TERMINATE_EARLY:
                selfBean.handleEarlyTermination(amendmentEntities, SnapshotSourceTypeEnum.ARCHIVE.getCode());
                break;
            // 变更 - 默认
            default:
                selfBean.handleChange(amendmentEntities, SnapshotSourceTypeEnum.ARCHIVE.getCode());
                break;
        }
    }

    /**
     * 保存日志
     */
    private void saveContractLog(Integer contractId, Consumer<ContractLogEntity> consumer) {
        ContractLogEntity contractLog = new ContractLogEntity();
        contractLog.setContractId(contractId);
        contractLog.setCreator(getCurrentExecutor());
        consumer.accept(contractLog);
        contractLogService.save(contractLog);
    }

    /**
     * 记录日志并抛出异常
     */
    private void saveLogAndThrowException(String msg, Level level) {
        log.atLevel(level).setMessage(msg).log();
        throw new RuntimeException(msg);
    }
}
