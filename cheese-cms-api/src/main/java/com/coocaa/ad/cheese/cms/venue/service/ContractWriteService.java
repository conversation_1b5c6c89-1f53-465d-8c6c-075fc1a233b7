package com.coocaa.ad.cheese.cms.venue.service;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractAmendmentChangeTypeEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractAmendmentTerminationEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractAttachmentEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractDepositSupplierEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractDeviceEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractDevicePointEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractLogEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractPaymentPeriodEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractPriceApplyEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractPricePeriodEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractProjectEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractSubEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractSupplierBankEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractSupplierEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.IFillingSupplierAndBankBoth;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.SupplierBankEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.SupplierEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IAgentService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractAmendmentChangeTypeService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractAmendmentTerminationService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractAttachmentService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractDepositSupplierService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractDevicePointService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractDeviceService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractLogService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractPaymentPeriodService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractPriceApplyService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractPricePeriodService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractProjectService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractSubService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractSupplierBankService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractSupplierService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.impl.ContractDevicePointServiceImpl;
import com.coocaa.ad.cheese.cms.common.db.venue.service.impl.ContractDeviceServiceImpl;
import com.coocaa.ad.cheese.cms.common.db.venue.service.impl.ContractPaymentPeriodServiceImpl;
import com.coocaa.ad.cheese.cms.common.db.venue.service.impl.ContractPriceApplyServiceImpl;
import com.coocaa.ad.cheese.cms.common.db.venue.service.impl.ContractPricePeriodServiceImpl;
import com.coocaa.ad.cheese.cms.common.db.venue.service.impl.ContractSupplierBankServiceImpl;
import com.coocaa.ad.cheese.cms.common.db.venue.service.impl.ContractSupplierServiceImpl;
import com.coocaa.ad.cheese.cms.common.db.venue.service.supplier.ISupplierBankService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.supplier.ISupplierService;
import com.coocaa.ad.cheese.cms.common.rpc.FeignAuthorityRpc;
import com.coocaa.ad.cheese.cms.common.tools.common.bean.common.CityDetailVO;
import com.coocaa.ad.cheese.cms.common.tools.common.enums.BooleFlagEnum;
import com.coocaa.ad.cheese.cms.common.tools.common.exception.CommonException;
import com.coocaa.ad.cheese.cms.common.tools.common.result.ResultTemplate;
import com.coocaa.ad.cheese.cms.common.tools.common.util.AesUtils;
import com.coocaa.ad.cheese.cms.common.tools.common.util.BigDecimalUtils;
import com.coocaa.ad.cheese.cms.common.tools.common.util.SecurityUtils;
import com.coocaa.ad.cheese.cms.common.tools.common.util.StringUtils;
import com.coocaa.ad.cheese.cms.common.tools.common.util.TransactionUtils;
import com.coocaa.ad.cheese.cms.common.tools.common.util.operatelog.ChangeExtractor;
import com.coocaa.ad.cheese.cms.common.tools.common.util.operatelog.ExpandChangedItem;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.AttachmentTypeEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.BusinessTypeEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractApplyStatusEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractChangeFlagEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractHistoryTypeEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractPartyEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractSnapshotSourceTypeEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractStatusEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractTypeEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ProjectLevelEnum;
import com.coocaa.ad.cheese.cms.common.util.converter.ConverterFactory;
import com.coocaa.ad.cheese.cms.dataimport.pojo.BasicInfoAllVO;
import com.coocaa.ad.cheese.cms.dataimport.pojo.BasicInfoMetaAllParam;
import com.coocaa.ad.cheese.cms.dataimport.service.FeignCrmClient;
import com.coocaa.ad.cheese.cms.venue.bean.contract.BatchContractTransferParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractAgreementAddParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractAmendmentApplyParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractAmendmentTerminationParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractApplyParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractArchiveParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractAttachmentParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractAuditParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractCancelParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractDeviceParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractDevicePointParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractEditParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractPaymentPeriodParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractPriceApplyParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractPricePeriodParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractProjectParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractRejectParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractSealParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractSnapshotParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractSupplierParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractTransferParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.SubContractFeeParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.SubContractParam;
import com.coocaa.ad.cheese.cms.venue.convert.contract.ContractConvert;
import com.coocaa.ad.cheese.cms.venue.rpc.FeignMethH5Rpc;
import com.coocaa.ad.cheese.cms.venue.rpc.FeignMethWebRpc;
import com.coocaa.ad.cheese.cms.venue.util.MehtUtils;
import com.coocaa.ad.cheese.cms.venue.vo.building.BuildingRatingDTO;
import com.coocaa.ad.cheese.cms.venue.vo.building.CmsResult;
import com.coocaa.ad.cheese.cms.venue.vo.building.PointDto;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.user.SysUserApproveVO;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.ad.common.user.bean.CachedUser;
import com.coocaa.ad.common.user.enums.UserTypeEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import jakarta.validation.constraints.Min;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.IntSummaryStatistics;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 合同保存管理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-04
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ContractWriteService extends BaseContractService {
    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    public static final DateTimeFormatter YEAR_MONTH_FORMATTER = DateTimeFormatter.ofPattern("yyMM");

    private final ContractConvert contractConvert;
    private final StringRedisTemplate stringRedisTemplate;
    private final FeignMethWebRpc feignMethWebRpc;
    private final FeignAuthorityRpc feignAuthorityRpc;
    private final IContractService contractService;
    private final IContractSupplierService contractSupplierService;
    private final IContractSupplierBankService contractSupplierBankService;
    private final IContractProjectService projectService;
    private final IContractPriceApplyService priceApplyService;
    private final IContractDeviceService deviceService;
    private final IContractDevicePointService devicePointService;
    private final IContractPricePeriodService pricePeriodService;
    private final IContractPaymentPeriodService paymentPeriodService;
    private final IContractDepositSupplierService depositSupplierService;
    private final IContractAttachmentService attachmentService;
    private final IContractSubService contractSubService;
    private final IContractLogService contractLogService;
    private final ContractApprovalService contractApprovalService;
    private final ContractNotifyService contractNotifyService;
    private final ContractTemplateService contractTemplateService;
    private final StatusChangeLogService statusChangeLogService;
    private final LedgerService ledgerService;
    private final IAgentService agentService;
    private final FeignCrmClient feignCrmClient;
    private final ConverterFactory converterFactory;
    private final ThreadPoolExecutor threadPoolExecutor;
    private final ContractReadService contractReadService;
    private final IContractAmendmentChangeTypeService contractAmendmentChangeTypeService;
    private final ContractSnapshotService contractSnapshotService;
    private final IContractAmendmentTerminationService contractAmendmentTerminationService;
    private final TransactionUtils transactionUtils;
    private final ISupplierService supplierService;
    private final ISupplierBankService supplierBankService;

    @Value("${contract.agent.approval.default.user:AGT0002}")
    private String agentApprovalDefaultUser;

    @Autowired
    private ContractWriteService selfBean;
    @Autowired
    private ContractPriceApplyServiceImpl contractPriceApplyServiceImpl;
    @Autowired
    private ContractDeviceServiceImpl contractDeviceServiceImpl;
    @Autowired
    private ContractDevicePointServiceImpl contractDevicePointServiceImpl;
    @Autowired
    private ContractPricePeriodServiceImpl contractPricePeriodServiceImpl;
    @Autowired
    private ContractPaymentPeriodServiceImpl contractPaymentPeriodServiceImpl;
    @Autowired
    private ContractSupplierServiceImpl contractSupplierServiceImpl;
    @Autowired
    private ContractSupplierBankServiceImpl contractSupplierBankServiceImpl;
    @Autowired
    private FeignMethH5Rpc feignMethH5Rpc;
    @Autowired
    private MehtUtils mehtUtils;


    /**
     * 创建或更新合同
     *
     * @param submit     是否提交
     * @param applyParam 申请合同的参数
     * @return 合同ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer createOrUpdateContract(boolean submit, boolean force, ContractApplyParam applyParam) {
        CachedUser cachedUser = Optional.ofNullable(UserThreadLocal.getUser()).orElse(new CachedUser());
        UserTypeEnum userType = Objects.isNull(cachedUser.getType()) ? UserTypeEnum.INNER : cachedUser.getType();

        if (force) {
            // 操作日志
            String content = submit ? "再发起合同申请提交" : "再发起合同申请提交草稿";
            String finalContent = cachedUser.getName() + content;
            saveContractLog(applyParam.getId(), contractLog -> contractLog.setContent(finalContent));
        }

        // 0. 转换合同信息
        ContractEntity contract = toContract(submit, applyParam, userType);
        log.info("合同申请单({}), 合同类型:{}, 提交人信息:{}", contract.getApplyCode(),
                BusinessTypeEnum.getDesc(contract.getBusinessType()), cachedUser);

        if (BooleFlagEnum.isYes(contract.getIntentionalDepositFlag()) &&
                (Objects.isNull(contract.getIntentionalDeposit()) ||
                        BigDecimalUtils.eq(contract.getIntentionalDeposit(), BigDecimal.ZERO))) {
            throw new CommonException("有意向金的时候意向金需要大于0");
        }

        // 获取代理商审核人
        if (Objects.equals(contract.getBusinessType(), BusinessTypeEnum.AGENT.getCode())
                && Objects.equals(userType, UserTypeEnum.AGENT)
                && StringUtils.isNotBlank(cachedUser.getWno())) {

            SysUserApproveVO approveUser = Optional.ofNullable(feignMethWebRpc.getUserDetail(cachedUser.getWno()))
                    .map(ResultTemplate::getData).orElse(null);
            log.info("代理合同申请单({}), 审批人信息:{}", contract.getApplyCode(), approveUser);
            contract.setAgentApprover(Objects.isNull(approveUser) ? agentApprovalDefaultUser : approveUser.getContractApplyCode());
        }

        // 设置applyTime
        LocalDateTime applyTime = Optional.ofNullable(contractService.getOne(new LambdaQueryWrapper<ContractEntity>()
                        .eq(ContractEntity::getApplyCode, contract.getApplyCode())
                        .ne(ContractEntity::getApplyTime, LocalDateTime.of(1900, 1, 1, 0, 0, 0))
                        .eq(ContractEntity::getDeleteFlag, 0)
                        .select(ContractEntity::getApplyTime))).map(ContractEntity::getApplyTime)
                .orElse(null);

        // 填充代理商名称
        if (Objects.nonNull(applyParam.getAgentId())) {
            Optional.ofNullable(agentService.getById(applyParam.getAgentId()))
                    .ifPresent(agent -> contract.setAgentName(agent.getAgentName()));
        }

        setProjectAiLevel(applyParam);

        setContractLevel(applyParam, contract);

        boolean isResubmit = checkNeedResubmit(contract);

        boolean result = Objects.isNull(contract.getId()) ? contractService.save(contract) : contractService.updateById(contract);

        if (!result || Objects.isNull(contract.getId())) {
            throw new CommonException("保存合同基本信息失败");
        }

        // 2. 先删除所有嵌套信息
        result = deleteAllExtend(contract);

        // 3. 保存供应商 & 银行信息
        result &= saveSuppliers(contract, 0, 0, applyParam.getSuppliers());

        // 4. 保存项目信息 & 级联信息 (价格申请、设备信息、设备位置、设备价格、设备支付)
        if (submit) {
            for (ContractProjectParam project : applyParam.getProjects()) {
                CmsResult<String> topLevel = feignMethH5Rpc.topLevel(project.getProjectCode());
                project.setTopLevel(topLevel.getData());
            }

        }
        result &= saveProjects(contract, applyParam.getProjects());

        // 保存附件
        result &= saveContractAttachments(contract, applyParam.getAttachments());

        // 提交审批，通知其它系统
        if (result && submit) {
            submitContract(contract.getId(), Objects.equals(userType, UserTypeEnum.INNER), isResubmit);

            if (Objects.isNull(applyTime)) {
                contract.setApplyTime(LocalDateTime.now());
            }
            String applyStatus = contract.getApplyStatus();
            if (StringUtils.equals(applyStatus, ContractApplyStatusEnum.RETURN.getCode())) {
                contract.setApplyStatus(ContractApplyStatusEnum.SUBMIT.getCode());
            }
            contractService.updateById(contract);
            log.info("合同提交审批成功, ID:{}", contract.getId());

            // 保存快照
            ContractSnapshotParam contractSnapshotParam = new ContractSnapshotParam();
            contractSnapshotParam.setSourceType(ContractSnapshotSourceTypeEnum.INITIATE_AGAIN.getCode());
            contractSnapshotParam.setContractId(contract.getId());
            contractSnapshotService.saveSnapshot(contractSnapshotParam);
        }

        // 返回合同ID
        if (result) {
            return contract.getId();
        }

        // 抛出异常, 回滚数据
        throw new CommonException("保存合同信息失败");
    }

    /**
     * 补齐项目的ai评级
     *
     * @param applyParam
     */
    private void setProjectAiLevel(ContractApplyParam applyParam) {
        if (CollectionUtils.isEmpty(applyParam.getProjects())) {
            return;
        }
        for (ContractProjectParam project : applyParam.getProjects()) {
            setProjectAiLevel(project);
        }
    }

    private void setProjectAiLevel(ContractProjectParam project) {
        if (StringUtils.isBlank(project.getProjectCode())) {
            return;
        }
        try {
            ResultTemplate<BuildingRatingDTO> retData = feignCrmClient.basicBuildMeta(project.getProjectCode()
                    .split("-")[0]);
            if (!String.valueOf(HttpStatus.OK.value()).equals(retData.getCode())) {
                log.error("feignCrmClient.basicBuildMeta 返回 : {}", JSON.toJSONString(retData));
                return;
            }
            BuildingRatingDTO data = retData.getData();
            if (data == null) {
                log.warn("feignCrmClient.basicBuildMeta 返回 : {}", JSON.toJSONString(retData));
                return;
            }
            log.info("feignCrmClient.basicBuildMeta 返回 : {}", JSON.toJSONString(retData));
            project.setAiLevel(data.getProjectLevelAi());
        } catch (Exception e) {
            log.error("feignCrmClient.basicBuildMeta异常", e);
        }
    }

    /**
     * 设置合同维度的人工评级和ai评级
     *
     * @param applyParam
     * @param contract
     */
    private static void setContractLevel(ContractApplyParam applyParam, ContractEntity contract) {
        if (CollectionUtils.isNotEmpty(applyParam.getProjects())) {
            AtomicReference<String> maxLevel = new AtomicReference<>(ProjectLevelEnum.OTHER.getDesc());
            AtomicReference<String> maxAiLevel = new AtomicReference<>(ProjectLevelEnum.OTHER.getDesc());
            applyParam.getProjects().forEach(p -> {
                maxLevel.set(ProjectLevelEnum.max(maxLevel.get(), p.getLevel()));
                maxAiLevel.set(ProjectLevelEnum.max(maxAiLevel.get(), p.getAiLevel()));
            });
            contract.setBuildingLevel(maxLevel.get());
            contract.setAiBuildingLevel(maxAiLevel.get());
        }
    }

    /**
     * 是否再次提交
     *
     * @param
     * @return true 关键信息没有变更，继承原实例 ，false 发起新的审批
     */
    private boolean checkNeedResubmit(ContractEntity contract) {
        log.info("checkNeedResubmit");
        if (contract == null) {
            return false;
        }
        log.info("checkNeedResubmit 合同信息{}", JSON.toJSONString(contract));

        Integer id = contract.getId();
        if (id == null) {
            log.info("checkNeedResubmit 合同ID为空,返回false");
            return false;
        }

        ContractEntity daoContract = contractService.getById(id);
        if (daoContract == null) {
            log.info("checkNeedResubmit 数据库合同为空,返回false");
            return false;
        }

        BigDecimal daoTotalAmount = daoContract.getTotalAmount();
        Integer daoNormalFlag = daoContract.getNormalFlag();

        BigDecimal paramTotalAmount = contract.getTotalAmount();
        Integer paramNormalFlag = contract.getNormalFlag();

        log.info("checkNeedResubmit {} {} {} {}", daoTotalAmount, paramTotalAmount, daoNormalFlag, paramNormalFlag);

        if (BigDecimalUtils.eq(daoTotalAmount, paramTotalAmount) && integerEquals(daoNormalFlag, paramNormalFlag)) {
            log.info("checkNeedResubmit 合同ID：{} 金额和是否标准合同没有变化 ,返回true", id);
            return true;
        }
        log.info("checkNeedResubmit都没命中 合同ID：{} ,返回false", id);
        return false;
    }

    public boolean integerEquals(Integer a, Integer b) {
        if (a == b) return true;
        if (a == null || b == null) return false;
        return a.intValue() == b.intValue();
    }


    /**
     * 合同盖章
     */
    public boolean sealContract(Integer contractId, ContractSealParam sealParam) {
        ContractEntity contract = Optional.ofNullable(contractService.getById(contractId))
                .orElseThrow(() -> new CommonException("合同不存在"));
        ContractPartyEnum partyEnum = ContractPartyEnum.parse(sealParam.getSealParty(), ContractPartyEnum.PARTY_A);

        // 查询供应商
        long supplierCount = contractSupplierService.lambdaQuery()
                .eq(ContractSupplierEntity::getContractId, contractId)
                .eq(ContractSupplierEntity::getSubContractId, 0)
                .count();

        // 盖章时间
        LocalDate sealDate = Optional.ofNullable(sealParam.getSealDate()).orElseGet(LocalDate::now);

        // 甲方
        if (Objects.equals(ContractPartyEnum.PARTY_A, partyEnum)) {
            if (supplierCount <= 0) throw new CommonException("合同未关联供应商");
            if (BooleFlagEnum.isYes(contract.getSealParty1Flag())) {
                throw new CommonException(partyEnum.getDesc() + "已盖章，不需要重复操作");
            }

            // 更新盖章标记
            boolean result = contractService.lambdaUpdate()
                    .set(ContractEntity::getSealParty1Flag, BooleFlagEnum.YES.getCode())
                    .set(ContractEntity::getSealParty1Date, sealDate)
                    .eq(ContractEntity::getId, contractId)
                    .update();

            // 保存合同操作日志
            saveContractLog(contractId, contractLog -> contractLog.setContent(partyEnum.getDesc() + "盖章" + " 备注：" + sealParam.getRemark()));
            return result;
        }

        // 乙方（我方）
        if (Objects.equals(ContractPartyEnum.PARTY_B, partyEnum)) {
            if (BooleFlagEnum.isYes(contract.getSealParty2Flag())) {
                throw new CommonException(partyEnum.getDesc() + "已盖章，不需要重复操作");
            }

            // 更新盖章标记
            boolean result = contractService.lambdaUpdate()
                    .set(ContractEntity::getSealParty2Flag, BooleFlagEnum.YES.getCode())
                    .set(ContractEntity::getSealParty2Date, sealDate)
                    .eq(ContractEntity::getId, contractId)
                    .update();

            // 保存合同操作日志
            saveContractLog(contractId, contractLog -> contractLog.setContent(partyEnum.getDesc() + "盖章" + " 备注：" + sealParam.getRemark()));
            return result;
        }

        // 丙方
        if (Objects.equals(ContractPartyEnum.PARTY_C, partyEnum)) {
            if (supplierCount <= 1) throw new CommonException("合同未关联第三方供应商");
            if (BooleFlagEnum.isYes(contract.getSealParty3Flag())) {
                throw new CommonException(partyEnum.getDesc() + "已盖章，不需要重复盖章");
            }

            // 更新盖章标记
            boolean result = contractService.lambdaUpdate()
                    .set(ContractEntity::getSealParty3Flag, BooleFlagEnum.YES.getCode())
                    .set(ContractEntity::getSealParty3Date, sealDate)
                    .eq(ContractEntity::getId, contractId)
                    .update();

            // 保存合同操作日志
            saveContractLog(contractId, contractLog -> contractLog.setContent(partyEnum.getDesc() + "盖章" + " 备注：" + sealParam.getRemark()));
            return result;
        }

        return false;
    }

    /**
     * 合同归档
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean archiveContract(Integer contractId, ContractArchiveParam archiveParam) {
        ContractEntity contract = Optional.ofNullable(contractService.getById(contractId))
                .orElseThrow(() -> new CommonException("合同不存在"));

        if (BooleFlagEnum.isYes(contract.getArchiveFlag())) {
            throw new CommonException("合同已归档，不需要重复操作");
        }

        // 只有甲乙方附件都已经上传才能归档
        if (!BooleFlagEnum.isYes(contract.getUploadFlag())) {
            throw new CommonException("主合同附件未上传，不能进行归档");
        }

        // 是否有子合同
        Long subContractCount = contractSubService.lambdaQuery()
                .eq(ContractSubEntity::getContractId, contractId)
                .count();
        if (Objects.nonNull(subContractCount) && subContractCount > 0
                && !BooleFlagEnum.isYes(contract.getUploadSubFlag())) {
            throw new CommonException("子合同附件未上传，不能进行归档");
        }

        // 计算当前合同状态
        LocalDate today = LocalDate.now();
        Supplier<ContractStatusEnum> getStatus = () -> {
            // 当前时间 > 合同结束时间 => 已到期
            if (today.isAfter(contract.getEndDate())) {
                return ContractStatusEnum.EXPIRED;
            }

            // 当前时间 >= 合同开始时间 => 执行中
            if (today.isAfter(contract.getStartDate()) || today.isEqual(contract.getStartDate())) {
                return ContractStatusEnum.EXECUTING;
            }

            // 待执行
            return ContractStatusEnum.WAIT_EXECUTE;
        };

        // 更新归档状态
        boolean result = contractService.lambdaUpdate()
                .set(ContractEntity::getArchiveFlag, BooleFlagEnum.YES.getCode())
                .set(ContractEntity::getArchiveRemark, archiveParam.getRemark())
                .set(ContractEntity::getArchiveTime, LocalDateTime.now())
                .set(ContractEntity::getFormalStatus, getStatus.get().getCode())
                .eq(ContractEntity::getId, contractId)
                .update();

        // 记录合同状态变更
        statusChangeLogService.recordContractStatusChange(contract.getId(), contract.getApplyCode(), getStatus.get()
                .getCode());
        contractNotifyService.notifyBusinessOnStatusChanged(contract.getId(), "0043-6");

        // 合同归档后，通知SSP将点位状态改成启用
        contractNotifyService.notifyOnStatusChanged(contractId, ContractApplyStatusEnum.APPROVED);

        // 生成台账
        ledgerService.createLedger(contractId);

        // 保存合同操作日志
        saveContractLog(contractId, contractLog -> contractLog.setContent("确认归档。归档说明：" + archiveParam.getRemark()));
        return result;
    }

    /**
     * 合同作废
     */
    public boolean cancelContract(Integer contractId, ContractCancelParam cancelParam) {
        ContractEntity contract = Optional.ofNullable(contractService.getById(contractId))
                .orElseThrow(() -> new CommonException("合同不存在"));
        if (StringUtils.equalsIgnoreCase(contract.getFormalStatus(), ContractStatusEnum.INVALID.getCode())) {
            throw new CommonException("合同已作废，操作失败");
        }

        // 更新合同状态
        boolean result = contractService.lambdaUpdate()
                .set(ContractEntity::getFormalStatus, ContractStatusEnum.INVALID.getCode())
                .set(ContractEntity::getApplyStatus, ContractApplyStatusEnum.INVALID.getCode())
                .set(ContractEntity::getCancelReason, cancelParam.getRemark())
                .eq(ContractEntity::getId, contractId)
                .update();


        // 保存作废附件
        String attachmentNames = StringUtils.EMPTY;
        if (CollectionUtils.isNotEmpty(cancelParam.getAttachments())) {
            List<ContractAttachmentEntity> entities = cancelParam.getAttachments().stream()
                    .map(contractConvert::toEntity)
                    .peek(attachment -> {
                        attachment.setContractId(contractId);
                        attachment.setBizId(contractId);
                        attachment.setType(AttachmentTypeEnum.CANCEL.getCode());
                        attachment.setSubType(0);
                        attachment.setDeleteFlag(BooleFlagEnum.NO.getCode());
                    }).toList();
            result &= attachmentService.saveBatch(entities);
            attachmentNames = cancelParam.getAttachments().stream().map(ContractAttachmentParam::getName)
                    .collect(Collectors.joining(","));
        }

        // 合同作废，发送消息通知其它系统
        Set<Integer> contractTypes = Sets.newHashSet(ContractTypeEnum.NORMAL.getCode(), ContractTypeEnum.CHANGE.getCode());
        if (result && contractTypes.contains(contract.getContractType())) {
            contractNotifyService.notifyOnStatusChanged(contractId, ContractApplyStatusEnum.INVALID);
            contractNotifyService.notifyBusinessOnStatusChanged(contractId, "0043-4");
        }

        // 合同作废，删除台账信息
        ledgerService.deleteLedgerByContractId(contractId);

        // 保存合同操作日志
        if (result) {
            StringJoiner logMsg = new StringJoiner(", ");
            logMsg.add("作废合同。作废原因：%s".formatted(cancelParam.getRemark()));
            if (StringUtils.isNotBlank(attachmentNames)) {
                logMsg.add("附件名称：%s".formatted(attachmentNames));
            }
            saveContractLog(contractId, contractLog -> contractLog.setContent(logMsg.toString()));
        }
        return result;
    }

    /**
     * 合同驳回
     */
    public boolean rejectContract(Integer contractId, ContractRejectParam rejectParam) {
        ContractEntity contract = Optional.ofNullable(contractService.getById(contractId))
                .orElseThrow(() -> new CommonException("合同不存在"));

        // 更新合同状态
        boolean result = contractService.lambdaUpdate()
                .set(ContractEntity::getFormalStatus, ContractStatusEnum.PENDING.getCode())
                .set(ContractEntity::getApplyStatus, ContractApplyStatusEnum.REJECT.getCode())
                .eq(ContractEntity::getId, contractId)
                .update();


        // 合同驳回，发送消息通知其它系统
        Set<Integer> contractTypes = Sets.newHashSet(ContractTypeEnum.NORMAL.getCode(), ContractTypeEnum.CHANGE.getCode());
        if (result && contractTypes.contains(contract.getContractType())) {
            contractNotifyService.notifyOnStatusChanged(contractId, ContractApplyStatusEnum.REJECT);
            contractNotifyService.notifyBusinessOnStatusChanged(contractId, "0043-4");
        }


        // 保存合同操作日志
        if (result) {
            saveContractLog(contractId, contractLog -> contractLog.setContent("驳回合同 驳回原因：" + rejectParam.getRemark()));
        }
        return result;
    }

    /**
     * 补充协议作废
     */
    public boolean cancelAgreement(Integer contractId, ContractCancelParam cancelParam) {
        ContractEntity contract = Optional.ofNullable(contractService.getById(contractId))
                .orElseThrow(() -> new CommonException("补充协议不存在"));
        if (StringUtils.equalsIgnoreCase(contract.getFormalStatus(), ContractStatusEnum.INVALID.getCode())) {
            throw new CommonException("补充协议已作废，操作失败");
        }

        // 更新补充协议状态
        boolean result = contractService.lambdaUpdate()
                .set(ContractEntity::getApplyStatus, ContractApplyStatusEnum.INVALID.getCode())
                .set(ContractEntity::getFormalStatus, ContractStatusEnum.INVALID.getCode())
                .set(Objects.nonNull(cancelParam.getRemark()), ContractEntity::getCancelReason, cancelParam.getRemark())
                .eq(ContractEntity::getId, contractId)
                .update();

        // 保存作废附件
        String attachmentNames = StringUtils.EMPTY;
        if (CollectionUtils.isNotEmpty(cancelParam.getAttachments())) {
            List<ContractAttachmentEntity> entities = cancelParam.getAttachments().stream()
                    .map(contractConvert::toEntity)
                    .peek(attachment -> {
                        attachment.setContractId(contractId);
                        attachment.setBizId(contractId);
                        attachment.setType(AttachmentTypeEnum.CANCEL.getCode());
                        attachment.setSubType(0);
                        attachment.setDeleteFlag(BooleFlagEnum.NO.getCode());
                    }).toList();
            result &= attachmentService.saveBatch(entities);
            attachmentNames = cancelParam.getAttachments().stream().map(ContractAttachmentParam::getName)
                    .collect(Collectors.joining(","));
        }

        // 补充协议作废，删除台账信息
        ledgerService.deleteLedgerByContractId(contractId);

        // 保存补充协议操作日志
        if (result) {
            StringJoiner logMsg = new StringJoiner(", ");
            logMsg.add("作废协议。作废原因：%s".formatted(cancelParam.getRemark()));
            if (StringUtils.isNotBlank(attachmentNames)) {
                logMsg.add("附件名称：%s".formatted(attachmentNames));
            }
            saveContractLog(contractId, contractLog -> contractLog.setContent(logMsg.toString()));
        }
        return result;
    }


    /**
     * 代理商合同审批
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean agentAudit(Integer contractId, ContractAuditParam auditParam) {
        ContractEntity contract = Optional.ofNullable(contractService.getById(contractId))
                .orElseThrow(() -> new CommonException("合同不存在"));

        // 检查是否代理合同
        if (!Objects.equals(contract.getBusinessType(), BusinessTypeEnum.AGENT.getCode())) {
            throw new CommonException("非代理合同申请单，不允许代理审核");
        }

        // 合同非审批阶段
        if (!Objects.equals(contract.getApplyStatus(), ContractApplyStatusEnum.PRE_APPROVING.getCode())) {
            throw new CommonException("合同状态不正确");
        }

        // 检查代理合同审批人
        String wno = Optional.ofNullable(UserThreadLocal.getUser()).map(CachedUser::getWno).orElse(null);
        if (!StringUtils.equalsIgnoreCase(wno, contract.getAgentApprover())) {
            throw new CommonException("代理合同审批人不匹配");
        }

        // 1:审核通过 2:审批拒绝
        // 审批通过
        boolean result = false;
        if (Objects.equals(auditParam.getStatus(), 1)) {
            result = contractService.lambdaUpdate()
                    .set(ContractEntity::getApplyStatus, ContractApplyStatusEnum.SUBMIT.getCode())
                    .set(StringUtils.isNotBlank(auditParam.getRemark()), ContractEntity::getAgentApprovalReason, auditParam.getRemark())
                    .eq(ContractEntity::getId, contractId)
                    .update();

            // 提交合同申请单
            submitContract(contractId, true, false);

            // 保存合同操作日志
            String remark = StringUtils.isNotBlank(auditParam.getRemark()) ? "审批原因: " + auditParam.getRemark() : "";
            saveContractLog(contractId, contractLog -> contractLog.setContent("代理商合同审批通过，发起飞书审批。" + remark));
            return result;
        }

        // 审批拒绝
        if (Objects.equals(auditParam.getStatus(), 2)) {
            result = contractService.lambdaUpdate()
                    .set(ContractEntity::getApplyStatus, ContractApplyStatusEnum.REJECT.getCode())
                    .set(ContractEntity::getAgentApprovalReason, auditParam.getRemark())
                    .eq(ContractEntity::getId, contractId)
                    .update();

            // 合同拒绝，发送消息通知其它系统
            if (result && !ContractTypeEnum.AGREEMENT.getCode().equals(contract.getContractType())) {
                contractNotifyService.notifyOnStatusChanged(contractId, ContractApplyStatusEnum.REJECT);
                contractNotifyService.notifyBusinessOnStatusChanged(contractId, "0043-4");
            }

            // 保存合同操作日志
            String remark = StringUtils.isNotBlank(auditParam.getRemark()) ? "拒绝原因: " + auditParam.getRemark() : "";
            saveContractLog(contractId, contractLog -> contractLog.setContent("代理商合同被拒绝。" + remark));
            return result;
        }

        return false;
    }


    /**
     * 合同转交
     */
    @Deprecated
    public boolean transferContract(Integer contractId, ContractTransferParam transferParam) {
        ContractEntity contract = Optional.ofNullable(contractService.getById(contractId))
                .orElseThrow(() -> new CommonException("合同不存在"));

        // 更新跟进人
        boolean result = contractService.lambdaUpdate()
                .set(ContractEntity::getFollower, transferParam.getFollower())
                .set(ContractEntity::getFollowerName, transferParam.getFollowerName())
                .eq(ContractEntity::getId, contract.getId())
                .update();

        // 补充协议跟进人修改
        contractService.lambdaUpdate()
                .set(ContractEntity::getFollower, transferParam.getFollower())
                .set(ContractEntity::getFollowerName, transferParam.getFollowerName())
                .eq(ContractEntity::getParentId, contract.getId())
                .update();

        // 台账跟进人修改
        ledgerService.updateFollower(contract.getId(), transferParam.getFollower(), transferParam.getFollowerName());

        // 保存合同操作日志
        String content = String.format("转交。跟进人：%s(%s)", transferParam.getFollowerName(), transferParam.getFollower());
        saveContractLog(contractId, contractLog -> contractLog.setContent(content));
        return result;
    }

    /**
     * 合同批量转交
     */
    public boolean batchTransferContract(BatchContractTransferParam batchContractTransferParam) {
        List<Integer> contractIds = batchContractTransferParam.getContractIds();
        List<ContractEntity> contractEntities = contractService.lambdaQuery()
                .select(ContractEntity::getId)
                .in(ContractEntity::getId, contractIds).list();
        // 比对数据库是否有不存在数据
        if (CollectionUtils.isEmpty(contractEntities)) {
            throw new CommonException("所选择合同全部不存在");
        }
        List<Integer> existIds = contractEntities.stream().map(ContractEntity::getId).toList();
        // 比较两个数组差异
        List<Integer> diffIds = contractIds.stream().distinct().filter(id -> !existIds.contains(id)).toList();
        if (CollectionUtils.isNotEmpty(diffIds)) {
            String msg = String.format("合同不存在, 合同ID：%s", StringUtils.join(diffIds, ","));
            throw new CommonException(msg);
        }

        // 更新跟进人
        boolean result = contractService.lambdaUpdate()
                .set(ContractEntity::getFollower, batchContractTransferParam.getFollower())
                .set(ContractEntity::getFollowerName, batchContractTransferParam.getFollowerName())
                .in(ContractEntity::getId, contractIds)
                .update();

        // 补充协议跟进人修改
        contractService.lambdaUpdate()
                .set(ContractEntity::getFollower, batchContractTransferParam.getFollower())
                .set(ContractEntity::getFollowerName, batchContractTransferParam.getFollowerName())
                .in(ContractEntity::getParentId, contractIds)
                .update();

        // 台账跟进人修改
        ledgerService.batchUpdateFollower(batchContractTransferParam);

        // 保存合同操作日志
        String content = String.format("转交。跟进人：%s(%s)", batchContractTransferParam.getFollowerName(), batchContractTransferParam.getFollower());
        batchSaveContractLog(contractIds, contractLog -> contractLog.setContent(content));
        return result;
    }

    /**
     * [历史合同编辑] 变更合同相关信息
     *
     * @param contractId 合同ID
     * @return true: 成功，false: 失败
     */
    public boolean changeSubContractOnHistoryContractEdit(Integer contractId) {
        ContractEntity contract = contractService.getById(contractId);
        if (Objects.isNull(contract)) {
            log.warn("[历史合同编辑] 变更合同相关信息，合同({})不存在", contractId);
            return false;
        }

        // 更新合同状态
        String contractCode = contract.getContractCode();

        // 修改 子合同编号 和 WORD文件上的合同编号
        List<ContractSubEntity> subcontracts = contractSubService.lambdaQuery()
                .select(ContractSubEntity::getId)
                .eq(ContractSubEntity::getContractId, contractId)
                .list();
        if (CollectionUtils.isNotEmpty(subcontracts)) {
            for (int i = 0; i < subcontracts.size(); i++) {
                subcontracts.get(i).setContractCode(String.format("%s-%02d", contractCode, i + 1));
            }
            contractSubService.updateBatchById(subcontracts);
        }

        return true;
    }

    /**
     * 修改合同状态，当飞书审批通过后
     *
     * @param contractId 合同ID
     * @return true: 成功，false: 失败
     */
    public boolean changeContractStatusOnApprovalPassed(Integer contractId) {
        ContractEntity contract = contractService.getById(contractId);
        if (Objects.isNull(contract)) {
            log.warn("[飞书审批] 修改状态为通过，合同({})不存在", contractId);
            return false;
        }

        // 获取项目城市名称
        List<String> areaNames = projectService.lambdaQuery()
                .select(ContractProjectEntity::getCityId, ContractProjectEntity::getAreaName)
                .eq(ContractProjectEntity::getContractId, contractId)
                .list().stream()
                .map(ContractProjectEntity::getAreaName)
                .filter(StringUtils::isNotBlank).toList();
        if (CollectionUtils.isEmpty(areaNames)) {
            log.warn("[飞书审批] 修改状态为通过，合同({})的城市信息不存在", contractId);
            return false;
        }

        // 更新合同状态
        String contractCode = StringUtils.isBlank(contract.getContractCode()) ? getContractCode(areaNames.get(0), contract.getCityId()) : contract.getContractCode();
        boolean result = contractService.lambdaUpdate()
                .set(ContractEntity::getContractCode, contractCode)
                .set(ContractEntity::getFormalFlag, BooleFlagEnum.YES.getCode())
                .set(ContractEntity::getApplyStatus, ContractApplyStatusEnum.APPROVED.getCode())
                .set(ContractEntity::getFormalStatus, ContractStatusEnum.PENDING.getCode())
                .eq(ContractEntity::getId, contractId)
                .update();

        // 修改 子合同编号 和 WORD文件上的合同编号
        if (result) {
            List<ContractSubEntity> subcontracts = contractSubService.lambdaQuery()
                    .select(ContractSubEntity::getId)
                    .eq(ContractSubEntity::getContractId, contractId)
                    .list();
            if (CollectionUtils.isNotEmpty(subcontracts)) {
                for (int i = 0; i < subcontracts.size(); i++) {
                    subcontracts.get(i).setContractCode(String.format("%s-%02d", contractCode, i + 1));
                }
                contractSubService.updateBatchById(subcontracts);
            }

            // 记录合同状态变更 (审批通过)
            statusChangeLogService.recordContractStatusChange(contract.getId(), contract.getApplyCode(), ContractApplyStatusEnum.APPROVED.getCode());

            // 修改WORD上的合同编号
            contractTemplateService.generate(contract.getId(), false);
        }

        return result;
    }

    /**
     * 修改合同状态，当飞书审批不通过后
     *
     * @param contractId 合同ID
     * @return true: 成功，false: 失败
     */
    public boolean changeContractStatusOnApprovalReject(Integer contractId) {
        ContractEntity contract = contractService.getById(contractId);
        if (Objects.isNull(contract)) {
            log.warn("[飞书审批] 修改状态为拒绝，合同({})不存在", contractId);
            return false;
        }

        // 更新合同状态
        boolean result = contractService.lambdaUpdate().set(ContractEntity::getContractCode, null)
                .set(ContractEntity::getFormalFlag, BooleFlagEnum.NO.getCode())
                .set(ContractEntity::getApplyStatus, ContractApplyStatusEnum.REJECT.getCode())
                .set(ContractEntity::getFormalStatus, ContractStatusEnum.PENDING.getCode())
                .eq(ContractEntity::getId, contractId).update();

        // 记录合同状态变更 (审批拒绝)
        if (result) {
            statusChangeLogService.recordContractStatusChange(contract.getId(), contract.getApplyCode(), ContractApplyStatusEnum.REJECT.getCode());
        }
        return result;
    }

    /**
     * 修改合同状态，当飞书撤回后
     *
     * @param contractId 合同ID
     * @return true: 成功，false: 失败
     */
    public boolean changeContractStatusOnApprovalWithdraw(Integer contractId) {
        ContractEntity contract = contractService.getById(contractId);
        if (Objects.isNull(contract)) {
            log.warn("[飞书审批] 修改状态为撤回，合同({})不存在", contractId);
            return false;
        }

        // 更新合同状态
        boolean result = contractService.lambdaUpdate().set(ContractEntity::getContractCode, null)
                .set(ContractEntity::getFormalFlag, BooleFlagEnum.NO.getCode())
                .set(ContractEntity::getApplyStatus, ContractApplyStatusEnum.WITHDRAW.getCode())
                .set(ContractEntity::getFormalStatus, ContractStatusEnum.PENDING.getCode())
                .eq(ContractEntity::getId, contractId).update();

        contractNotifyService.notifyOnStatusChanged(contractId, ContractApplyStatusEnum.WITHDRAW);
        contractNotifyService.notifyBusinessOnStatusChanged(contractId, "0043-4");

        // 记录合同状态变更 (审批撤回)
        if (result) {
            statusChangeLogService.recordContractStatusChange(contract.getId(), contract.getApplyCode(), ContractApplyStatusEnum.WITHDRAW.getCode());
        }
        return result;
    }


    /**
     * 提交合同申请单
     *
     * @param contractId       合同ID
     * @param toFeiShuApproval 提交给飞书审批
     * @param isResubmit       是否需要再次发起
     */
    public void submitContract(Integer contractId, boolean toFeiShuApproval, boolean isResubmit) {
        try {
            // 生成合同申请单
            contractTemplateService.generate(contractId, true);
        } catch (Exception ex) {
            log.error("创建合同({})WORD文件失败", contractId, ex);
        }

        // 内部用户直接提交给飞书，外部用户需要预审
        if (toFeiShuApproval) {
            try {
                handleContractApproval(contractId, isResubmit);
            } catch (Exception ex) {
                log.error("创建合同({})审批信息失败", contractId, ex);
            }
        }

        try {
            contractNotifyService.notifyBusinessOnStatusChanged(contractId, "0043-5");
        } catch (Exception ex) {
            log.error("合同({})创建后发送商机变更通知失败", contractId, ex);
        }

        try {
            contractNotifyService.notifyOnStatusChanged(contractId, ContractApplyStatusEnum.SUBMIT);
        } catch (Exception ex) {
            log.error("合同({})创建后发送通知失败", contractId, ex);
        }
    }

    public void handleContractApproval(Integer contractId, boolean isResubmit) {
        log.info("处理合同审批,合同ID:{},isResubmit:{}", contractId, isResubmit);
        ContractEntity contract = contractService.getById(contractId);
        if (contract == null) {
            throw new IllegalStateException("没找到合同实例");
        }
        log.info("handleContractApproval,合同信息{}", JSON.toJSONString(contract));
        log.info("处理合同审批,合同ID:{},当前状态为{}", contractId, contract.getApplyStatus());
        if (StringUtils.equals(ContractApplyStatusEnum.RETURN.getCode(), contract.getApplyStatus())) {
            log.info("处理合同审批,合同ID:{},再次提交", contractId);
            contractApprovalService.taskResubmit(contractId, isResubmit);
        } else {
            log.info("处理合同审批,合同ID:{}创建新审批", contractId);
            contractApprovalService.createContractApprovalInstance(contractId);
        }
    }

    /**
     * 提交补充协议申请单
     *
     * @param contractId       补充协议ID
     * @param toFeiShuApproval 提交给飞书审批
     */
    public void submitContractAgreement(Integer contractId, boolean toFeiShuApproval, boolean isResubmit) {

        // 内部用户直接提交给飞书，外部用户需要预审
        if (toFeiShuApproval) {
            try {
                handleContractApproval(contractId, isResubmit);
            } catch (Exception ex) {
                log.error("创建补充协议({})审批信息失败", contractId, ex);
            }
        }
    }

    /**
     * 提交合同变更申请单
     *
     * @param contractId       合同变更单ID
     * @param toFeiShuApproval 提交给飞书审批
     */
    public void submitContractAmendment(Integer contractId, boolean toFeiShuApproval, boolean isResubmit) {

        // 内部用户直接提交给飞书，外部用户需要预审
        if (toFeiShuApproval) {
            try {
                handleContractApproval(contractId, isResubmit);
            } catch (Exception ex) {
                log.error("创建合同变更({})审批信息失败", contractId, ex);
                throw new RuntimeException("创建合同变更审批信息失败");
            }
        }
    }

    /**
     * 转换成实体
     */
    private ContractEntity toContract(boolean submit, ContractApplyParam applyParam, UserTypeEnum userType) {
        Objects.requireNonNull(applyParam, "合同申请参数不能为空");

        // 记录合同申请单状态
        AtomicReference<String> applyStatus = new AtomicReference<>(ContractApplyStatusEnum.DRAFT.getCode());
        if (submit) {
            // 代理商用户 提交 代理合同 => 状态为 【预审】
            applyStatus.set(Objects.equals(applyParam.getBusinessType(), BusinessTypeEnum.AGENT.getCode()) && Objects.equals(UserTypeEnum.AGENT, userType)
                    ? ContractApplyStatusEnum.PRE_APPROVING.getCode()
                    : ContractApplyStatusEnum.SUBMIT.getCode());
        }

        // 查询已存在的合同申请编码
        String applyCode = null;
        if (Objects.nonNull(applyParam.getId())) {
            ContractEntity existedContract = contractService.lambdaQuery()
                    .select(ContractEntity::getApplyCode, ContractEntity::getApplyStatus)
                    .eq(ContractEntity::getId, applyParam.getId())
                    .one();
            applyCode = Optional.ofNullable(existedContract).map(ContractEntity::getApplyCode).orElse(null);
            if (StringUtils.equals(ContractApplyStatusEnum.RETURN.getCode(), existedContract.getApplyStatus())) {
                applyStatus.set(existedContract.getApplyStatus());
            }
        }

        // 转换成合同申请单
        ContractEntity contract = contractConvert.toEntity(applyParam);
        contract.setFormalFlag(BooleFlagEnum.NO.getCode());
        contract.setSubmitter(UserThreadLocal.getUserId());
        contract.setFollower(UserThreadLocal.getUserId());
        contract.setApplyStatus(applyStatus.get());
        contract.setApplyCode(StringUtils.isBlank(applyCode) ? getApplyCode() : applyCode);
        contract.setContractType(Objects.equals(BooleFlagEnum.YES.getCode(), applyParam.getOldFlag()) ? ContractTypeEnum.CHANGE.getCode() : ContractTypeEnum.NORMAL.getCode());
        if (CollectionUtils.isNotEmpty(applyParam.getProjects()) && Objects.nonNull(applyParam.getProjects().get(0)
                .getCityId())) {
            contract.setCityId(applyParam.getProjects().get(0).getCityId());
        }
        return contract;
    }

    /**
     * xfl
     * 转换成实体
     */
    private ContractEntity toContract(boolean submit, ContractAgreementAddParam applyParam, UserTypeEnum userType) {
        Objects.requireNonNull(applyParam, "补充协议申请参数不能为空");
        log.info("合同变更申请参数:{}", JSON.toJSONString(applyParam));

        // 记录补充协议申请单状态
        AtomicReference<String> applyStatus = new AtomicReference<>(ContractApplyStatusEnum.DRAFT.getCode());
        if (submit) {
            // 代理商用户 提交 代理合同 => 状态为 【预审】
            applyStatus.set(Objects.equals(applyParam.getBusinessType(), BusinessTypeEnum.AGENT.getCode()) && Objects.equals(UserTypeEnum.AGENT, userType)
                    ? ContractApplyStatusEnum.PRE_APPROVING.getCode()
                    : ContractApplyStatusEnum.SUBMIT.getCode());
        }

        // 查询已存在的合同申请编码
        String applyCode = null;
        if (Objects.nonNull(applyParam.getId())) {
            ContractEntity existedContract = contractService.lambdaQuery()
                    .select(ContractEntity::getApplyCode, ContractEntity::getApplyStatus)
                    .eq(ContractEntity::getId, applyParam.getId())
                    .one();
            log.info("合同变更申请数据库信息:{}", JSON.toJSONString(existedContract));
            applyCode = Optional.ofNullable(existedContract).map(ContractEntity::getApplyCode).orElse(null);
            if (StringUtils.equals(ContractApplyStatusEnum.RETURN.getCode(), existedContract.getApplyStatus())) {
                applyStatus.set(existedContract.getApplyStatus());
            }
        }

        // 转换成合同申请单
        ContractEntity contract = contractConvert.toEntity(applyParam);
        contract.setFormalFlag(BooleFlagEnum.NO.getCode());
        contract.setSubmitter(UserThreadLocal.getUserId());
        contract.setFollower(UserThreadLocal.getUserId());
        contract.setApplyStatus(applyStatus.get());
        contract.setApplyCode(StringUtils.isBlank(applyCode) ? getContractAgreementApplyCode() : applyCode);
        if (CollectionUtils.isNotEmpty(applyParam.getProjects()) && Objects.nonNull(applyParam.getProjects().get(0)
                .getCityId())) {
            contract.setCityId(applyParam.getProjects().get(0).getCityId());
        }
        return contract;
    }

    /**
     * xfl
     * 转换成实体
     */
    private ContractEntity toContract(boolean submit, ContractAmendmentApplyParam applyParam, UserTypeEnum userType) {
        Objects.requireNonNull(applyParam, "合同变更申请参数不能为空");
        log.info("合同变更申请参数:{}", JSON.toJSONString(applyParam));

        // 记录合同变更申请单状态
        AtomicReference<String> applyStatus = new AtomicReference<>(ContractApplyStatusEnum.DRAFT.getCode());
        if (submit) {
            // 代理商用户 提交 代理合同 => 状态为 【预审】
            applyStatus.set(Objects.equals(applyParam.getBusinessType(), BusinessTypeEnum.AGENT.getCode()) && Objects.equals(UserTypeEnum.AGENT, userType)
                    ? ContractApplyStatusEnum.PRE_APPROVING.getCode()
                    : ContractApplyStatusEnum.SUBMIT.getCode());
        }

        // 查询已存在的合同申请编码
        String applyCode = null;
        if (Objects.nonNull(applyParam.getId())) {
            ContractEntity existedContract = contractService.lambdaQuery()
                    .select(ContractEntity::getApplyCode, ContractEntity::getApplyStatus)
                    .eq(ContractEntity::getId, applyParam.getId())
                    .one();
            log.info("查询已存在的合同申请编码:{}", existedContract);
            applyCode = Optional.ofNullable(existedContract).map(ContractEntity::getApplyCode).orElse(null);
            if (StringUtils.equals(ContractApplyStatusEnum.RETURN.getCode(), existedContract.getApplyStatus())) {
                applyStatus.set(existedContract.getApplyStatus());
            }
        }

        // 转换成合同变更申请单
        ContractEntity contract = contractConvert.toEntity(applyParam);
        contract.setFormalFlag(BooleFlagEnum.NO.getCode());
        contract.setSubmitter(UserThreadLocal.getUserId());
        contract.setFollower(UserThreadLocal.getUserId());
        contract.setApplyStatus(applyStatus.get());
        contract.setApplyCode(StringUtils.isBlank(applyCode) ? getContractAmendmentApplyCode() : applyCode);
        if (CollectionUtils.isNotEmpty(applyParam.getProjects()) && Objects.nonNull(applyParam.getProjects().get(0)
                .getCityId())) {
            contract.setCityId(applyParam.getProjects().get(0).getCityId());
        }
        return contract;
    }

    /**
     * 删除所有嵌套信息
     */
    private boolean deleteAllExtend(ContractEntity contract) throws CommonException {
        if (Objects.isNull(contract) || Objects.isNull(contract.getId())) return true;
        Integer contractId = contract.getId();

        // 删除供应商
        contractSupplierService.lambdaUpdate().eq(ContractSupplierEntity::getContractId, contractId).remove();

        // 删除供应商银行
        contractSupplierBankService.lambdaUpdate().eq(ContractSupplierBankEntity::getContractId, contractId).remove();

        // 删除项目信息
        projectService.lambdaUpdate().eq(ContractProjectEntity::getContractId, contractId).remove();

        // 删除项目押金供应商信息
        depositSupplierService.lambdaUpdate().eq(ContractDepositSupplierEntity::getContractId, contractId).remove();

        // 删除价格申请
        priceApplyService.lambdaUpdate().eq(ContractPriceApplyEntity::getContractId, contractId).remove();

        // 删除设备信息
        deviceService.lambdaUpdate().eq(ContractDeviceEntity::getContractId, contractId).remove();

        // 删除安装信息
        devicePointService.lambdaUpdate().eq(ContractDevicePointEntity::getContractId, contractId).remove();

        // 删除价格周期
        pricePeriodService.lambdaUpdate().eq(ContractPricePeriodEntity::getContractId, contractId).remove();

        // 删除支付周期
        paymentPeriodService.lambdaUpdate().eq(ContractPaymentPeriodEntity::getContractId, contractId).remove();

        // 删除子合同
        contractSubService.lambdaUpdate().eq(ContractSubEntity::getContractId, contractId).remove();

        // 删除合同附件
        attachmentService.lambdaUpdate().eq(ContractAttachmentEntity::getContractId, contractId).remove();

        // 删除变更类型
        if (ContractTypeEnum.AMENDMENT.getCode().equals(contract.getContractType())) {
            contractAmendmentChangeTypeService.lambdaUpdate().eq(ContractAmendmentChangeTypeEntity::getContractId,
                    contractId).remove();
            contractAmendmentTerminationService.lambdaUpdate().eq(ContractAmendmentTerminationEntity::getContractId,
                    contractId).remove();
        }
        return true;
    }

    /**
     * 保存合同供应商信息
     *
     * @param contract      合同
     * @param subContractId 子合同ID
     * @param projectId     项目ID
     * @param suppliers     供应商列表
     * @return 是否保存成功
     */
    private boolean saveSuppliers(ContractEntity contract, Integer subContractId, Integer projectId,
                                  List<ContractSupplierParam> suppliers) {
        if (CollectionUtils.isEmpty(suppliers)) return true;

        // 如果不是子合同，则设置为0
        Integer subConId = Optional.ofNullable(subContractId).orElse(0);
        Integer contractId = contract.getId();

        // 分步保存供应商信息
        boolean result = true;
        for (ContractSupplierParam supplier : suppliers) {
            ContractSupplierEntity entity = contractConvert.toEntity(supplier);
            entity.setContractId(contractId);
            entity.setSubContractId(subConId);
            entity.setProjectId(Optional.ofNullable(projectId).orElse(0));
            result &= contractSupplierService.save(entity);

            // 保存银行信息
            if (CollectionUtils.isNotEmpty(supplier.getBankAccountCodes())) {
                List<ContractSupplierBankEntity> banks = supplier.getBankAccountCodes().stream()
                        .map(accountCode -> {
                            ContractSupplierBankEntity supplierBank = new ContractSupplierBankEntity();
                            supplierBank.setContractId(contractId);
                            supplierBank.setSubContractId(subConId);
                            supplierBank.setSupplierId(entity.getSupplierId());
                            supplierBank.setBankAccountCode(SecurityUtils.decryptByFront(accountCode));
                            supplierBank.setDepositBank(BooleFlagEnum.NO.getCode());
                            return supplierBank;
                        }).toList();
                // 补全供应商、银行信息
                fillingSupplierAndBank(banks);
                contractSupplierBankService.saveBatch(banks);
            }
        }

        return result;
    }

    /**
     * 保存合同项目信息
     *
     * @param contract 合同
     * @param projects 项目信息
     * @return 项目ID
     */
    private boolean saveProjects(ContractEntity contract, List<ContractProjectParam> projects) {
        // 没有项目，则直接返回
        if (CollectionUtils.isEmpty(projects)) return true;

        // 分步保存项目信息
        boolean result = true;
        Integer contractId = contract.getId();

        // 项目信息
        List<Pair<ContractProjectParam, ContractProjectEntity>> projectPairs = projects.stream()
                .map(project -> {
                    ContractProjectEntity entity = contractConvert.toEntity(project);
                    entity.setContractId(contractId);
                    return Pair.of(project, entity);
                }).toList();
        if (CollectionUtils.isEmpty(projectPairs)) {
            return true;
        }
        result &= projectService.saveBatch(projectPairs.stream().map(Pair::getRight).toList());

        // 保存押金供应商信息
        saveProjectDepositSupplier(contractId, projectPairs);

        // 保存价格申请信息
        result &= savePriceApply(contract, projectPairs);

        // 保存子合同信息
        result &= saveSubContract(contract, projectPairs);

        return result;
    }

    /**
     * 保存合同项目信息
     *
     * @param contract 合同
     * @param projects 项目信息
     * @param map      项目的初始签约数信息
     * @return 项目ID
     */
    private boolean saveHistoryProjects(ContractEntity contract, List<ContractProjectParam> projects, Map<String, Integer> map) {
        // 没有项目，则直接返回
        if (CollectionUtils.isEmpty(projects)) return true;

        // 分步保存项目信息
        boolean result = true;
        Integer contractId = contract.getId();

        // 项目信息
        List<Pair<ContractProjectParam, ContractProjectEntity>> projectPairs = projects.stream()
                .map(project -> {
                    ContractProjectEntity entity = contractConvert.toEntity(project);
                    entity.setContractId(contractId);
                    entity.setFirstSignCount(map.get(entity.getProjectName()));
                    // 如果有变更则取实际
                    List<ContractEntity> contractEntities = contractService.lambdaQuery()
                            .select(ContractEntity::getParentId)
                            .eq(ContractEntity::getParentId, contractId)
                            .eq(ContractEntity::getEffectFlag, BooleFlagEnum.YES.getCode())
                            .list();
                    if (CollectionUtils.isNotEmpty(contractEntities)) {
                        // 变更过 取实际的数量
                        int sum = project.getPriceApplies().stream()
                                .filter(priceApply -> CollectionUtils.isNotEmpty(priceApply.getDevices()))
                                .flatMap(priceApply -> priceApply.getDevices().stream())
                                .mapToInt(ContractDeviceParam::getCount)
                                .sum();
                        entity.setLastSignCount(sum);
                    }
                    return Pair.of(project, entity);
                }).toList();
        if (CollectionUtils.isEmpty(projectPairs)) return true;
        result &= projectService.saveBatch(projectPairs.stream().map(Pair::getRight).toList());

        // 保存押金供应商信息
        saveProjectDepositSupplier(contractId, projectPairs);

        // 保存价格申请信息
        result &= savePriceApply(contract, projectPairs);

        // 保存子合同信息
        result &= saveSubContract(contract, projectPairs);

        return result;
    }

    /**
     * 保存合同附件信息
     */
    private boolean saveContractAttachments(ContractEntity contract, List<ContractAttachmentParam> attachments) {
        if (CollectionUtils.isEmpty(attachments)) return true;

        // 数据转换
        List<ContractAttachmentEntity> entities = attachments.stream().map(contractConvert::toEntity)
                .peek(attachment -> {
                    attachment.setContractId(contract.getId());
                    attachment.setBizId(contract.getId());
                    attachment.setSubType(0);
                    attachment.setDeleteFlag(BooleFlagEnum.NO.getCode());
                }).toList();
        return attachmentService.saveBatch(entities);
    }

    /**
     * 填充供应商、银行信息
     */
    private void fillingSupplierAndBank(List<? extends IFillingSupplierAndBankBoth> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return;
        }
        // 处理供应商、银行数据
        Set<Integer> supplierIds = Sets.newHashSetWithExpectedSize(entities.size());
        Set<String> accountNos = Sets.newHashSetWithExpectedSize(entities.size());
        entities.forEach(e -> {
            supplierIds.add(e.getSupplierId());
            accountNos.add(AesUtils.encryptHex(e.getAccountNo()));
        });
        // 查询供应商信息
        Map<Integer, SupplierEntity> supplierMap = supplierService.lambdaQuery()
                .in(SupplierEntity::getId, supplierIds)
                .list().stream()
                .collect(Collectors.toMap(SupplierEntity::getId, e -> e));
        // 查询供应商银行信息
        Map<String, SupplierBankEntity> bankMap = supplierBankService.lambdaQuery()
                .in(SupplierBankEntity::getAccountNo, accountNos)
                .list().stream()
                .collect(Collectors.toMap(SupplierBankEntity::getAccountNo, e -> e, (e1, e2) -> e1));

        // 补全数据
        entities.forEach(e -> {
            // 供应商数据
            SupplierEntity supplierEntity = supplierMap.get(e.getSupplierId());
            if (Objects.nonNull(supplierEntity)) {
                e.setSupplierCode(supplierEntity.getSupplierCode());
                e.setSupplierName(supplierEntity.getSupplierName());
            } else {
                log.warn("供应商信息不存在，ID：{}", e.getSupplierId());
            }
            // 供应商银行
            SupplierBankEntity bankEntity = bankMap.get(e.getAccountNo());
            if (Objects.nonNull(bankEntity)) {
                e.setBankCode(bankEntity.getBankCode());
                e.setBankName(bankEntity.getBankName());
                e.setAccountName(bankEntity.getAccountName());
            } else {
                log.warn("供应商银行信息不存在，账号：{}", e.getAccountNo());
            }
        });
    }

    /**
     * 保存项目押金供应商信息
     *
     * @param contractId   合同ID
     * @param projectPairs 项目信息
     */
    private void saveProjectDepositSupplier(Integer contractId, List<Pair<ContractProjectParam, ContractProjectEntity>> projectPairs) {
        // 押金供应商信息
        List<ContractDepositSupplierEntity> depositSuppliers = projectPairs.stream()
                .filter(pair -> BooleFlagEnum.isYes(pair.getLeft().getDepositFlag()))
                .filter(pair -> Objects.nonNull(pair.getLeft().getDepositSupplierId()))
                .map(pair -> {
                    ContractDepositSupplierEntity entity = new ContractDepositSupplierEntity();
                    entity.setContractId(contractId);
                    entity.setProjectId(pair.getRight().getId());
                    entity.setSupplierId(pair.getLeft().getDepositSupplierId());
                    entity.setAccountNo(pair.getLeft().getDepositSupplierAccountNo());
                    entity.setSupplierBankId(pair.getLeft().getDepositSupplierBankId());
                    return entity;
                }).toList();

        if (CollectionUtils.isNotEmpty(depositSuppliers)) {
            // 补全供应商、银行信息
            fillingSupplierAndBank(depositSuppliers);
            depositSupplierService.saveBatch(depositSuppliers);
        }
    }

    /**
     * 保存合同价格申请相关信息
     *
     * @param contract     合同
     * @param projectPairs 项目信息
     * @return true:保存成功
     */
    private boolean savePriceApply(ContractEntity contract, List<Pair<ContractProjectParam, ContractProjectEntity>> projectPairs) {
        // 分步保存价格申请
        boolean result = true;
        Integer contractId = contract.getId();

        // 价格申请信息
        List<Pair<ContractPriceApplyParam, ContractPriceApplyEntity>> priceApplyPairs = projectPairs.stream()
                .filter(pair -> CollectionUtils.isNotEmpty(pair.getLeft().getPriceApplies()))
                .map(pair -> {
                    Integer projectId = pair.getRight().getId();
                    return pair.getLeft().getPriceApplies().stream().map(priceApply -> {
                        ContractPriceApplyEntity entity = contractConvert.toEntity(priceApply);
                        entity.setContractId(contractId);
                        entity.setProjectId(projectId);
                        return Pair.of(priceApply, entity);
                    }).toList();
                }).flatMap(Collection::stream).toList();
        if (CollectionUtils.isEmpty(priceApplyPairs)) return result;
        result &= priceApplyService.saveBatch(priceApplyPairs.stream().map(Pair::getRight).toList());

        // 设备信息
        List<Pair<ContractDeviceParam, ContractDeviceEntity>> devicePairs = priceApplyPairs.stream()
                .filter(pair -> CollectionUtils.isNotEmpty(pair.getLeft().getDevices()))
                .map(pair -> {
                    Integer priceApplyId = pair.getRight().getId();
                    Integer projectId = pair.getRight().getProjectId();
                    return pair.getLeft().getDevices().stream().map(device -> {
                        ContractDeviceEntity entity = contractConvert.toEntity(device);
                        entity.setContractId(contractId);
                        entity.setProjectId(projectId);
                        entity.setPriceApplyId(priceApplyId);
                        return Pair.of(device, entity);
                    }).toList();
                }).flatMap(Collection::stream).toList();
        if (CollectionUtils.isEmpty(devicePairs)) return result;
        result &= deviceService.saveBatch(devicePairs.stream().map(Pair::getRight).toList());

        // 终端点位
        List<String> devicePointCodes = devicePairs.stream()
                .filter(pair -> CollectionUtils.isNotEmpty(pair.getLeft().getPoints()))
                .map(pair -> pair.getLeft().getPoints().stream().map(ContractDevicePointParam::getCode).toList())
                .flatMap(Collection::stream)
                .toList();
        Map<String, String> devicePointSizeMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(devicePointCodes)) {
            devicePointSizeMap.putAll(Optional.ofNullable(feignMethWebRpc.listPointSizeByCodes(devicePointCodes))
                    .map(ResultTemplate::getData)
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(point -> StringUtils.isNotBlank(point.getCode()))
                    .filter(point -> StringUtils.isNotBlank(point.getDeviceSize()))
                    .collect(Collectors.toMap(PointDto::getCode, PointDto::getDeviceSize, (size1, size2) -> size1)));
        }

        List<Pair<ContractDevicePointParam, ContractDevicePointEntity>> devicePointPairs = devicePairs.stream()
                .filter(pair -> CollectionUtils.isNotEmpty(pair.getLeft().getPoints()))
                .map(pair -> {
                    Integer deviceId = pair.getRight().getId();
                    Integer projectId = pair.getRight().getProjectId();
                    return pair.getLeft().getPoints().stream().map(point -> {
                        ContractDevicePointEntity entity = contractConvert.toEntity(point);
                        entity.setContractId(contractId);
                        entity.setProjectId(projectId);
                        entity.setDeviceId(deviceId);
                        entity.setSize(devicePointSizeMap.get(point.getCode()));
                        return Pair.of(point, entity);
                    }).toList();
                }).flatMap(Collection::stream).toList();
        if (CollectionUtils.isNotEmpty(devicePointPairs)) {
            result &= devicePointService.saveBatch(devicePointPairs.stream().map(Pair::getRight).toList());
        }

        // 保存子合同单价周期
        List<Pair<ContractPricePeriodParam, ContractPricePeriodEntity>> pricePeriodPairs = devicePairs.stream()
                .filter(pair -> CollectionUtils.isNotEmpty(pair.getLeft().getPricePeriods()))
                .map(pair -> {
                    Integer deviceId = pair.getRight().getId();
                    Integer projectId = pair.getRight().getProjectId();
                    return pair.getLeft().getPricePeriods().stream().map(pricePeriod -> {
                        ContractPricePeriodEntity entity = contractConvert.toEntity(pricePeriod);
                        entity.setContractId(contractId);
                        entity.setSubContractId(0);
                        entity.setProjectId(projectId);
                        entity.setDeviceId(deviceId);
                        return Pair.of(pricePeriod, entity);
                    }).toList();
                }).flatMap(Collection::stream).toList();
        if (CollectionUtils.isNotEmpty(pricePeriodPairs)) {
            result &= pricePeriodService.saveBatch(pricePeriodPairs.stream().map(Pair::getRight).toList());
        }

        // 保存子合同单付款周期
        List<Pair<ContractPaymentPeriodParam, ContractPaymentPeriodEntity>> paymentPeriodPairs = devicePairs.stream()
                .filter(pair -> CollectionUtils.isNotEmpty(pair.getLeft().getPaymentPeriods()))
                .map(pair -> {
                    Integer deviceId = pair.getRight().getId();
                    Integer projectId = pair.getRight().getProjectId();
                    return pair.getLeft().getPaymentPeriods().stream().map(paymentPeriod -> {
                        ContractPaymentPeriodEntity entity = contractConvert.toEntity(paymentPeriod);
                        entity.setContractId(contractId);
                        entity.setSubContractId(0);
                        entity.setProjectId(projectId);
                        entity.setDeviceId(deviceId);
                        return Pair.of(paymentPeriod, entity);
                    }).toList();
                }).flatMap(Collection::stream).toList();
        if (CollectionUtils.isNotEmpty(paymentPeriodPairs)) {
            List<ContractPaymentPeriodEntity> paymentPeriodEntities = paymentPeriodPairs.stream().map(Pair::getRight).toList();
            // 补全供应商、银行信息
            fillingSupplierAndBank(paymentPeriodEntities);
            result &= paymentPeriodService.saveBatch(paymentPeriodEntities);
        }

        return result;
    }


    /**
     * 保存子合同信息
     *
     * @param contract     合同
     * @param projectPairs 项目信息
     * @return true:保存成功
     */
    private boolean saveSubContract(ContractEntity contract, List<Pair<ContractProjectParam, ContractProjectEntity>> projectPairs) {
        // 分步保存子合同信息
        boolean result = true;
        Integer mainContractId = contract.getId();

        // 子合同信息
        AtomicInteger globalIndex = new AtomicInteger(1);
        List<Pair<SubContractParam, ContractSubEntity>> subContractPairs = projectPairs.stream()
                .filter(pair -> CollectionUtils.isNotEmpty(pair.getLeft().getSubContracts()))
                .map(pair -> {
                    Integer projectId = pair.getRight().getId();
                    return pair.getLeft().getSubContracts().stream().map(subContract -> {
                        int index = globalIndex.getAndIncrement();
                        ContractSubEntity entity = contractConvert.toEntity(subContract);
                        entity.setContractId(mainContractId);
                        entity.setProjectId(projectId);
                        entity.setApplyCode(String.format("%s-%02d", contract.getApplyCode(), index));
                        return Pair.of(subContract, entity);
                    }).toList();
                }).flatMap(Collection::stream).toList();
        if (CollectionUtils.isEmpty(subContractPairs)) return true;
        result &= contractSubService.saveBatch(subContractPairs.stream().map(Pair::getRight).toList());

        // 保存子合同供应商信息
        subContractPairs.stream().filter(pair -> CollectionUtils.isNotEmpty(pair.getLeft().getSuppliers()))
                .forEach(pair -> saveSuppliers(contract, pair.getRight().getId(), pair.getRight()
                        .getProjectId(), pair.getLeft().getSuppliers()));

        // 保存子合同单价周期 & 付款周期
        List<ContractPricePeriodEntity> pricePeriods = Lists.newArrayList();
        List<ContractPaymentPeriodEntity> paymentPeriods = Lists.newArrayList();
        subContractPairs.stream()
                .filter(pair -> CollectionUtils.isNotEmpty(pair.getLeft().getFees()))
                .forEach(pair -> {
                    Integer subContractId = pair.getRight().getId();
                    Integer projectId = pair.getRight().getProjectId();

                    // 会有多个费用类型
                    for (SubContractFeeParam fee : pair.getLeft().getFees()) {
                        String feeType = fee.getFeeType();
                        if (CollectionUtils.isNotEmpty(fee.getPricePeriods())) {
                            pricePeriods.addAll(fee.getPricePeriods().stream().map(pricePeriod -> {
                                ContractPricePeriodEntity entity = contractConvert.toEntity(pricePeriod);
                                entity.setFeeType(feeType);
                                entity.setContractId(mainContractId);
                                entity.setSubContractId(subContractId);
                                entity.setProjectId(projectId);
                                entity.setDeviceId(0);
                                return entity;
                            }).toList());
                        }

                        if (CollectionUtils.isNotEmpty(fee.getPaymentPeriods())) {
                            paymentPeriods.addAll(fee.getPaymentPeriods().stream().map(paymentPeriod -> {
                                ContractPaymentPeriodEntity entity = contractConvert.toEntity(paymentPeriod);
                                entity.setFeeType(feeType);
                                entity.setContractId(mainContractId);
                                entity.setSubContractId(subContractId);
                                entity.setProjectId(projectId);
                                entity.setDeviceId(0);
                                return entity;
                            }).toList());
                        }
                    }
                });
        if (CollectionUtils.isNotEmpty(pricePeriods)) {
            result &= pricePeriodService.saveBatch(pricePeriods);
        }

        if (CollectionUtils.isNotEmpty(paymentPeriods)) {
            // 补全供应商、银行信息
            fillingSupplierAndBank(paymentPeriods);
            result &= paymentPeriodService.saveBatch(paymentPeriods);
        }
        return result;
    }

    /**
     * 保存合同操作日志
     */
    private boolean saveContractLog(Integer contractId, Consumer<ContractLogEntity> consumer) {
        ContractLogEntity entity = new ContractLogEntity();
        entity.setContractId(contractId);
        Optional.ofNullable(consumer).ifPresent(c -> c.accept(entity));
        return contractLogService.save(entity);
    }

    /**
     * 批量保存合同操作日志
     */
    private boolean batchSaveContractLog(List<Integer> contractIds, Consumer<ContractLogEntity> consumer) {
        List<ContractLogEntity> entities = contractIds.stream().map(contractId -> {
            ContractLogEntity entity = new ContractLogEntity();
            entity.setContractId(contractId);
            Optional.ofNullable(consumer).ifPresent(c -> c.accept(entity));
            return entity;
        }).toList();
        return contractLogService.saveBatch(entities);
    }

    /**
     * 生成申请编号
     * WYHTSQ+年月日+流水号（4位，从0001开始）
     * WYHTSQ202412010001
     */
    private String getApplyCode() {
        String today = DATE_FORMATTER.format(LocalDate.now());
        String cacheKey = "cms:venue:apply:code:" + today;
        Long index = stringRedisTemplate.opsForValue().increment(cacheKey);
        stringRedisTemplate.expire(cacheKey, 24, TimeUnit.HOURS);
        return String.format("WYHTSQ%s%04d", today, index);
    }

    /**
     * 生成补充协议申请编号
     * BCXY+年月日+流水号（4位，从0001开始）
     * BCXY202412010001
     */
    private String getContractAgreementApplyCode() {
        String today = DATE_FORMATTER.format(LocalDate.now());
        String cacheKey = "cms:venue:agreement:apply:code:" + today;
        Long index = stringRedisTemplate.opsForValue().increment(cacheKey);
        stringRedisTemplate.expire(cacheKey, 24, TimeUnit.HOURS);
        return String.format("BCXY%s%04d", today, index);
    }

    /**
     * 生成变更申请编号
     * BGSQ+年月日+流水号（4位，从0001开始）
     * BGSQ202412010001
     */
    private String getContractAmendmentApplyCode() {
        String today = DATE_FORMATTER.format(LocalDate.now());
        String cacheKey = "cms:venue:amendment:apply:code:" + today;
        Long index = stringRedisTemplate.opsForValue().increment(cacheKey);
        stringRedisTemplate.expire(cacheKey, 24, TimeUnit.HOURS);
        return String.format("BGSQ%s%04d", today, index);
    }

    /**
     * 生成合同编号
     * CS-省-城市-年月-流水号（5位）。例如：CS-SD-QD-2409-00025
     */
    private String getContractCode(String areaName, Integer cityId) {
        String yearMonth = YEAR_MONTH_FORMATTER.format(LocalDate.now());
        String cacheKey = "cms:venue:contract:code:" + yearMonth;
        Long index = stringRedisTemplate.opsForValue().increment(cacheKey);
        stringRedisTemplate.expire(cacheKey, 35, TimeUnit.DAYS);

        // 获取省份及城市编码（优先使用权限系统配置的省和城市编码）
        String provinceCode = null, cityCode = null;
        if (Objects.nonNull(cityId) && cityId > 0) {
            CityDetailVO cityDetail = Optional.ofNullable(feignAuthorityRpc.getCityDetail(cityId))
                    .map(ResultTemplate::getData).orElse(null);
            if (Objects.nonNull(cityDetail)) {
                provinceCode = cityDetail.getProvinceCode();
                cityCode = cityDetail.getCityCode();
            }
        }

        // 四川省,成都市,青羊区（如果权限系统未配置，使用拼音首字母）
        if (StringUtils.isBlank(provinceCode) || StringUtils.isBlank(cityCode)) {
            areaName = areaName.replaceAll("省", "").replaceAll("市", "");
            String[] areas = StringUtils.split(areaName, ',');
            provinceCode = StringUtils.getPinyinInitial(areas[0], 3);
            cityCode = StringUtils.getPinyinInitial(areas[1], 3);
        }

        return String.format("CS-%s-%s-%s-%05d", provinceCode, cityCode, yearMonth, index);
    }


    @Transactional
    public Integer createOrUpdateContractAgreement(boolean submit, ContractAgreementAddParam contractAgreementAddParam) {
        // 处理代理商合同信息
        CachedUser cachedUser = UserThreadLocal.getUser();
        UserTypeEnum userType = Optional.ofNullable(cachedUser).map(CachedUser::getType).orElse(UserTypeEnum.INNER);

        // 0. 转换合同信息
        ContractEntity contract = toContract(submit, contractAgreementAddParam, userType);
        log.info("补充协议申请单({}), 合同类型:{}, 提交人信息:{}", contract.getApplyCode(),
                BusinessTypeEnum.getDesc(contract.getBusinessType()), cachedUser);

        // 设置applyTime
        LocalDateTime applyTime = Optional.ofNullable(contractService.getOne(new LambdaQueryWrapper<ContractEntity>()
                        .eq(ContractEntity::getApplyCode, contract.getApplyCode())
                        .ne(ContractEntity::getApplyTime, LocalDateTime.of(1900, 1, 1, 0, 0, 0))
                        .eq(ContractEntity::getDeleteFlag, 0)
                        .select(ContractEntity::getApplyTime))).map(ContractEntity::getApplyTime)
                .orElse(null);

        // 填充代理商名称
        if (Objects.nonNull(contractAgreementAddParam.getAgentId())) {
            Optional.ofNullable(agentService.getById(contractAgreementAddParam.getAgentId()))
                    .ifPresent(agent -> contract.setAgentName(agent.getAgentName()));
        }

        setProjectAiLevel(contractAgreementAddParam);

        setContractLevel(contractAgreementAddParam, contract);

        // 合同关键信息有没有变更
        boolean isResubmit = checkNeedResubmit(contract);
        log.info("补充协议合同关键信息有没有变更:{},合同信息:{}", isResubmit, JSON.toJSONString(contract));

        boolean result = Objects.isNull(contract.getId()) || contract.getId() <= 0
                ? contractService.save(contract)
                : contractService.updateById(contract);
        if (!result || Objects.isNull(contract.getId()) || contract.getId() <= 0) {
            throw new CommonException("保存合同基本信息失败");
        }

        // 2. 先删除所有嵌套信息
        result = deleteAllExtend(contract);

        // 3. 保存供应商 & 银行信息
        result &= saveSuppliers(contract, 0, 0, contractAgreementAddParam.getSuppliers());

        // 4. 保存项目信息 & 级联信息 (价格申请、设备信息、设备位置、设备价格、设备支付)
        // top楼宇值
        if (submit) {
            for (ContractProjectParam project : contractAgreementAddParam.getProjects()) {
                CmsResult<String> topLevel = feignMethH5Rpc.topLevel(project.getProjectCode());
                project.setTopLevel(topLevel.getData());
            }

        }
        result &= saveProjects(contract, contractAgreementAddParam.getProjects());

        // 保存附件
        result &= saveContractAttachments(contract, contractAgreementAddParam.getAttachments());

        // 提交审批，通知其它系统
        if (result && submit) {
            submitContractAgreement(contract.getId(), Objects.equals(userType, UserTypeEnum.INNER), isResubmit);
            if (Objects.isNull(applyTime)) {
                contract.setApplyTime(LocalDateTime.now());
            }
            String applyStatus = contract.getApplyStatus();
            if (StringUtils.equals(applyStatus, ContractApplyStatusEnum.RETURN.getCode())) {
                contract.setApplyStatus(ContractApplyStatusEnum.SUBMIT.getCode());
            }
            contractService.updateById(contract);

            // 每次提交后 保存快照
            ContractSnapshotParam contractSnapshotParam = new ContractSnapshotParam();
            contractSnapshotParam.setSourceType(ContractSnapshotSourceTypeEnum.INITIATE_AGAIN.getCode());
            contractSnapshotParam.setContractId(contract.getId());
            contractSnapshotService.saveSnapshot(contractSnapshotParam);
        }

        // 返回合同ID
        if (result) return contract.getId();

        // 抛出异常, 回滚数据
        throw new CommonException("保存合同信息失败");
    }

    @Transactional
    public Integer updateContract(ContractEditParam contractEditParam) {
        ContractEntity contractEntity = Optional.ofNullable(contractService.getById(contractEditParam.getId()))
                .orElseThrow(() -> new CommonException(String.format("合同不存在[%s]", contractEditParam.getId())));
        ContractEntity contract = contractConvert.toEntity(contractEditParam, contractEntity);

        // 填充代理商名称
        if (Objects.nonNull(contractEditParam.getAgentId())) {
            Optional.ofNullable(agentService.getById(contractEditParam.getAgentId()))
                    .ifPresent(agent -> contract.setAgentName(agent.getAgentName()));
        }

        boolean result = contractService.updateById(contract);
        if (!result) {
            throw new CommonException("保存合同基本信息失败");
        }
        Map<String, Integer> map = getFirstSignCountMap(contract.getId());
        // 2. 先删除所有嵌套信息
        result = deleteAllExtend(contract);

        // 3. 保存供应商 & 银行信息
        result &= saveSuppliers(contract, 0, 0, contractEditParam.getSuppliers());

        // 4. 保存项目信息 & 级联信息 (价格申请、设备信息、设备位置、设备价格、设备支付)
        result &= saveHistoryProjects(contract, contractEditParam.getProjects(), map);

        // 保存附件
        result &= saveContractAttachments(contract, contractEditParam.getAttachments());

        // 返回合同ID
        if (result) return contract.getId();

        // 抛出异常, 回滚数据
        throw new CommonException("保存合同信息失败");
    }

    /**
     * 获取合同下项目对应的初始签约数
     *
     * @param contractId
     * @return
     */
    private Map<String, Integer> getFirstSignCountMap(Integer contractId) {
        Map<String, Integer> map = projectService.lambdaQuery()
                .select(ContractProjectEntity::getProjectName, ContractProjectEntity::getFirstSignCount)
                .eq(ContractProjectEntity::getContractId, contractId)
                .list().stream()
                .collect(Collectors.toMap(ContractProjectEntity::getProjectName,
                        ContractProjectEntity::getFirstSignCount));
        return map;
    }

    /**
     * 合同编辑 - 添加日志记录
     */
    public Integer updateContractWithLog(ContractEditParam contractEditParam) {
        // 获取用户ID
        AtomicInteger userIdHolder = new AtomicInteger();
        try {
            userIdHolder.set(UserThreadLocal.getUserId());
        } catch (Exception e) {
            log.error("获取用户ID失败", e);
        }
        // 合同修改前记录日志
        AtomicReference<ContractDetailVO> beforeUpdateVOHolder = new AtomicReference<>();
        try {
            beforeUpdateVOHolder.set(contractReadService.getContractHistoricDetail(contractEditParam.getId()));
        } catch (Exception e) {
            log.error("[id={}]合同编辑操作日志记录失败！", contractEditParam.getId(), e);
        }

        // 修改合同信息
        Integer contractId = selfBean.updateContract(contractEditParam);
        try {
            changeSubContractOnHistoryContractEdit(contractId);
        } catch (Exception e) {
            log.error("[id={}]合同编辑操作日志记录失败！", contractEditParam.getId(), e);
        }

        // 记录日志
        CompletableFuture.runAsync(() -> {
            AtomicReference<ContractDetailVO> afterUpdateVOHolder = new AtomicReference<>();
            try {
                afterUpdateVOHolder.set(contractReadService.getContractHistoricDetail(contractEditParam.getId()));
                contractLogService.saveCreateOrRemoveLog(contractEditParam.getId(), "合同编辑。",
                        ContractHistoryTypeEnum.CONTRACT_EDIT, userIdHolder.get(),
                        List.of(beforeUpdateVOHolder.get()), List.of(afterUpdateVOHolder.get()));
            } catch (Exception e) {
                log.error("[id={}]合同编辑操作日志记录失败！\n修改前：{}\n修改后：{}", contractEditParam.getId(),
                        beforeUpdateVOHolder.get(), afterUpdateVOHolder.get(), e);
            }
        }, threadPoolExecutor);

        return contractId;
    }

    @Transactional
    public void deleteContractAgreementById(Integer agreementId) {
        ContractEntity contractEntity = contractService.getById(agreementId);
        if (contractEntity == null) {
            return;
        }
        ;
        if (!ContractTypeEnum.AGREEMENT.getCode().equals(contractEntity.getContractType())) {
            throw new CommonException("非补充协议id");
        }
        if (!"0025-1".equals(contractEntity.getApplyStatus())) {
            throw new CommonException("非补充协议草稿");
        }
        deleteAllExtend(contractEntity);
        contractService.removeById(contractEntity);
    }

//    @Async
    public void fixProjectInfo(Boolean force) {
        // 补全所有项目
        List<ContractProjectEntity> projectEntities = projectService.lambdaQuery()
                .list();
        if (CollectionUtils.isEmpty(projectEntities)) {
            return;
        }

        //查找合同
        Map<Integer, ContractEntity> contractEntityMap = contractService.lambdaQuery()
                .eq(ContractEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .list().stream().peek(contractEntity -> {
                    contractEntity.setAiBuildingLevel("");
                    contractEntity.setBuildingLevel("");
                }).collect(Collectors.toMap(ContractEntity::getId, contractEntity -> contractEntity));

        for (ContractProjectEntity projectEntity : projectEntities) {
            if (StringUtils.isBlank(projectEntity.getProjectCode())) {
                continue;
            }
            mehtUtils.fixProjectLevelInfo(projectEntity, contractEntityMap, force);
        }
        List<List<ContractProjectEntity>> pLists = Lists.partition(projectEntities, 500);
        pLists.forEach(projectService::updateBatchById);

        List<List<ContractEntity>> cLists =Lists.partition(contractEntityMap.values().stream().toList(), 500);
        cLists.forEach(contractService::updateBatchById);
    }

    public void fixProjectInfoAll() {

        Set<Integer> set = Sets.newHashSet(ContractTypeEnum.HISTORY.getCode());
        List<Integer> contractIds = contractService.lambdaQuery()
                .select(ContractEntity::getId)
                .in(ContractEntity::getContractType, set)
                .list().stream().map(ContractEntity::getId).toList();

        List<ContractProjectEntity> projectEntities = projectService.lambdaQuery()
                .in(ContractProjectEntity::getContractId, contractIds)
                .list();
        if (CollectionUtils.isEmpty(projectEntities)) {
            return;
        }

        Set<String> collect = projectEntities.stream().map(ContractProjectEntity::getProjectCode)
                .collect(Collectors.toSet());
        BasicInfoMetaAllParam basicInfoMetaAllParam = new BasicInfoMetaAllParam();
        basicInfoMetaAllParam.setCodes(collect);
        ResultTemplate<List<BasicInfoAllVO>> listResultTemplate = feignCrmClient.basicInfoAllBuildMeta(basicInfoMetaAllParam);

        if (CollectionUtils.isEmpty(listResultTemplate.getData())) {
            log.info("data.basicInfoAllBuildMeta 返回为空");
            return;
        }
        Map<String, BasicInfoAllVO> basicInfoAllVOMap = listResultTemplate.getData().stream()
                .collect(Collectors.toMap(BasicInfoAllVO::getBuildingRatingNo, e -> e, (v1, v2) -> v1));

        for (ContractProjectEntity projectEntity : projectEntities) {
            if (StringUtils.isBlank(projectEntity.getProjectCode())) {
                continue;
            }
            BasicInfoAllVO data = basicInfoAllVOMap.get(projectEntity.getProjectCode().split("-")[0]);
            if (Objects.isNull(data)) {
                continue;
            }

            String areaName = data.getMapProvince() + "," + data.getMapCity() + "," + data.getMapRegion();
            projectEntity.setAreaName(areaName);
            projectEntity.setAddress(data.getMapAddress());
//            projectEntity.setPropertyType(Optional.ofNullable(StaticMapUtil.propertyMap.get(data.getBuildingType()))
//                    .map(list -> list.get(0)).orElse(""));
            projectEntity.setLevel(org.apache.commons.lang3.StringUtils.isNotBlank(data.getProjectLevel()) ?
                    data.getProjectLevel() : data.getProjectLevelAi());

        }

        projectService.updateBatchById(projectEntities);
    }

    public void fixContractLevel() {

        Set<Integer> set = Sets.newHashSet(ContractTypeEnum.HISTORY.getCode());
        List<ContractEntity> list = contractService.lambdaQuery()
                .select(ContractEntity::getId, ContractEntity::getBuildingLevel)
                .in(ContractEntity::getContractType, set)
                .list();

        List<Integer> ids = list.stream().map(ContractEntity::getId).toList();

        List<ContractProjectEntity> projectEntities = projectService.lambdaQuery()
                .in(ContractProjectEntity::getContractId, ids)
                .list();

        if (CollectionUtils.isEmpty(projectEntities)) {
            return;
        }

        Map<Integer, List<ContractProjectEntity>> collect = projectEntities.stream()
                .collect(Collectors.groupingBy(ContractProjectEntity::getContractId));

        for (ContractEntity contractEntity : list) {
            List<ContractProjectEntity> contractProjectEntities = collect.get(contractEntity.getId());

            if (CollectionUtils.isEmpty(contractProjectEntities)) {
                continue;
            }

            List<String> levelList = contractProjectEntities.stream().map(ContractProjectEntity::getLevel)
                    .filter(StringUtils::isNotBlank)
                    .sorted().collect(Collectors.toList());

            if (CollectionUtils.isEmpty(levelList)) {
                continue;
            }

            contractService.lambdaUpdate()
                    .set(ContractEntity::getBuildingLevel, levelList.get(levelList.size() - 1))
                    .eq(ContractEntity::getId, contractEntity.getId())
                    .update();
        }
    }


    /**
     * 比较并保存修改日志
     */
    public void contractCompareAndSaveLog(ContractEntity source, ContractEntity target) {
        if (Objects.isNull(source) || Objects.isNull(target)) {
            return;
        }

        CompletableFuture.runAsync(() -> {
            try {
                // 获取对象差异
                var sourceVO = contractConvert.toDetailVO(source);
                var targetVO = contractConvert.toDetailVO(target);
                converterFactory.convert(List.of(sourceVO, targetVO));
                List<ExpandChangedItem> ChangedItems = ChangeExtractor.extract(sourceVO, targetVO, null, true);
                if (CollectionUtils.isEmpty(ChangedItems)) {
                    log.info("[id={}]的原始合同没有需要更新的数据", sourceVO.getId());
                    return;
                }
                // 保存修改日志
                contractLogService.saveChangedLog(source.getId(), "合同编辑。",
                        ContractHistoryTypeEnum.CONTRACT.getCode(), null, ChangedItems);
            } catch (Exception e) {
                log.error("保存合同修改日志失败，source: {}, target: {}", source, target);
            }
        }, threadPoolExecutor);
    }

    /**
     * 合同id数组
     *
     * @param ids
     */
    @Transactional
    public void deleteContracts(List<Integer> ids) {
        contractService.removeByIds(ids);
        deleteProjects(ids);
    }

    /**
     * 合同id数组
     *
     * @param ids
     */
    @Transactional
    public void deleteProjects(List<Integer> ids) {

        List<Integer> projectIds = projectService.lambdaQuery().select(ContractProjectEntity::getId)
                .in(ContractProjectEntity::getContractId, ids)
                .list()
                .stream()
                .map(ContractProjectEntity::getId)
                .toList();
        projectService.removeByIds(projectIds);

        List<Integer> applyIds = contractPriceApplyServiceImpl.lambdaQuery().select(ContractPriceApplyEntity::getId)
                .in(ContractPriceApplyEntity::getContractId, ids)
                .list()
                .stream()
                .map(ContractPriceApplyEntity::getId)
                .toList();
        contractPriceApplyServiceImpl.removeByIds(applyIds);

        List<Integer> deviceIds = contractDeviceServiceImpl.lambdaQuery().select(ContractDeviceEntity::getId)
                .in(ContractDeviceEntity::getContractId, ids)
                .list()
                .stream()
                .map(ContractDeviceEntity::getId)
                .toList();
        contractDeviceServiceImpl.removeByIds(deviceIds);

        List<Integer> devicePointIds = contractDevicePointServiceImpl.lambdaQuery()
                .select(ContractDevicePointEntity::getId)
                .in(ContractDevicePointEntity::getContractId, ids)
                .list()
                .stream()
                .map(ContractDevicePointEntity::getId)
                .toList();
        contractDevicePointServiceImpl.removeByIds(devicePointIds);

        List<Integer> pricePeriodIds = contractPricePeriodServiceImpl.lambdaQuery()
                .select(ContractPricePeriodEntity::getId)
                .in(ContractPricePeriodEntity::getContractId, ids)
                .list()
                .stream()
                .map(ContractPricePeriodEntity::getId)
                .toList();
        contractPricePeriodServiceImpl.removeByIds(pricePeriodIds);

        List<Integer> payPeriodIds = contractPaymentPeriodServiceImpl.lambdaQuery()
                .select(ContractPaymentPeriodEntity::getId)
                .in(ContractPaymentPeriodEntity::getContractId, ids)
                .list()
                .stream()
                .map(ContractPaymentPeriodEntity::getId)
                .toList();
        contractPaymentPeriodServiceImpl.removeByIds(payPeriodIds);

        List<Integer> supplierIds = contractSupplierServiceImpl.lambdaQuery().select(ContractSupplierEntity::getId)
                .in(ContractSupplierEntity::getContractId, ids)
                .list()
                .stream()
                .map(ContractSupplierEntity::getId)
                .toList();
        contractSupplierServiceImpl.removeByIds(supplierIds);

        List<Integer> supplierBankIds = contractSupplierBankServiceImpl.lambdaQuery()
                .select(ContractSupplierBankEntity::getId)
                .in(ContractSupplierBankEntity::getContractId, ids)
                .list()
                .stream()
                .map(ContractSupplierBankEntity::getId)
                .toList();
        contractSupplierBankServiceImpl.removeByIds(supplierBankIds);
    }

    /**
     * 审批通过后，将项目的点位数设置到项目表，以后永远不会更改这个字段
     *
     * @param contractId
     */
    public void initialProjectFirstSignCount(Integer contractId) {
        List<ContractProjectEntity> projectEntities = projectService.lambdaQuery()
                .eq(ContractProjectEntity::getContractId, contractId)
                .list();
        if (CollectionUtils.isEmpty(projectEntities)) {
            return;
        }
        Set<Integer> projectIds = projectEntities
                .stream().map(ContractProjectEntity::getId)
                .collect(Collectors.toSet());
        Map<Integer, IntSummaryStatistics> map = deviceService.lambdaQuery()
                .select(ContractDeviceEntity::getProjectId, ContractDeviceEntity::getSignCount)
                .in(ContractDeviceEntity::getProjectId, projectIds)
                .list().stream()
                .collect(Collectors.groupingBy(ContractDeviceEntity::getProjectId,
                        Collectors.summarizingInt(ContractDeviceEntity::getSignCount)));
        if (MapUtils.isEmpty(map)) {
            return;
        }
        IntSummaryStatistics intSummaryStatistics = new IntSummaryStatistics();
        for (ContractProjectEntity projectEntity : projectEntities) {
            if (!map.containsKey(projectEntity.getId())) {
                projectEntities.remove(projectEntity);
            } else {
                projectEntity.setFirstSignCount((int) map.getOrDefault(projectEntity.getId(), intSummaryStatistics)
                        .getSum());
            }
        }
        projectService.updateBatchById(projectEntities);
    }


    public void initialProjectsFirstSignCount() {
        Set<Integer> contractIds = projectService.lambdaQuery()
                .select(ContractProjectEntity::getContractId)
                .eq(ContractProjectEntity::getFirstSignCount, 0)
                .list().stream().map(ContractProjectEntity::getContractId)
                .collect(Collectors.toSet());

        contractIds.forEach(this::initialProjectFirstSignCount);
    }

    @Transactional
    public Integer createOrUpdateContractAmendment(boolean submit, ContractAmendmentApplyParam contractAmendmentApplyParam) {
        // 处理代理商合同信息
        CachedUser cachedUser = UserThreadLocal.getUser();
        UserTypeEnum userType = Optional.ofNullable(cachedUser).map(CachedUser::getType).orElse(UserTypeEnum.INNER);

        // 0. 转换合同信息
        ContractEntity contract = toContract(submit, contractAmendmentApplyParam, userType);
        log.info("合同变更申请单({}), 合同类型:{}, 提交人信息:{}", contract.getApplyCode(),
                BusinessTypeEnum.getDesc(contract.getBusinessType()), cachedUser);

        // 获取代理商审核人
        if (Objects.equals(contract.getBusinessType(), BusinessTypeEnum.AGENT.getCode())
                && Objects.equals(userType, UserTypeEnum.AGENT)
                && Objects.nonNull(cachedUser) && StringUtils.isNotBlank(cachedUser.getWno())) {

            SysUserApproveVO approveUser = Optional.ofNullable(feignMethWebRpc.getUserDetail(cachedUser.getWno()))
                    .map(ResultTemplate::getData).orElse(null);
            log.info("代理合同变更申请单({}), 审批人信息:{}", contract.getApplyCode(), approveUser);
            contract.setAgentApprover(Objects.isNull(approveUser) ? agentApprovalDefaultUser : approveUser.getContractApplyCode());
        }

        // 设置applyTime
        LocalDateTime applyTime = Optional.ofNullable(contractService.getOne(new LambdaQueryWrapper<ContractEntity>()
                        .eq(ContractEntity::getApplyCode, contract.getApplyCode())
                        .ne(ContractEntity::getApplyTime, LocalDateTime.of(1900, 1, 1, 0, 0, 0))
                        .eq(ContractEntity::getDeleteFlag, 0)
                        .select(ContractEntity::getApplyTime))).map(ContractEntity::getApplyTime)
                .orElse(null);

        // 填充代理商名称
        if (Objects.nonNull(contractAmendmentApplyParam.getAgentId())) {
            Optional.ofNullable(agentService.getById(contractAmendmentApplyParam.getAgentId()))
                    .ifPresent(agent -> contract.setAgentName(agent.getAgentName()));
        }

        setProjectAiLevel(contractAmendmentApplyParam);

        setContractLevel(contractAmendmentApplyParam, contract);

        boolean isResubmit = checkNeedResubmit(contract);

        log.info("合同变更申请单,是否需要重新提交:{},合同信息：{}", isResubmit, JSON.toJSONString(contract));
        boolean result = Objects.isNull(contract.getId()) || contract.getId() <= 0
                ? contractService.save(contract)
                : contractService.updateById(contract);
        if (!result || Objects.isNull(contract.getId()) || contract.getId() <= 0)
            throw new CommonException("保存合同基本信息失败");

        // 2. 先删除所有嵌套信息
        result = deleteAllExtend(contract);

        // 保存变更类型
        result &= saveContractChangeType(contract.getId(), contractAmendmentApplyParam.getChangeType(),
                contractAmendmentApplyParam.getSubChangeType());

        // 3. 保存供应商 & 银行信息
        result &= saveSuppliers(contract, 0, 0, contractAmendmentApplyParam.getSuppliers());

        // 4. 保存项目信息 & 级联信息 (价格申请、设备信息、设备位置、设备价格、设备支付)
        result &= saveProjects(contract, contractAmendmentApplyParam.getProjects());

        // 保存附件
        result &= saveContractAttachments(contract, contractAmendmentApplyParam.getAttachments());

        // 保存终止信息
        result &= saveContractAmendmentTermination(contract, contractAmendmentApplyParam.getTermination());

        // 提交审批，通知其它系统
        if (result && submit) {
            submitContractAmendment(contract.getId(), Objects.equals(userType, UserTypeEnum.INNER), isResubmit);
            if (Objects.isNull(applyTime)) {
                contract.setApplyTime(LocalDateTime.now());
            }
            String applyStatus = contract.getApplyStatus();
            if (StringUtils.equals(applyStatus, ContractApplyStatusEnum.RETURN.getCode())) {
                contract.setApplyStatus(ContractApplyStatusEnum.SUBMIT.getCode());
            }
            contractService.updateById(contract);

            // 每次提交后 保存快照
            ContractSnapshotParam contractSnapshotParam = new ContractSnapshotParam();
            contractSnapshotParam.setSourceType(ContractSnapshotSourceTypeEnum.INITIATE_AGAIN.getCode());
            contractSnapshotParam.setContractId(contract.getId());
            contractSnapshotService.saveSnapshot(contractSnapshotParam);
        }

        // 返回合同ID
        if (result) return contract.getId();

        // 抛出异常, 回滚数据
        throw new CommonException("保存合同信息失败");
    }

    /**
     * 保存终止信息
     *
     * @param contract
     * @param terminationParam
     * @return
     */
    private boolean saveContractAmendmentTermination(ContractEntity contract, ContractAmendmentTerminationParam terminationParam) {
        if (!ContractTypeEnum.AMENDMENT.getCode().equals(contract.getContractType())) {
            return true;
        }
        if (Objects.isNull(terminationParam)) {
            return true;
        }
        ContractAmendmentTerminationEntity terminationEntity = contractConvert.toEntity(terminationParam);
        terminationEntity.setContractId(contract.getId());
        return contractAmendmentTerminationService.save(terminationEntity);
    }

    /**
     * @param contractId    合同id
     * @param changeType    一级变更类型
     * @param subChangeType 二级变更类型
     * @return
     */
    private boolean saveContractChangeType(Integer contractId, String changeType, List<String> subChangeType) {
        if (Objects.isNull(contractId)) {
            log.error("保存变更类型:contractId为空");
            return false;
        }
        if (StringUtils.isBlank(changeType)) {
            log.error("保存变更类型:changeType为空");
            return false;
        }
        if (CollectionUtils.isEmpty(subChangeType)) {
            log.error("保存变更类型:subChangeType为空");
            return false;
        }
        List<ContractAmendmentChangeTypeEntity> changeTypeEntities = new ArrayList<>(subChangeType.size());
        for (String s : subChangeType) {
            ContractAmendmentChangeTypeEntity changeTypeEntity = new ContractAmendmentChangeTypeEntity();
            changeTypeEntity.setChangeType(changeType);
            changeTypeEntity.setSubChangeType(s);
            changeTypeEntity.setContractId(contractId);
            changeTypeEntities.add(changeTypeEntity);
        }
        return contractAmendmentChangeTypeService.saveBatch(changeTypeEntities);
    }

    /**
     * 上传押金条
     *
     * @param contractId   合同id
     * @param depositParam 押金条附件
     * @return 保存结果
     */
    public Boolean uploadDeposit(@Min(value = 1, message = "合同ID不能小于1") Integer contractId, ContractCancelParam depositParam) {
        if (CollectionUtils.isEmpty(depositParam.getAttachments())) {
            return false;
        }

        // 验证是否有押金
        Boolean b = Optional.ofNullable(projectService.lambdaQuery()
                        .eq(ContractProjectEntity::getContractId, contractId)
                        .select(ContractProjectEntity::getDepositAmount)
                        .list())
                .map(list -> list.stream().anyMatch(e -> BigDecimal.ZERO.compareTo(e.getDepositAmount()) < 0))
                .orElse(false);
        if (!b) {
            throw new CommonException("[%s]合同不存在押金".formatted(contractId));
        }

        transactionUtils.doBatch(List.of(
                // 移除押金条附件
                () -> {
                    attachmentService.lambdaUpdate()
                            .eq(ContractAttachmentEntity::getContractId, contractId)
                            .eq(ContractAttachmentEntity::getType, AttachmentTypeEnum.MAIN_DEPOSIT.getCode())
                            .eq(ContractAttachmentEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                            .set(ContractAttachmentEntity::getDeleteFlag, BooleFlagEnum.YES.getCode())
                            .update();
                    return true;
                },
                // 保存押金条附件
                () -> attachmentService.saveBatch(depositParam.getAttachments().stream()
                        .map(contractConvert::toEntity)
                        .peek(attachment -> {
                            attachment.setContractId(contractId);
                            attachment.setBizId(contractId);
                            attachment.setType(AttachmentTypeEnum.MAIN_DEPOSIT.getCode());
                            attachment.setSubType(0);
                            attachment.setDeleteFlag(BooleFlagEnum.NO.getCode());
                        }).toList()),
                // 同步合同押金条的标识
                () -> contractService.lambdaUpdate()
                        .eq(ContractEntity::getId, contractId)
                        .set(ContractEntity::getDepositUploadFlag, BooleFlagEnum.YES.getCode())
                        .update()
        ), () -> "[%s]合同上传押金条失败".formatted(contractId));

        // 保存操作日志
        StringJoiner logMsg = new StringJoiner("。");
        logMsg.add("上传押金条。备注说明：%s".formatted(StringUtils.isBlank(depositParam.getRemark()) ? "无" : depositParam.getRemark()));
        String attachmentNames = depositParam.getAttachments().stream().map(ContractAttachmentParam::getName)
                .collect(Collectors.joining(","));
        if (StringUtils.isNotBlank(attachmentNames)) {
            logMsg.add("附件：%s".formatted(attachmentNames));
        }
        saveContractLog(contractId, contractLog -> contractLog.setContent(logMsg.toString()));

        return true;
    }

    /**
     * 更新目标合同对应原合同的 变更状态
     *
     * @param contractId
     */
    public void changeParentContractChangeFlag(Integer contractId) {
        if (Objects.isNull(contractId) || contractId <= 0) {
            return;
        }
        ContractEntity contractEntity = contractService.getById(contractId);
        if (Objects.isNull(contractEntity)) {
            return;
        }
        if (Objects.isNull(contractEntity.getParentId()) || contractEntity.getParentId() <= 0) {
            return;
        }
        // 设置change_flag
        contractService.lambdaUpdate()
                .eq(ContractEntity::getId, contractEntity.getParentId())
                .set(ContractEntity::getChangeFlag, ContractChangeFlagEnum.WAIT_ARCHIVE.getCode())
                .update();
    }

    /**
     * 修改合同状态为审批退回
     *
     * @param contractId
     */
    public boolean changeContractStatusOnApprovalReturned(Integer contractId) {
        ContractEntity contract = contractService.getById(contractId);
        if (Objects.isNull(contract)) {
            log.warn("[飞书审批] 修改状态为撤回，合同({})不存在", contractId);
            return false;
        }

        // 更新合同状态
        boolean result = contractService.lambdaUpdate().set(ContractEntity::getContractCode, null)
                .set(ContractEntity::getFormalFlag, BooleFlagEnum.NO.getCode())
                .set(ContractEntity::getApplyStatus, ContractApplyStatusEnum.RETURN.getCode())
                .set(ContractEntity::getFormalStatus, ContractStatusEnum.PENDING.getCode())
                .eq(ContractEntity::getId, contractId).update();

        // 记录合同状态变更 (审批撤回)
        if (result) {
            statusChangeLogService.recordContractStatusChange(contract.getId(), contract.getApplyCode(), ContractApplyStatusEnum.RETURN.getCode());
        }
        return result;
    }
}
