package com.coocaa.ad.cheese.cms.venue.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.fastjson2.JSON;
import com.alibaba.nacos.api.model.v2.Result;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractApprovalEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractDeviceEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractDevicePointEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractPricePeriodEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.LedgerEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.mapper.ContractSupplierMapper;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractApprovalService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractDevicePointService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractDeviceService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractPricePeriodService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.impl.ContractSupplierBankServiceImpl;
import com.coocaa.ad.cheese.cms.common.db.venue.service.impl.ContractSupplierServiceImpl;
import com.coocaa.ad.cheese.cms.common.db.venue.service.impl.LedgerServiceImpl;
import com.coocaa.ad.cheese.cms.common.rpc.FeignAuthorityRpc;
import com.coocaa.ad.cheese.cms.common.tools.common.constant.ContractConstants;
import com.coocaa.ad.cheese.cms.common.tools.common.enums.BooleFlagEnum;
import com.coocaa.ad.cheese.cms.common.tools.common.exception.CommonException;
import com.coocaa.ad.cheese.cms.common.tools.common.result.ResultTemplate;
import com.coocaa.ad.cheese.cms.common.tools.common.util.BigDecimalUtils;
import com.coocaa.ad.cheese.cms.common.tools.common.util.EasyExcelUtils;
import com.coocaa.ad.cheese.cms.common.tools.venue.bean.ContractPageDTO;
import com.coocaa.ad.cheese.cms.common.tools.venue.bean.ContractQueryDTO;
import com.coocaa.ad.cheese.cms.common.tools.venue.constant.VenueConstants;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ApprovalResultEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.BusinessTypeEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractAbnormalFlagEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractApplyStatusEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractStatusEnum;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.StampOrderEnum;
import com.coocaa.ad.cheese.cms.common.util.CodeNameHelper;
import com.coocaa.ad.cheese.cms.common.util.converter.ConverterFactory;
import com.coocaa.ad.cheese.cms.dataimport.service.FeignSspClient;
import com.coocaa.ad.cheese.cms.download.enums.DownLoadTypeEnum;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractPageWithPayPeriodDTO;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractPageWithoutPayPeriodDTO;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractQueryParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractSupplierAndBankDTO;
import com.coocaa.ad.cheese.cms.venue.bean.workorder.FilterBuildingNoDTO;
import com.coocaa.ad.cheese.cms.venue.convert.contract.ContractConvert;
import com.coocaa.ad.cheese.cms.venue.rpc.FeignMethH5Rpc;
import com.coocaa.ad.cheese.cms.venue.rpc.FeignWorkOrderRpc;
import com.coocaa.ad.cheese.cms.venue.vo.building.BuildingGeneVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ApplyContractExportVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractAgreementExportVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractApplyExportVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractExportVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ProjectExportVO;
import com.lark.oapi.core.utils.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 合同导出服务
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-09
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ContractExportService extends BaseContractService {
    private final ContractConvert contractConvert;
    private final ConverterFactory converterFactory;
    private final ContractFillingService contractFillingService;
    private final IContractService contractService;
    private final IContractDeviceService deviceService;
    private final IContractDevicePointService devicePointService;
    private final IContractPricePeriodService pricePeriodService;
    private final IContractApprovalService contractApprovalService;
    private final FeignWorkOrderRpc feignWorkOrderRpc;
    private final FeignSspClient feignSspClient;
    private final FeignMethH5Rpc feignMethH5Rpc;
    private final FeignAuthorityRpc authorityRpc;
    //    private final Set<Integer> redFields = Sets.newHashSet(34, 35, 36, 37, 38);
    private final int RED_CONDITION = 30;
    private final int APPLY_RED_CONDITION = 25;

    private final int RED_CONDITION_AGREEMENTS = 40;
    private final int RED_CONDITION_HISTORY = 40;
    private final LedgerService ledgerService;
    private final ContractSupplierBankServiceImpl contractSupplierBankServiceImpl;
    private final ContractSupplierServiceImpl contractSupplierServiceImpl;
    private final ContractSupplierMapper contractSupplierMapper;
    private final LedgerServiceImpl ledgerServiceImpl;
    private final CodeNameHelper codeNameHelper;
    private final RegionService regionService;

    // 是否大屏，参考项目->设备尺寸
    @Value("#{'${contract.venue.template.large-screen.dict-codes:}'.split(',')}")
    private Set<String> largeScreenSizeCodes;

    private static final Map<String, String> SIZE_SPEC_MAP = new HashMap<>(8);

    /**
     * 申请单导出
     */
    public String exportApplies(ContractQueryParam queryParam) {
        List<ContractPageDTO> applies = listContacts(null, queryParam);
        if (CollectionUtils.isEmpty(applies)) {
            return StringUtils.EMPTY;
        }
        setContractsCodeByParentId(applies);

        try {

            // 查找签约数
            List<Integer> ids = applies.stream().map(ContractPageDTO::getId).toList();
            Map<Integer, List<ContractDeviceEntity>> contractDeviceMap = deviceService.lambdaQuery().select(ContractDeviceEntity::getSignCount,
                            ContractDeviceEntity::getContractId)
                    .in(ContractDeviceEntity::getContractId, ids)
                    .list().stream().collect(Collectors.groupingBy(ContractDeviceEntity::getContractId));

            List<Integer> contractIds = ids.stream().distinct().toList();
            // 审批信息
            List<ContractApprovalEntity> approvalEntities = contractApprovalService.lambdaQuery().in(ContractApprovalEntity::getContractId, contractIds).list();
            // toMap
            Map<Integer, ContractApprovalEntity> approvalMap = approvalEntities.stream()
                    .collect(Collectors.toMap(ContractApprovalEntity::getContractId, e -> e, (v1, v2) -> {
                        log.warn("[id={}]的原始合同下发现了多余的审批记录，请注意核对！", v1.getContractId());
                        if (v1.getApprovalVersion() > v2.getApprovalVersion()) {
                            return v1;
                        }
                        return v2;
                    }));


            // 数据转换
            List<ContractApplyExportVO> vos = applies.stream()
                    .map(contractConvert::toApplyExportVO)
                    .peek(vo -> {
                        vo.setBusinessTypeName(BusinessTypeEnum.getDesc(vo.getBusinessType()));
                        vo.setNormalFlagName(BooleFlagEnum.getDesc(vo.getNormalFlag()));
                        vo.setApplyStatusName(ContractApplyStatusEnum.getDesc(vo.getApplyStatus()));
                        vo.setArchiveFlagName(BooleFlagEnum.getDesc(vo.getArchiveFlag()));
                        vo.setStopFlagName(BooleFlagEnum.getDesc(vo.getStopFlag()));
                        // 查找签约数
                        List<ContractDeviceEntity> contractDeviceEntities = contractDeviceMap.get(vo.getId());
                        int signCount = CollectionUtil.isEmpty(contractDeviceEntities) ? 0 : contractDeviceEntities.stream().mapToInt(ContractDeviceEntity::getSignCount).sum();
                        vo.setSignCount(signCount);

                        // 审批通过时间
                        ContractApprovalEntity contractApprovalEntity = approvalMap.get(vo.getId());
                        if (contractApprovalEntity != null && (ApprovalResultEnum.APPROVE.getCode().equals(contractApprovalEntity.getApprovalStatus()))) {
                            vo.setApprovedTime(contractApprovalEntity.getUpdateTime());
                        }

                        vo.setPartyAStampStatusName(BooleFlagEnum.getDesc(vo.getSealParty1Flag()));
                        vo.setPartyBStampStatusName(BooleFlagEnum.getDesc(vo.getSealParty2Flag()));
                        vo.setPartyCStampStatusName(BooleFlagEnum.getDesc(vo.getSealParty3Flag()));
                    })
                    .toList();
            // 填充字典、用户信息
            contractFillingService.fillingProject(vos);
            contractFillingService.fillingSupplier(vos);
            contractFillingService.fillingCity(vos);
            converterFactory.convert(vos);
            converterFactory.convert(vos);

            // 填充大区
            Map<String, String> cityReginMapping = regionService.getCityReginMapping();
            vos.forEach(vo -> vo.setRegion(cityReginMapping.get(vo.getCityName())));

            String date = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String fileName = String.format(DownLoadTypeEnum.HTSQD_LB.getDesc()+"_%s_%s.xlsx", date, System.currentTimeMillis());
            List<ApplyContractExportVO> list = vos.stream().map(contractConvert::toApplyContractExportVO).toList();

            // 填充大屏、设备尺寸信息
            fillingDeviceData(contractIds, list);

            return EasyExcelUtils.createExcelToCos("合同申请单", list,
                    Collections.emptyList(), fileName, "venue");
        } catch (CommonException ex) {
            log.warn("转换查询参数失败, 请求参数: {}", queryParam, ex);
            throw ex;
        } catch (Exception ex) {
            log.warn("导出申请单列表失败, 请求参数: {}", queryParam, ex);
        }
        return StringUtils.EMPTY;
    }

    /**
     * 填充大屏、设备尺寸信息
     */
    private void fillingDeviceData(List<Integer> contractIds, List<ApplyContractExportVO> vos) {
        Map<String, List<Map<String, Object>>> devicePointMap = getDevicePointByContractId(contractIds);
        if (MapUtils.isEmpty(devicePointMap)) {
            return;
        }
        // 翻译尺寸规格
        vos.forEach(vo -> setSizeSpecAndLargeScreen(devicePointMap, vo::getId, vo::setSizeSpec, vo::setLargeScreen));
    }

    /**
     * 填充大屏、设备尺寸信息
     */
    public void fillingDeviceData(List<? extends ContractExportVO> vos) {
        // 查询所有设备点位信息
        Map<String, List<Map<String, Object>>> devicePointMap = getDevicePointByContractId(vos.stream().map(ContractExportVO::getId).toList());
        if (MapUtils.isEmpty(devicePointMap)) {
            return;
        }
        // 翻译尺寸规格
        vos.forEach(vo -> setSizeSpecAndLargeScreen(devicePointMap, vo::getId, vo::setSizeSpec, vo::setLargeScreen));
    }

    /**
     * 查询所有设备点位信息
     */
    private Map<String, List<Map<String, Object>>> getDevicePointByContractId(List<Integer> contractIds) {
        if (CollectionUtils.isEmpty(contractIds)) {
            return Collections.emptyMap();
        }
        // 查询所有设备点位信息
        QueryWrapper<ContractDevicePointEntity> wrapper = new QueryWrapper<>();
        wrapper.select("contract_id", "size", "COUNT(1) AS count")
                .in("contract_id", contractIds)
                .groupBy("contract_id", "size");
        return devicePointService.getBaseMapper().selectMaps(wrapper).stream()
                .collect(Collectors.groupingBy(r -> String.valueOf(r.get("contract_id")), Collectors.toList()));
    }

    /**
     * 处理设备尺寸、是否大屏
     */
    private void setSizeSpecAndLargeScreen(Map<String, List<Map<String, Object>>> devicePointMap,
                                           Supplier<Integer> contractIdSupplier,
                                           Consumer<String> sizeSpecConsumer, Consumer<String> largeScreenConsumer) {
        List<Map<String, Object>> devicePoints = devicePointMap.get(String.valueOf(contractIdSupplier.get()));
        if (CollectionUtils.isEmpty(devicePoints)) {
            return;
        }
        Pair<String, String> pair = getSizeSpecAndLargeScreen(devicePoints);
        //
        sizeSpecConsumer.accept(pair.getLeft());
        largeScreenConsumer.accept(pair.getRight());
    }

    private Pair<String, String> getSizeSpecAndLargeScreen(List<Map<String, Object>> devicePoints) {
        if (MapUtils.isEmpty(SIZE_SPEC_MAP)) {
            Map<String, String> sizeDictMap = codeNameHelper.getCodeNameMapping("0013", authorityRpc::listDictByParent);
            if (MapUtils.isEmpty(sizeDictMap)) {
                return Pair.of(StringUtils.EMPTY, StringUtils.EMPTY);
            }
            SIZE_SPEC_MAP.putAll(sizeDictMap);
        }
        // 统计尺寸规格
        Map<String, Object> sizeCountMap = devicePoints.stream().collect(Collectors.toMap(
                r -> String.valueOf(r.getOrDefault("size", "")), r -> r.get("count")));
        StringJoiner sizeSpecJoiner = new StringJoiner(", ");
        AtomicBoolean isLargeScreen = new AtomicBoolean(false);
        SIZE_SPEC_MAP.forEach((sizeCode, sizeZh) -> {
            if (sizeCountMap.containsKey(sizeCode)) {
                sizeSpecJoiner.add("%s:%s台".formatted(sizeZh, sizeCountMap.get(sizeCode)));
                //
                if (!isLargeScreen.get() && largeScreenSizeCodes.contains(sizeCode)) {
                    isLargeScreen.set(true);
                }
            } else {
                sizeSpecJoiner.add("%s:%s台".formatted(sizeZh, 0));
            }
        });

        return Pair.of(sizeSpecJoiner.toString(), (isLargeScreen.get() ? BooleFlagEnum.YES : BooleFlagEnum.NO).getDesc());
    }

    /**
     * 设值原合同编号
     *
     * @param contracts
     */
    private void setContractsCodeByParentIdProjectExport(List<ContractPageWithoutPayPeriodDTO> contracts) {
        Set<Integer> parentIds = contracts.stream().map(ContractPageWithoutPayPeriodDTO::getParentId).filter(Objects::nonNull).collect(Collectors.toSet());
        List<ContractEntity> parentContractList = contractService.lambdaQuery().select(ContractEntity::getId, ContractEntity::getContractCode)
                .in(ContractEntity::getId, parentIds).list();
        Map<Integer, List<ContractEntity>> parentContractMap = parentContractList.stream().collect(Collectors.groupingBy(ContractEntity::getId));
        contracts.forEach(contract -> {
            List<ContractEntity> list = parentContractMap.get(contract.getParentId());
            if (CollectionUtils.isNotEmpty(list)) {
                contract.setParentCode(list.get(0).getContractCode());
            }
        });
    }

    /**
     * 设值原合同编号
     *
     * @param contracts
     */
    private void setContractsCodeByParentId(List<ContractPageDTO> contracts) {
        Set<Integer> parentIds = contracts.stream().map(ContractPageDTO::getParentId).filter(Objects::nonNull).collect(Collectors.toSet());
        List<ContractEntity> parentContractList = contractService.lambdaQuery().select(ContractEntity::getId, ContractEntity::getContractCode)
                .in(ContractEntity::getId, parentIds).list();
        Map<Integer, List<ContractEntity>> parentContractMap = parentContractList.stream().collect(Collectors.groupingBy(ContractEntity::getId));
        contracts.forEach(contract -> {
            List<ContractEntity> list = parentContractMap.get(contract.getParentId());
            if (CollectionUtils.isNotEmpty(list)) {
                contract.setParentCode(list.get(0).getContractCode());
            }
        });
    }


    /**
     * 5
     * 合同导出
     */
    public String exportContracts(ContractQueryParam queryParam, String type) {
        // 查询出所有合同
        List<ContractPageDTO> contracts = listContacts(BooleFlagEnum.YES, queryParam);
        if (CollectionUtils.isEmpty(contracts)) {
            return StringUtils.EMPTY;
        }

        try {
            // 数据转换
            List<ContractExportVO> vos = contracts.stream()
                    .map(contractConvert::toExportVO)
                    .peek(vo -> {
                        vo.setBusinessTypeName(BusinessTypeEnum.getDesc(vo.getBusinessType()));
                        vo.setNormalFlagName(BooleFlagEnum.getDesc(vo.getNormalFlag()));
                        vo.setFormalStatusName(ContractStatusEnum.getDesc(vo.getFormalStatusName()));
                        vo.setPartyAStampStatusName(BooleFlagEnum.getDesc(vo.getPartyAStampStatus()));
                        vo.setPartyBStampStatusName(BooleFlagEnum.getDesc(vo.getPartyBStampStatus()));
                        vo.setPartyCStampStatusName(BooleFlagEnum.getDesc(vo.getPartyCStampStatus()));
                        vo.setUploadFlagName(BooleFlagEnum.getDesc(vo.getUploadFlag()));
                        vo.setUploadSubFlagName(BooleFlagEnum.getDesc(vo.getUploadSubFlag()));
                        vo.setArchiveFlagName(BooleFlagEnum.getDesc(vo.getArchiveFlag()));
                        vo.setAbnormalFlagName(ContractAbnormalFlagEnum.getDesc(vo.getAbnormalFlag()));
                        vo.setStampOrderName(StampOrderEnum.getDesc(vo.getStampOrder()));
                        // vo.setChangeFlagName(ContractChangeFlagEnum.getDesc(vo.getChangeFlag()));
                        vo.setDepositUploadFlagName(BooleFlagEnum.getDesc(vo.getDepositUploadFlag()));

                        // 查找签约数
                        int signCount = deviceService.lambdaQuery().select(ContractDeviceEntity::getSignCount)
                                .eq(ContractDeviceEntity::getContractId, vo.getId())
                                .list().stream().mapToInt(ContractDeviceEntity::getSignCount).sum();
                        vo.setSignCount(signCount);
                    })
                    .toList();
            // 填充字典、用户信息
            contractFillingService.fillingProject(vos);
            contractFillingService.fillingSupplier(vos);
            contractFillingService.fillingCity(vos);
            converterFactory.convert(vos);
            Map<String, String> cityReginMapping = regionService.getCityReginMapping();
            vos = vos.stream().peek(vo -> {
                vo.setRegion(cityReginMapping.get(vo.getCityName()));
                vo.setChangedName((Objects.equals(vo.getChangeFlag(), 0) ? BooleFlagEnum.NO : BooleFlagEnum.YES).getDesc());
                vo.setApplyTime(getInvalidDate(vo.getApplyTime()));
                vo.setArchiveTime(getInvalidDate(vo.getArchiveTime()));
            }).toList();
            String date = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String fileName = String.format(DownLoadTypeEnum.YXHT_LB.getDesc()+"_%s_%s.xlsx", date, System.currentTimeMillis());
            if (ContractConstants.HISTORY_TYPE.equals(type)) {
                fileName = String.format(DownLoadTypeEnum.YXHT_LB_HIS.getDesc()+"_%s_%s.xlsx", date, System.currentTimeMillis());
            } else {
                // 填充大屏、设备尺寸信息
                fillingDeviceData(vos);
            }
//            log.error("---------------");
//            log.error(JSON.toJSONString(vos));
//            log.error("---------------");
            return EasyExcelUtils.createExcelToCos("合同列表", vos,
                    Collections.emptyList(), fileName, "venue");
        } catch (CommonException ex) {
            log.warn("转换查询参数失败, 请求参数: {}", queryParam, ex);
            throw ex;
        } catch (Exception ex) {
            log.warn("导出合同列表失败, 请求参数: {}", queryParam, ex);
        }
        return StringUtils.EMPTY;
    }

    public <T extends ContractExportVO> String exportContracts(ContractQueryParam queryParam, List<T> vos, String exportFileName) {
        try {
            // 填充字典、用户信息
            contractFillingService.fillingProject(vos);
            contractFillingService.fillingSupplier(vos);
            contractFillingService.fillingCity(vos);
            converterFactory.convert(vos);
            Map<String, String> cityReginMapping = regionService.getCityReginMapping();
            vos = vos.stream()
                    .peek(vo -> vo.setRegion(cityReginMapping.get(vo.getCityName())))
                    .toList();
            String date = LocalDate.now().format(DateTimeFormatter.ofPattern(DatePattern.PURE_DATE_PATTERN));
            String fileName = String.format("%s_%s_%s.xlsx", DownLoadTypeEnum.BGHT_SQD_LB.getDesc(), date, System.currentTimeMillis());
//            log.error("---------------");
//            log.error(JSON.toJSONString(vos));
//            log.error("---------------");
            return EasyExcelUtils.createExcelToCos("%s列表".formatted(exportFileName), vos,
                    Collections.emptyList(), fileName, "venue");
        } catch (CommonException ex) {
            log.warn("转换查询参数失败, 请求参数: {}", queryParam, ex);
            throw ex;
        } catch (Exception ex) {
            log.warn("导出合同列表失败, 请求参数: {}", queryParam, ex);
        }
        return StringUtils.EMPTY;
    }


    /**
     * 合同或申请单导出
     */
    public List<ContractPageDTO> listContacts(BooleFlagEnum formalFlag, ContractQueryParam queryParam) {
        Page<ContractPageDTO> page = new Page<>(1, -1);

        // 转换查询参数
        ContractQueryDTO queryDto = toQueryDto(queryParam, null);
        Optional.ofNullable(formalFlag).ifPresent(flag -> queryDto.setFormalFlag(flag.getCode()));

        // 分页查询
        IPage<ContractPageDTO> pagedContracts = contractService.pageListContracts(page, queryDto);
        return CollectionUtils.isEmpty(pagedContracts.getRecords()) ? Collections.emptyList() : pagedContracts.getRecords();
    }

    /**
     * 导出合同包含项目的付款周期
     *
     * @param queryParam
     * @return
     */
    public String exportContractsWithPayPeriod(ContractQueryParam queryParam, BooleFlagEnum formalFlag, String contractType) {
        // 查询出所有合同id
        List<Integer> contractIds = listContractsIds(formalFlag, queryParam);

        if (CollectionUtils.isEmpty(contractIds)) {
            return StringUtils.EMPTY;
        }

        // 查询出所有合同
        List<ContractPageWithoutPayPeriodDTO> contracts = listContactsWithoutPayPeriodByContractIds(contractIds);
        if (CollectionUtils.isEmpty(contracts)) {
            return StringUtils.EMPTY;
        }

        try {
            // 查找合同下是否有已生效的补充协议
            Set<Integer> ids = contracts.stream().map(ContractPageWithoutPayPeriodDTO::getId)
                    .collect(Collectors.toSet());
            Set<Integer> effectIds = contractService.lambdaQuery().select(ContractEntity::getParentId)
                    .in(ContractEntity::getParentId, ids)
                    .eq(ContractEntity::getEffectFlag, BooleFlagEnum.YES.getCode())
                    .list()
                    .stream().map(ContractEntity::getParentId)
                    .collect(Collectors.toSet());
            // 查找工单系统
            Set<String> projectCodes = contracts.stream().map(ContractPageWithoutPayPeriodDTO::getProjectCode)
                    .filter(Objects::nonNull)
                    .map(projectCode -> projectCode.split("-")[0])
                    .collect(Collectors.toSet());
            Set<String> dispatchedBuildingNos = null;
            try {
                ResultTemplate<Set<String>> retData = feignWorkOrderRpc.filterDispatchedBuildingNoList(new FilterBuildingNoDTO(projectCodes));
                log.info("feignWorkOrderRpc.filterDispatchedBuildingNoList返回数据:{}", JSON.toJSONString(retData));
                if (retData.getSuccess()) {
                    dispatchedBuildingNos = retData.getData();
                } else {
                    dispatchedBuildingNos = Collections.emptySet();
                }
            } catch (Exception e) {
                log.error("获取工单数据失败", e);
            }
            // ssp - 获取点位验收数量
            AtomicReference<Map<String, Integer>> projectPointAcceptedMapHolder = new AtomicReference<>(Collections.emptyMap());
            if (CollectionUtils.isNotEmpty(projectCodes)) {
                try {
                    ResultTemplate<List<Map<Object, Object>>> retData = feignSspClient.getPointCountByBuildingNo(projectCodes.stream().toList());
                    List<Map<Object, Object>> pointCountByBuildingNos = retData.getSuccess() ? retData.getData() : Collections.emptyList();
                    if (CollectionUtils.isNotEmpty(pointCountByBuildingNos)) {
                        projectPointAcceptedMapHolder.set(pointCountByBuildingNos.stream()
                                .collect(Collectors.toMap(map -> (String) map.get("name"), map -> (Integer) map.get("count"), (v1, v2) -> v1)));
                    }
                } catch (Exception e) {
                    log.error("请求ssp获取点位验收数量异常！", e);
                }
            }
            Set<String> dispatchedBuildingNosFinal = dispatchedBuildingNos;
            List<ContractPageWithPayPeriodDTO> contractPageWithPayPeriodDTOS = getContractPageWithPayPeriodDTOS(contracts);
            // 补充签约单价
            fillingPricePeriodData(contractPageWithPayPeriodDTOS);
            // 补充设备数据
            fillingDeviceDataOnProject(contractIds, contractPageWithPayPeriodDTOS);
            // 补充大屏系数
            fillingLargeScreenData(contractPageWithPayPeriodDTOS);

            // 数据转换
            List<ProjectExportVO> vos = getProjectExportVOS(contractPageWithPayPeriodDTOS, projectPointAcceptedMapHolder, effectIds, dispatchedBuildingNosFinal);

            // 填充字典、用户信息
            contractFillingService.fillingSupplier(vos);
            converterFactory.convert(vos);
            Map<String, String> cityReginMapping = regionService.getCityReginMapping();
            vos = vos.stream().peek(vo -> {
                vo.setRegion(cityReginMapping.get(vo.getCityName()));
                // 补充“楼宇评级编码”
                if (StringUtils.isNotBlank(vo.getProjectCode())) {
                    vo.setBuildingNo(vo.getProjectCode().split("-", 2)[0]);
                }
            }).toList();

            List vosToExport = null;
            String name = null;
            // 历史有效合同
            if (ContractConstants.HISTORY_TYPE.equals(contractType)) {
                vosToExport = vos;
                name = DownLoadTypeEnum.YXHT_XM_FK_HIS.getDesc();
            } else if (ContractConstants.CONTRACT_TYPE.equals(contractType)) {
                vosToExport = vos;
                name = DownLoadTypeEnum.YXHT_XM_FK.getDesc();
            }

            String date = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String fileName = String.format("%s_%s_%s.xlsx", name, date, System.currentTimeMillis());
            log.error("---------------");
            log.error(JSON.toJSONString(vos));
            log.error("---------------");
            return EasyExcelUtils.createExcelToCosWithHandler(name, vosToExport,
                    Collections.emptyList(), fileName, "venue", new CellWriteHandler() {
                        @Override
                        public void afterCellDispose(CellWriteHandlerContext context) {
                            if (!context.getHead()) {
                                WriteCellData<?> cellData = context.getFirstCellData();
                                WriteCellStyle writeCellStyle = cellData.getOrCreateStyle();
                                writeCellStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
//                                if (!redFields.contains(context.getColumnIndex())) {
//                                    return;
//                                }
                                if (context.getColumnIndex() != RED_CONDITION) {
                                    return;
                                }
                                setColor(context, writeCellStyle);
                            } else {
                                WriteCellData<?> cellData = context.getFirstCellData();
                                WriteCellStyle writeCellStyle = cellData.getOrCreateStyle();
                                writeCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
                            }
                        }
                    });
        } catch (CommonException ex) {
            log.warn("转换查询参数失败, 请求参数: {}", queryParam, ex);
            throw ex;
        } catch (Exception ex) {
            log.warn("导出失败, 请求参数: {}", queryParam, ex);
        }
        return StringUtils.EMPTY;
    }

    private static void setColor(CellWriteHandlerContext context, WriteCellStyle writeCellStyle) {
        try {
            String payPeriod = context.getFirstCellData().getStringValue();
            if (StringUtils.isBlank(payPeriod)) {
                return;
            }
            String[] strs = payPeriod.split("~");
            LocalDate now = LocalDate.now();
            if (strs.length == 2 && StringUtils.isNotBlank(strs[0]) && StringUtils.isNotBlank(strs[1])) {
                LocalDate start = LocalDate.parse(strs[0], DateTimeFormatter.ofPattern(VenueConstants.DATE_FORMAT));
                LocalDate end = LocalDate.parse(strs[1], DateTimeFormatter.ofPattern(VenueConstants.DATE_FORMAT));
                if (!now.isBefore(start) && !now.isAfter(end)) {
                    writeCellStyle.setFillForegroundColor(IndexedColors.RED.getIndex());
                    writeCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    private List<ProjectExportVO> getProjectExportVOS(List<ContractPageWithPayPeriodDTO> contractPageWithPayPeriodDTOS, AtomicReference<Map<String, Integer>> projectPointAcceptedMapHolder, Set<Integer> effectIds, Set<String> dispatchedBuildingNosFinal) {
        List<ProjectExportVO> vos = contractPageWithPayPeriodDTOS.stream()
                .map(contractConvert::toProjectExportVO)
                .peek(vo -> {
                    vo.setBusinessTypeName(BusinessTypeEnum.getDesc(vo.getBusinessType()));
                    vo.setNormalFlagName(BooleFlagEnum.getDesc(vo.getNormalFlag()));
                    vo.setFormalStatusName(ContractStatusEnum.getDesc(vo.getFormalStatusName()));
                    vo.setArchiveFlagName(BooleFlagEnum.getDesc(vo.getArchiveFlag()));
                    vo.setAbnormalFlagName(ContractAbnormalFlagEnum.getDesc(vo.getAbnormalFlag()));
                    vo.setStopFlagName(BooleFlagEnum.getDesc(vo.getStopFlag()));
                    if (StringUtils.isNotBlank(vo.getProjectCode())) {
                        vo.setFinishCount(projectPointAcceptedMapHolder.get().getOrDefault(vo.getProjectCode().split("-")[0], 0));
                    } else {
                        vo.setFinishCount(0);
                    }
                    vo.setRealCount(vo.getFinishCount() - (0 == vo.getLastSignCount() ? vo.getFirstSignCount() : vo.getLastSignCount()));
                    if (vo.getFirstSignCount() == null) {
                        vo.setFirstSignCount(0);
                    }

                    if (effectIds.contains(vo.getId())) {
                        vo.setChangeStatus(BooleFlagEnum.YES.getDesc());
                        vo.setLastSignCountStr(String.valueOf(vo.getLastSignCount()));
                    } else {
                        vo.setLastSignCountStr("-");
                        vo.setChangeStatus(BooleFlagEnum.NO.getDesc());
                    }

                    if (StringUtils.isNotBlank(vo.getProjectCode()) && Objects.nonNull(dispatchedBuildingNosFinal)
                            && dispatchedBuildingNosFinal.contains(vo.getProjectCode().split("-")[0])) {
                        vo.setWorkOrderStatus(BooleFlagEnum.YES.getDesc());
                    }
                    if (BigDecimalUtils.eq(vo.getDepositAmount(), BigDecimal.ZERO)) {
                        vo.setDepositAmountStr("无");
                    } else {
                        vo.setDepositAmountStr(String.valueOf(vo.getDepositAmount()));
                    }
                })
                .toList();
        return vos;
    }

    /**
     * 补充协议导出
     *
     * @param vos
     * @return
     */
    private String createExcelByAgreements(List vos, String contractType) {
        try {
            String date = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String fileName = String.format("补充协议_%s_%s.xlsx", date, System.currentTimeMillis());
            int redCondition = RED_CONDITION_AGREEMENTS;
            if (ContractConstants.HISTORY_TYPE.equals(contractType)) {
                fileName = String.format("历史有效合同_%s_%s.xlsx", date, System.currentTimeMillis());
                redCondition = RED_CONDITION_HISTORY;
            }

            int finalRedCondition = redCondition;
            return EasyExcelUtils.createExcelToCosWithHandler("合同列表", vos,
                    Collections.emptyList(), fileName, "venue", new CellWriteHandler() {
                        @Override
                        public void afterCellDispose(CellWriteHandlerContext context) {
                            if (!context.getHead()) {
                                WriteCellData<?> cellData = context.getFirstCellData();
                                WriteCellStyle writeCellStyle = cellData.getOrCreateStyle();
                                writeCellStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
                                if (context.getColumnIndex() != finalRedCondition) {
                                    return;
                                }
                                setColor(context, writeCellStyle);
                            } else {
                                WriteCellData<?> cellData = context.getFirstCellData();
                                WriteCellStyle writeCellStyle = cellData.getOrCreateStyle();
                                writeCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
                            }
                        }
                    });
        } catch (CommonException ex) {
            log.warn("转换查询参数失败", ex);
            throw ex;
        } catch (Exception ex) {
            log.warn("导出合同列表失败", ex);
        }
        return StringUtils.EMPTY;
    }

    /**
     * 查找出符合查询条件的合同Id
     *
     * @param formalFlag
     * @param queryParam
     * @return
     */
    private List<Integer> listContractsIds(BooleFlagEnum formalFlag, ContractQueryParam queryParam) {
        // 转换查询参数
        ContractQueryDTO queryDto = toQueryDto(queryParam, null);
        Optional.ofNullable(formalFlag).ifPresent(flag -> queryDto.setFormalFlag(flag.getCode()));

        List<Integer> contractIds = contractService.listContractsIds(queryDto);
        return CollectionUtils.isEmpty(contractIds) ? Collections.emptyList() : contractIds;
    }

    /**
     * 合同或申请单导出
     */
    private List<ContractPageWithoutPayPeriodDTO> listContactsWithoutPayPeriodByContractIds(List<Integer> contractIds) {
        List<ContractPageWithoutPayPeriodDTO> contractPageWithoutPayPeriodDTOS = contractService.listContactsWithoutPayPeriodByContractIds(contractIds);
        return CollectionUtils.isEmpty(contractPageWithoutPayPeriodDTOS) ? Collections.emptyList() : contractPageWithoutPayPeriodDTOS;
    }

    /**
     * 过滤无效日期
     *
     * @param time2
     * @return
     */
    private LocalDateTime getInvalidDate(LocalDateTime time2) {
        if (time2 == null) {
            return null;
        }
        LocalDateTime time1 = LocalDateTime.of(1900, 1, 1, 0, 0);
        boolean isEqual = time1.getYear() == time2.getYear() &&
                time1.getMonth() == time2.getMonth() &&
                time1.getDayOfMonth() == time2.getDayOfMonth();

        if (isEqual) {
            return null;
        } else {
            return time2;
        }
    }

    /**
     * 补充协议单独导出
     *
     * @param queryParam
     * @return
     */
    public String exportAgreements(ContractQueryParam queryParam, String name) {
        List<ContractPageDTO> applies = listContacts(null, queryParam);
        if (CollectionUtils.isEmpty(applies)) {
            return StringUtils.EMPTY;
        }
        setContractsCodeByParentId(applies);

        try {
            // 数据转换
            List<ContractApplyExportVO> vos = applies.stream()
                    .map(contractConvert::toApplyExportVO)
                    .peek(vo -> {
                        vo.setBusinessTypeName(BusinessTypeEnum.getDesc(vo.getBusinessType()));
                        vo.setNormalFlagName(BooleFlagEnum.getDesc(vo.getNormalFlag()));
                        vo.setApplyStatusName(ContractApplyStatusEnum.getDesc(vo.getApplyStatus()));
                        vo.setBusinessTypeName(BusinessTypeEnum.getDesc(vo.getBusinessType()));
                        vo.setNormalFlagName(BooleFlagEnum.getDesc(vo.getNormalFlag()));
                        vo.setArchiveFlagName(BooleFlagEnum.getDesc(vo.getArchiveFlag()));
                        vo.setStopFlagName(BooleFlagEnum.getDesc(vo.getStopFlag()));
                        // 查找签约数
                        int signCount = deviceService.lambdaQuery().select(ContractDeviceEntity::getSignCount)
                                .eq(ContractDeviceEntity::getContractId, vo.getId())
                                .list().stream().mapToInt(ContractDeviceEntity::getSignCount).sum();
                        vo.setSignCount(signCount);
                        vo.setStopFlagName(BooleFlagEnum.getDesc(vo.getStopFlag()));
                        vo.setBusinessTypeName(BusinessTypeEnum.getDesc(vo.getBusinessType()));
                        vo.setNormalFlagName(BooleFlagEnum.getDesc(vo.getNormalFlag()));
                        vo.setArchiveFlagName(BooleFlagEnum.getDesc(vo.getArchiveFlag()));
                    })
                    .toList();
            // 填充字典、用户信息
            contractFillingService.fillingProject(vos);
            contractFillingService.fillingSupplier(vos);
            contractFillingService.fillingCity(vos);
            converterFactory.convert(vos);
            converterFactory.convert(vos);

            // 填充大区
            Map<String, String> cityReginMapping = regionService.getCityReginMapping();
            vos.forEach(vo -> vo.setRegion(cityReginMapping.get(vo.getCityName())));

            String date = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String fileName = String.format("%s_%s_%s.xlsx", name, date, System.currentTimeMillis());
            List<ContractAgreementExportVO> list = vos.stream().map(contractConvert::toContractAgreementExportVO)
                    .toList();
            return EasyExcelUtils.createExcelToCos(name, list,
                    Collections.emptyList(), fileName, "venue");
        } catch (CommonException ex) {
            log.warn("转换查询参数失败, 请求参数: {}", queryParam, ex);
            throw ex;
        } catch (Exception ex) {
            log.warn("导出{}列表失败, 请求参数: {}", name, queryParam, ex);
        }
        return StringUtils.EMPTY;
    }

    /**
     * 导出变更包含项目的付款周期
     *
     * @param queryParam
     * @return
     */
    public String exportAmendmentWithPayPeriod(ContractQueryParam queryParam, BooleFlagEnum formalFlag, String name) {
        // 查询出所有合同id
        List<Integer> contractIds = listContractsIds(formalFlag, queryParam);

        if (CollectionUtils.isEmpty(contractIds)) {
            return StringUtils.EMPTY;
        }

        // 查询出所有合同
        List<ContractPageWithoutPayPeriodDTO> contracts = listContactsWithoutPayPeriodByContractIds(contractIds);
        if (CollectionUtils.isEmpty(contracts)) {
            return StringUtils.EMPTY;
        }

        try {
            List<ContractPageWithPayPeriodDTO> contractPageWithPayPeriodDTOS = getContractPageWithPayPeriodDTOS(contracts);
            // 补充签约单价
            fillingPricePeriodData(contractPageWithPayPeriodDTOS);
            // 补充设备数据
            fillingDeviceDataOnProject(contractIds, contractPageWithPayPeriodDTOS);
            // 补充大屏系数
            fillingLargeScreenData(contractPageWithPayPeriodDTOS);

            // 数据转换
            List<ProjectExportVO> vos = contractPageWithPayPeriodDTOS.stream()
                    .map(contractConvert::toProjectExportVO)
                    .peek(vo -> {
                        vo.setBusinessTypeName(BusinessTypeEnum.getDesc(vo.getBusinessType()));
                        vo.setNormalFlagName(BooleFlagEnum.getDesc(vo.getNormalFlag()));
                        vo.setFormalStatusName(ContractStatusEnum.getDesc(vo.getFormalStatusName()));
                        vo.setArchiveFlagName(BooleFlagEnum.getDesc(vo.getArchiveFlag()));
                        vo.setAbnormalFlagName(ContractAbnormalFlagEnum.getDesc(vo.getAbnormalFlag()));
                        vo.setStopFlagName(BooleFlagEnum.getDesc(vo.getStopFlag()));
                        vo.setEffectFlagName(BooleFlagEnum.getDesc(vo.getEffectFlag()));
                        // // 查找签约数
                        // int signCount = deviceService.lambdaQuery().select(ContractDeviceEntity::getSignCount)
                        //         .eq(ContractDeviceEntity::getContractId, vo.getContractId())
                        //         .list().stream().mapToInt(ContractDeviceEntity::getSignCount).sum();
                        // vo.setSignCount(signCount);
                    })
                    .toList();

            // 填充字典、用户信息
            contractFillingService.fillingSupplier(vos);
            converterFactory.convert(vos);
            Map<String, String> cityReginMapping = regionService.getCityReginMapping();
            vos = vos.stream().peek(vo -> vo.setRegion(cityReginMapping.get(vo.getCityName()))).toList();
            // 变更申请
            List vosToExport = vos.stream().map(contractConvert::toProjectExportByAmendmentVO).toList();

            String date = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String fileName = String.format("%s_%s_%s.xlsx", DownLoadTypeEnum.BGHT_SQD_XM_FK.getDesc(), date, System.currentTimeMillis());
            return EasyExcelUtils.createExcelToCosWithHandler(name, vosToExport,
                    Collections.emptyList(), fileName, "venue", new CellWriteHandler() {
                        @Override
                        public void afterCellDispose(CellWriteHandlerContext context) {
                            if (!context.getHead()) {
                                WriteCellData<?> cellData = context.getFirstCellData();
                                WriteCellStyle writeCellStyle = cellData.getOrCreateStyle();
                                writeCellStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
//                                if (!redFields.contains(context.getColumnIndex())) {
//                                    return;
//                                }
                                if (context.getColumnIndex() != RED_CONDITION) {
                                    return;
                                }
                                setColor(context, writeCellStyle);
                            } else {
                                WriteCellData<?> cellData = context.getFirstCellData();
                                WriteCellStyle writeCellStyle = cellData.getOrCreateStyle();
                                writeCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
                            }
                        }
                    });
        } catch (CommonException ex) {
            log.warn("转换查询参数失败, 请求参数: {}", queryParam, ex);
            throw ex;
        } catch (Exception ex) {
            log.warn("导出失败, 请求参数: {}", queryParam, ex);
        }
        return StringUtils.EMPTY;
    }

    private List<ContractPageWithPayPeriodDTO> getContractPageWithPayPeriodDTOS(List<ContractPageWithoutPayPeriodDTO> contracts) {
        List<ContractPageWithPayPeriodDTO> contractPageWithPayPeriodDTOS = Lists.newArrayList();
        for (ContractPageWithoutPayPeriodDTO contractPageWithoutPayPeriodDTO : contracts) {
            try {
                List<LedgerEntity> ledgerInfos = ledgerService.getLedgerInfo(contractPageWithoutPayPeriodDTO.getId());
                log.info("根据合同id:{}查询台账:{}", contractPageWithoutPayPeriodDTO.getId(), JSON.toJSONString(ledgerInfos));
                if (CollectionUtils.isEmpty(ledgerInfos)) {
                    // 一个台账都没有找到
                    ContractPageWithPayPeriodDTO contractPageWithPayPeriodDTO = new ContractPageWithPayPeriodDTO();
                    contractConvert.toContractPageWithPayPeriodDTOCopy(contractPageWithoutPayPeriodDTO, contractPageWithPayPeriodDTO);
                    contractPageWithPayPeriodDTOS.add(contractPageWithPayPeriodDTO);
                }
                boolean find = false;
                for (LedgerEntity ledgerInfo : ledgerInfos) {
                    if (!StringUtils.equals(ledgerInfo.getProjectName(), contractPageWithoutPayPeriodDTO.getProjectName())) {
                        continue;
                    }
                    find = true;
                    ContractPageWithPayPeriodDTO contractPageWithPayPeriodDTO = contractConvert.toContractPageWithPayPeriodDTO(ledgerInfo);
                    if (Objects.nonNull(ledgerInfo.getStartDate()) && Objects.nonNull(ledgerInfo.getEndDate())) {
                        contractPageWithPayPeriodDTO.setPayPeriod(String.join("~",
                                ledgerInfo.getStartDate().format(DateTimeFormatter.ofPattern(VenueConstants.DATE_FORMAT)),
                                ledgerInfo.getEndDate().format(DateTimeFormatter.ofPattern(VenueConstants.DATE_FORMAT))));
                    }
                    contractConvert.toContractPageWithPayPeriodDTOCopy(contractPageWithoutPayPeriodDTO, contractPageWithPayPeriodDTO);
                    // 查询供应商以及银行信息
                    List<ContractSupplierAndBankDTO> contractSupplierAndBankDtos = contractSupplierMapper.getSupplierAndBankInfo(ledgerInfo.getSupplierId(),
                            ledgerInfo.getContractId());
                    log.info("根据供应商查询银行等信息 供应商id:{},合同id:{},查询结果:{}", ledgerInfo.getSupplierId(), ledgerInfo.getContractId(),
                            JSON.toJSONString(contractSupplierAndBankDtos));
                    if (CollectionUtils.isNotEmpty(contractSupplierAndBankDtos)) {
                        contractConvert.toContractPageWithPayPeriodDTOBySupplierAndBank(contractSupplierAndBankDtos.get(0), contractPageWithPayPeriodDTO);
                    }
                    // 已付金额 回票金额
                    List<LedgerEntity> ledgerEntities = ledgerServiceImpl.lambdaQuery()
                            .eq(LedgerEntity::getPaymentPeriodId, ledgerInfo.getPaymentPeriodId())
                            .eq(LedgerEntity::getContractId, ledgerInfo.getContractId())
                            .eq(LedgerEntity::getProjectId, ledgerInfo.getContractId())
                            .list();
                    if (CollectionUtils.isNotEmpty(ledgerEntities)) {
                        contractPageWithPayPeriodDTO.setReturnInvoicedAmount(ledgerEntities.get(0).getReturnInvoicedAmount()
                                .setScale(2, RoundingMode.HALF_UP));
                        contractPageWithPayPeriodDTO.setPaidAmount(ledgerEntities.get(0).getPaidAmount()
                                .setScale(2, RoundingMode.HALF_UP));
                    }
                    contractPageWithPayPeriodDTOS.add(contractPageWithPayPeriodDTO);
                }
                if (!find) {
                    //如果这个项目没找到
                    log.warn("单据({}),项目({}),没有获取到台账", contractPageWithoutPayPeriodDTO.getId(), contractPageWithoutPayPeriodDTO.getProjectName());
                    ContractPageWithPayPeriodDTO contractPageWithPayPeriodDTO = new ContractPageWithPayPeriodDTO();
                    contractConvert.toContractPageWithPayPeriodDTOCopy(contractPageWithoutPayPeriodDTO, contractPageWithPayPeriodDTO);
                    contractPageWithPayPeriodDTOS.add(contractPageWithPayPeriodDTO);
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        return contractPageWithPayPeriodDTOS;
    }

    /**
     * 导出补充协议包含项目的付款周期
     *
     * @param queryParam
     * @return
     */
    public String exportAgreementWithPayPeriod(ContractQueryParam queryParam, BooleFlagEnum formalFlag, String name) {
        // 查询出所有合同id
        List<Integer> contractIds = listContractsIds(formalFlag, queryParam);

        if (CollectionUtils.isEmpty(contractIds)) {
            return StringUtils.EMPTY;
        }

        // 查询出所有合同
        List<ContractPageWithoutPayPeriodDTO> contracts = listContactsWithoutPayPeriodByContractIds(contractIds);
        if (CollectionUtils.isEmpty(contracts)) {
            return StringUtils.EMPTY;
        }

        setContractsCodeByParentIdProjectExport(contracts);

        try {
            List<ContractPageWithPayPeriodDTO> contractPageWithPayPeriodDTOS = getContractPageWithPayPeriodDTOS(contracts);
            // 数据转换
            List<ProjectExportVO> vos = contractPageWithPayPeriodDTOS.stream()
                    .map(contractConvert::toProjectExportVO)
                    .peek(vo -> {
                        vo.setBusinessTypeName(BusinessTypeEnum.getDesc(vo.getBusinessType()));
                        vo.setNormalFlagName(BooleFlagEnum.getDesc(vo.getNormalFlag()));
                        vo.setFormalStatusName(ContractStatusEnum.getDesc(vo.getFormalStatusName()));
                        vo.setArchiveFlagName(BooleFlagEnum.getDesc(vo.getArchiveFlag()));
                        vo.setAbnormalFlagName(ContractAbnormalFlagEnum.getDesc(vo.getAbnormalFlag()));
                        vo.setStopFlagName(BooleFlagEnum.getDesc(vo.getStopFlag()));
                        // 查找签约数
                        int signCount = deviceService.lambdaQuery().select(ContractDeviceEntity::getSignCount)
                                .eq(ContractDeviceEntity::getContractId, vo.getContractId())
                                .list().stream().mapToInt(ContractDeviceEntity::getSignCount).sum();
                        vo.setSignCount(signCount);
                    })
                    .toList();

            // 填充字典、用户信息
            contractFillingService.fillingSupplier(vos);
            converterFactory.convert(vos);
            Map<String, String> cityReginMapping = regionService.getCityReginMapping();
            vos = vos.stream().peek(vo -> vo.setRegion(cityReginMapping.get(vo.getCityName()))).toList();
            // 变更申请
            List vosToExport = vos.stream().map(contractConvert::toProjectExportByAgreementVO).toList();

            String date = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String fileName = String.format("%s_%s_%s.xlsx", name, date, System.currentTimeMillis());
            log.error("---------------");
            log.error(JSON.toJSONString(vos));
            log.error("---------------");
            return EasyExcelUtils.createExcelToCosWithHandler(name, vosToExport,
                    Collections.emptyList(), fileName, "venue", new CellWriteHandler() {
                        @Override
                        public void afterCellDispose(CellWriteHandlerContext context) {
                            if (!context.getHead()) {
                                WriteCellData<?> cellData = context.getFirstCellData();
                                WriteCellStyle writeCellStyle = cellData.getOrCreateStyle();
                                writeCellStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
//                                if (!redFields.contains(context.getColumnIndex())) {
//                                    return;
//                                }
                                if (context.getColumnIndex() != RED_CONDITION) {
                                    return;
                                }
                                setColor(context, writeCellStyle);
                            } else {
                                WriteCellData<?> cellData = context.getFirstCellData();
                                WriteCellStyle writeCellStyle = cellData.getOrCreateStyle();
                                writeCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
                            }
                        }
                    });
        } catch (CommonException ex) {
            log.warn("转换查询参数失败, 请求参数: {}", queryParam, ex);
            throw ex;
        } catch (Exception ex) {
            log.warn("导出失败, 请求参数: {}", queryParam, ex);
        }
        return StringUtils.EMPTY;
    }

    /**
     * 导出合同申请包含项目的付款周期
     *
     * @param queryParam
     * @return
     */
    public String exportApplyWithPayPeriod(ContractQueryParam queryParam, BooleFlagEnum formalFlag) {
        // 查询出所有合同id
        List<Integer> contractIds = listContractsIds(formalFlag, queryParam);

        if (CollectionUtils.isEmpty(contractIds)) {
            return StringUtils.EMPTY;
        }

        // 查询出所有合同
        List<ContractPageWithoutPayPeriodDTO> contracts = listContactsWithoutPayPeriodByContractIds(contractIds);
        if (CollectionUtils.isEmpty(contracts)) {
            return StringUtils.EMPTY;
        }

        try {
            List<ContractPageWithPayPeriodDTO> contractPageWithPayPeriodDTOS = getContractPageWithPayPeriodDTOS(contracts);
            // 补充签约单价
            fillingPricePeriodData(contractPageWithPayPeriodDTOS);
            // 补充设备数据
            fillingDeviceDataOnProject(contractIds, contractPageWithPayPeriodDTOS);
            // 补充大屏系数
            fillingLargeScreenData(contractPageWithPayPeriodDTOS);

            // 数据转换
            List<ProjectExportVO> vos = contractPageWithPayPeriodDTOS.stream()
                    .map(contractConvert::toProjectExportVO)
                    .peek(vo -> {
                        vo.setBusinessTypeName(BusinessTypeEnum.getDesc(vo.getBusinessType()));
                        vo.setNormalFlagName(BooleFlagEnum.getDesc(vo.getNormalFlag()));
                        vo.setFormalStatusName(ContractStatusEnum.getDesc(vo.getFormalStatusName()));
                        vo.setArchiveFlagName(BooleFlagEnum.getDesc(vo.getArchiveFlag()));
                        vo.setAbnormalFlagName(ContractAbnormalFlagEnum.getDesc(vo.getAbnormalFlag()));
                        vo.setStopFlagName(BooleFlagEnum.getDesc(vo.getStopFlag()));
                        // // 查找签约数
                        // int signCount = deviceService.lambdaQuery().select(ContractDeviceEntity::getSignCount)
                        //         .eq(ContractDeviceEntity::getContractId, vo.getContractId())
                        //         .list().stream().mapToInt(ContractDeviceEntity::getSignCount).sum();
                        // vo.setSignCount(signCount);
                    })
                    .toList();

            // 填充字典、用户信息
            contractFillingService.fillingSupplier(vos);
            converterFactory.convert(vos);
            Map<String, String> cityReginMapping = regionService.getCityReginMapping();
            vos = vos.stream().peek(vo -> vo.setRegion(cityReginMapping.get(vo.getCityName()))).toList();
            // 变更申请
            List vosToExport = vos.stream().map(contractConvert::toProjectExportByApplyVO).toList();

            String date = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String fileName = String.format("%s_%s_%s.xlsx", DownLoadTypeEnum.HTSQD_XM_FK.getDesc(), date, System.currentTimeMillis());
            return EasyExcelUtils.createExcelToCosWithHandler("合同申请单", vosToExport,
                    Collections.emptyList(), fileName, "venue", new CellWriteHandler() {
                        @Override
                        public void afterCellDispose(CellWriteHandlerContext context) {
                            if (!context.getHead()) {
                                WriteCellData<?> cellData = context.getFirstCellData();
                                WriteCellStyle writeCellStyle = cellData.getOrCreateStyle();
                                writeCellStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
//                                if (!redFields.contains(context.getColumnIndex())) {
//                                    return;
//                                }
                                if (context.getColumnIndex() != APPLY_RED_CONDITION) {
                                    return;
                                }
                                setColor(context, writeCellStyle);
                            } else {
                                WriteCellData<?> cellData = context.getFirstCellData();
                                WriteCellStyle writeCellStyle = cellData.getOrCreateStyle();
                                writeCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
                            }
                        }
                    });
        } catch (CommonException ex) {
            log.warn("转换查询参数失败, 请求参数: {}", queryParam, ex);
            throw ex;
        } catch (Exception ex) {
            log.warn("导出失败, 请求参数: {}", queryParam, ex);
        }
        return StringUtils.EMPTY;
    }

    /**
     * 获取单价周期数据
     */
    // private Map<String, String> getDevicePricePeriodData(Set<Integer> contractIds) {
    //     if (ListUtils.isEmpty(contractIds)) {
    //         return Map.of();
    //     }
    //     //
    //     return pricePeriodService.lambdaQuery()
    //             .select(ContractPricePeriodEntity::getContractId, ContractPricePeriodEntity::getSubContractId,
    //                     ContractPricePeriodEntity::getDeviceId, ContractPricePeriodEntity::getPriceYear)
    //             .in(ContractPricePeriodEntity::getContractId, contractIds)
    //             .list().stream()
    //             .collect(Collectors.groupingBy(e -> "%s-%s".formatted(e.getSubContractId(), e.getDeviceId()),
    //                     Collectors.mapping(e -> String.valueOf(e.getPriceYear()), Collectors.joining(","))));
    // }

    /**
     * 补充签约单价
     */
    private void fillingPricePeriodData(List<ContractPageWithPayPeriodDTO> dtos) {
        Set<Integer> contractIds = dtos.stream().map(ContractPageWithPayPeriodDTO::getId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(contractIds)) {
            return;
        }
        //
        Map<String, String> devicePricePeriodMap = pricePeriodService.lambdaQuery()
                .select(ContractPricePeriodEntity::getContractId, ContractPricePeriodEntity::getSubContractId,
                        ContractPricePeriodEntity::getDeviceId, ContractPricePeriodEntity::getPriceYear)
                .in(ContractPricePeriodEntity::getContractId, contractIds)
                .list().stream()
                .collect(Collectors.groupingBy(e -> "%s-%s".formatted(e.getSubContractId(), e.getDeviceId()),
                        Collectors.mapping(e -> String.valueOf(e.getPriceYear()), Collectors.joining(","))));
        if (MapUtils.isEmpty(devicePricePeriodMap)) {
            return;
        }
        // 补充数据
        dtos.forEach(dto -> dto.setPricePeriodYear(
                devicePricePeriodMap.getOrDefault("%s-%s".formatted(dto.getSubContractId(), dto.getDeviceId()), "")));
    }

    /**
     * 补充大屏系数
     */
    private void fillingLargeScreenData(List<ContractPageWithPayPeriodDTO> dtos) {
        // 大屏系数
        List<String> buildingNos = dtos.stream().map(e -> e.getProjectCode().split("-")[0]).distinct().toList();
        if (CollectionUtils.isEmpty(buildingNos)) {
            return;
        }
        Result<List<BuildingGeneVO>> result = feignMethH5Rpc.buildingGeneByNo(buildingNos);
        if (result.getCode() != HttpStatus.OK.value() || CollectionUtils.isEmpty(result.getData())) {
            return;
        }
        Map<String, BigDecimal> buildingFinalCoefficientMap = result.getData().stream()
                // valueMapper不能为null
                .filter(e -> Objects.nonNull(e.getFinalCoefficient()))
                .collect(Collectors.toMap(BuildingGeneVO::getBuildingRatingNo, BuildingGeneVO::getFinalCoefficient));
        dtos.forEach(dto -> {
            if (StringUtils.isNotBlank(dto.getLargeScreen()) && Objects.equals(dto.getLargeScreen(), BooleFlagEnum.YES.getDesc())) {
                dto.setLargeScreenCoefficient(buildingFinalCoefficientMap.get(dto.getProjectCode().split("-")[0]));
            }
        });
    }

    /**
     * 补充设备数据
     */
    private void fillingDeviceDataOnProject(List<Integer> contractIds, List<ContractPageWithPayPeriodDTO> dtos) {
        if (CollectionUtils.isEmpty(contractIds)) {
            return;
        }
        // 查询设备上的“激励单价”数据
        Map<Integer, ContractDeviceEntity> deviceMap = deviceService.lambdaQuery()
                .in(ContractDeviceEntity::getContractId, contractIds)
                .list().stream()
                .collect(Collectors.toMap(ContractDeviceEntity::getId, e -> e));
        if (MapUtils.isEmpty(deviceMap)) {
            return;
        }

        // 统计设备点位的规格数量
        QueryWrapper<ContractDevicePointEntity> wrapper = new QueryWrapper<>();
        wrapper.select("contract_id", "project_id", "device_id", "size", "COUNT(1) AS count")
                .in("contract_id", contractIds)
                .groupBy("contract_id", "project_id", "device_id", "size");
        Map<String, List<Map<String, Object>>> devicePointMap = devicePointService.getBaseMapper().selectMaps(wrapper).stream()
                .collect(Collectors.groupingBy(r -> "%s-%s-%s".formatted(r.get("contract_id"), r.get("project_id"), r.get("device_id")), Collectors.toList()));

        //
        deviceDataHandle(dtos, deviceMap, devicePointMap);
    }

    private void deviceDataHandle(List<ContractPageWithPayPeriodDTO> dtos, Map<Integer, ContractDeviceEntity> deviceMap,
                                  Map<String, List<Map<String, Object>>> devicePointMap) {
        dtos.stream().filter(e -> Objects.nonNull(e.getDeviceId()))
                .collect(Collectors.groupingBy(ContractPageWithPayPeriodDTO::getDeviceId, Collectors.toList()))
                .forEach((deviceId, items) -> {
                    ContractDeviceEntity device = deviceMap.get(deviceId);
                    if (Objects.isNull(device)) {
                        return;
                    }
                    //
                    List<Map<String, Object>> devicePoints = devicePointMap.get("%s-%s-%s".formatted(device.getContractId(), device.getProjectId(), device.getId()));
                    if (CollectionUtils.isEmpty(devicePoints)) {
                        return;
                    }
                    //
                    Pair<String, String> pair = getSizeSpecAndLargeScreen(devicePoints);
                    //
                    items.forEach(item -> {
                        // 签约数
                        item.setSignCount(device.getSignCount());
                        // 激励金
                        item.setIncentivePrice(device.getIncentivePrice());
                        // 尺寸规格
                        item.setSizeSpec(pair.getLeft());
                        // 大屏
                        item.setLargeScreen(pair.getRight());
                    });
                });
    }
}
