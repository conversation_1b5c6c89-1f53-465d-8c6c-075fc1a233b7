package com.coocaa.ad.cheese.cms.venue.controller.contract;

import cn.hutool.json.JSONUtil;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractService;
import com.coocaa.ad.cheese.cms.common.tools.common.result.ResultTemplate;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractApplyStatusEnum;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractHackParam;
import com.coocaa.ad.cheese.cms.venue.controller.BaseController;
import com.coocaa.ad.cheese.cms.venue.service.ContractApprovalService;
import com.coocaa.ad.cheese.cms.venue.service.ContractNotifyService;
import com.coocaa.ad.cheese.cms.venue.service.ContractTemplateService;
import com.coocaa.ad.cheese.cms.venue.service.ContractWriteService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * 物业合同测试接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-18
 */
@Slf4j
@RestController
@RequestMapping("/venue/contract/{contractId}/hack")
@Tag(name = "物业合同测试接口", description = "物业合同测试接口")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ContractHackController extends BaseController {
    private final ContractWriteService contractWriteService;
    private final ContractNotifyService contractNotifyService;
    private final ContractApprovalService contractApprovalService;
    private final ContractTemplateService contractTemplateService;
    private final IContractService contractService;

    /**
     * 合同状态变更，通知其它系统
     */
    @Operation(summary = "合同状态变更，通知其它系统")
    @PutMapping("/notify")
    public ResultTemplate<Boolean> notify(@PathVariable(name = "contractId") Integer contractId,
                                          @RequestParam(name = "status") String status) {
        ContractApplyStatusEnum statusEnum = ContractApplyStatusEnum.parse(status);
        if (Objects.isNull(contractId) || contractId <= 0 || Objects.isNull(status)) {
            return ResultTemplate.fail("参数错误");
        }
        log.info("[HACK] 合同:{}, 状态:{} 变化，通知其它系统", contractId, status);
        return ResultTemplate.success(contractNotifyService.notifyOnStatusChanged(contractId, statusEnum));
    }

    /**
     * 合同审批通过
     */
    @Operation(summary = "合同审批通过")
    @PutMapping("/approve/passed")
    public ResultTemplate<Boolean> approvePassed(@PathVariable(name = "contractId") Integer contractId) {
        log.info("[HACK] 合同:{}, 审批通过", contractId);
        return ResultTemplate.success(contractWriteService.changeContractStatusOnApprovalPassed(contractId));
    }

    /**
     * 合同审批拒绝
     */
    @Operation(summary = "合同审批拒绝")
    @PutMapping("/approve/reject")
    public ResultTemplate<Boolean> approveReject(@PathVariable(name = "contractId") Integer contractId) {
        log.info("[HACK] 合同:{}, 审批拒绝", contractId);
        return ResultTemplate.success(contractWriteService.changeContractStatusOnApprovalReject(contractId));
    }

    /**
     * 重新提交申请单
     */
    @Operation(summary = "重新提交申请单")
    @PutMapping("/submit")
    @Transactional(rollbackFor = Exception.class)
    public ResultTemplate<Boolean> submitContract(@PathVariable(name = "contractId") Integer contractId) {
        log.info("[HACK] 合同:{}, 重新提交", contractId);

        // 提交合同申请单
        contractWriteService.submitContract(contractId, true, false);

        return ResultTemplate.success(true);
    }

    /**
     * 修改合同信息
     */
    @Operation(summary = "修改合同信息")
    @PutMapping("/change")
    public ResultTemplate<Boolean> changeContract(@PathVariable(name = "contractId") Integer contractId,
                                                  @RequestBody ContractHackParam contractParam) {
        log.info("[HACK] 合同:{}, 修改内容:{}", contractId, JSONUtil.toJsonStr(contractParam));
        boolean result = contractService.lambdaUpdate()
                .set(Objects.nonNull(contractParam.getBusinessType()), ContractEntity::getBusinessType, contractParam.getBusinessType())
                .set(Objects.nonNull(contractParam.getCityId()), ContractEntity::getCityId, contractParam.getCityId())
                .set(Objects.nonNull(contractParam.getStartDate()), ContractEntity::getStartDate, contractParam.getStartDate())
                .set(Objects.nonNull(contractParam.getEndDate()), ContractEntity::getEndDate, contractParam.getEndDate())
                .set(Objects.nonNull(contractParam.getPeriod()), ContractEntity::getPeriod, contractParam.getPeriod())
                .set(Objects.nonNull(contractParam.getTotalAmount()), ContractEntity::getTotalAmount, contractParam.getTotalAmount())
                .set(Objects.nonNull(contractParam.getAgentName()), ContractEntity::getAgentName, contractParam.getAgentName())
                .set(Objects.nonNull(contractParam.getAgentApprover()), ContractEntity::getAgentApprover, contractParam.getAgentApprover())
                .set(Objects.nonNull(contractParam.getInvoiceType()), ContractEntity::getInvoiceType, contractParam.getInvoiceType())
                .set(Objects.nonNull(contractParam.getTaxPoint()), ContractEntity::getTaxPoint, contractParam.getTaxPoint())
                .set(Objects.nonNull(contractParam.getContactPerson()), ContractEntity::getContactPerson, contractParam.getContactPerson())
                .set(Objects.nonNull(contractParam.getContactPhone()), ContractEntity::getContactPhone, contractParam.getContactPhone())
                .set(Objects.nonNull(contractParam.getContactEmail()), ContractEntity::getContactEmail, contractParam.getContactEmail())
                .set(Objects.nonNull(contractParam.getContactAddress()), ContractEntity::getContactAddress, contractParam.getContactAddress())
                .set(Objects.nonNull(contractParam.getDescription()), ContractEntity::getDescription, contractParam.getDescription())
                .set(Objects.nonNull(contractParam.getFollower()), ContractEntity::getFollower, contractParam.getFollower())
                .set(Objects.nonNull(contractParam.getNormalFlag()), ContractEntity::getNormalFlag, contractParam.getNormalFlag())
                .set(Objects.nonNull(contractParam.getThirdFlag()), ContractEntity::getThirdFlag, contractParam.getThirdFlag())
                .set(Objects.nonNull(contractParam.getGiftFlag()), ContractEntity::getGiftFlag, contractParam.getGiftFlag())
                .set(Objects.nonNull(contractParam.getFormalFlag()), ContractEntity::getFormalFlag, contractParam.getFormalFlag())
                .set(Objects.nonNull(contractParam.getUploadFlag()), ContractEntity::getUploadFlag, contractParam.getUploadFlag())
                .set(Objects.nonNull(contractParam.getUploadSubFlag()), ContractEntity::getUploadSubFlag, contractParam.getUploadSubFlag())
                .set(Objects.nonNull(contractParam.getSealParty1Flag()), ContractEntity::getSealParty1Flag, contractParam.getSealParty1Flag())
                .set(Objects.nonNull(contractParam.getSealParty1Date()), ContractEntity::getSealParty1Date, contractParam.getSealParty1Date())
                .set(Objects.nonNull(contractParam.getSealParty2Flag()), ContractEntity::getSealParty2Flag, contractParam.getSealParty2Flag())
                .set(Objects.nonNull(contractParam.getSealParty2Date()), ContractEntity::getSealParty2Date, contractParam.getSealParty2Date())
                .set(Objects.nonNull(contractParam.getSealParty3Flag()), ContractEntity::getSealParty3Flag, contractParam.getSealParty3Flag())
                .set(Objects.nonNull(contractParam.getSealParty3Date()), ContractEntity::getSealParty3Date, contractParam.getSealParty3Date())
                .set(Objects.nonNull(contractParam.getSealType()), ContractEntity::getSealType, contractParam.getSealType())
                .set(Objects.nonNull(contractParam.getApplyStatus()), ContractEntity::getApplyStatus, contractParam.getApplyStatus())
                .set(Objects.nonNull(contractParam.getFormalStatus()), ContractEntity::getFormalStatus, contractParam.getFormalStatus())
                .set(Objects.nonNull(contractParam.getArchiveFlag()), ContractEntity::getArchiveFlag, contractParam.getArchiveFlag())
                .set(Objects.nonNull(contractParam.getArchiveRemark()), ContractEntity::getArchiveRemark, contractParam.getArchiveRemark())
                .set(Objects.nonNull(contractParam.getArchiveTime()), ContractEntity::getArchiveTime, contractParam.getArchiveTime())
                .set(Objects.nonNull(contractParam.getBuildingLevel()), ContractEntity::getBuildingLevel, contractParam.getBuildingLevel())
                .set(Objects.nonNull(contractParam.getDeleteFlag()), ContractEntity::getDeleteFlag, contractParam.getDeleteFlag())
                .eq(ContractEntity::getId, contractId).update();
        return ResultTemplate.success(result);
    }

    /**
     * 按模板生成WORD文件
     */
    @Operation(summary = "按模板生成WORD文件")
    @Parameter(name = "id", description = "合同ID", required = true, example = "1")
    @GetMapping("/template")
    public ResultTemplate<String> generateTemplate(@PathVariable(name = "contractId") Integer contractId,
                                                   @RequestParam(name = "force", required = false, defaultValue = "false") boolean force) {
        log.info("[HACK] 合同:{}, 按模板生成WORD文件", contractId);
        return ResultTemplate.success(contractTemplateService.generate(contractId, force));
    }
}
