package com.coocaa.ad.cheese.cms.venue.service;

import com.alibaba.fastjson2.JSON;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractDevicePointEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractPriceApplyEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractProjectEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractDevicePointService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractPriceApplyService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractProjectService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractService;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractApplyStatusEnum;
import com.coocaa.ad.cheese.cms.venue.bean.notify.BusinessStatusChangeDTO;
import com.coocaa.ad.cheese.cms.venue.bean.notify.ContractProjectPointDTO;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import com.coocaa.ad.common.user.UserCacheHelper;
import com.coocaa.ad.common.user.bean.CachedUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 合同审批通知服务
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-14
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ContractNotifyService {
    private static final String TOPIC_NAME = "cheese-venue-contract-point";
    private static final String CONTRACT_BUSINESS_TOPIC_NAME = "cheese-building-status-change-log";
    private static final String BUSINESS_STATUS_CHANGE = "business-status-change";
    private final KafkaTemplate<String, String> kafkaTemplate;
    private final IContractService contractService;
    private final IContractProjectService projectService;
    private final IContractPriceApplyService priceApplyService;
    private final IContractDevicePointService devicePointService;
    private final UserCacheHelper userCacheHelper;

    /**
     * 发送合同审批通知
     *
     * @param contractId 合同ID
     * @param status     合同状态
     * @return true:调用成功
     */
    public boolean notifyOnStatusChanged(Integer contractId, ContractApplyStatusEnum status) {
        ContractEntity contract = contractService.getById(contractId);
        if (Objects.isNull(contract)) {
            log.warn("[发送合同({})状态变更通知] 合同不存在", contractId);
            return false;
        }

        // 查询合同使用的项目
        List<ContractProjectEntity> projects = projectService.lambdaQuery()
                .select(ContractProjectEntity::getId, ContractProjectEntity::getProjectCode,
                        ContractProjectEntity::getProjectName,
                        ContractProjectEntity::getStartDate, ContractProjectEntity::getEndDate)
                .eq(ContractProjectEntity::getContractId, contractId)
                .list();
        if (CollectionUtils.isEmpty(projects)) {
            log.warn("[发送合同({})状态变更通知] 未关联项目", contractId);
            return false;
        }

        // 查询项目下的点位
        Map<Integer, List<String>> projectPointMap = devicePointService.lambdaQuery()
                .select(ContractDevicePointEntity::getProjectId, ContractDevicePointEntity::getCode)
                .eq(ContractDevicePointEntity::getContractId, contractId)
                .list().stream()
                .collect(Collectors.groupingBy(ContractDevicePointEntity::getProjectId,
                        Collectors.mapping(ContractDevicePointEntity::getCode, Collectors.toList())));
        if (MapUtils.isEmpty(projectPointMap)) {
            log.warn("[发送合同({})状态变更通知] 未关联设备点", contractId);
            return false;
        }

        // 价格申请列表
        Map<Integer, List<String>> priceApplyMap = priceApplyService.lambdaQuery()
                .select(ContractPriceApplyEntity::getProjectId, ContractPriceApplyEntity::getApplyCode)
                .eq(ContractPriceApplyEntity::getContractId, contractId)
                .list().stream()
                .collect(Collectors.groupingBy(ContractPriceApplyEntity::getProjectId,
                        Collectors.mapping(ContractPriceApplyEntity::getApplyCode, Collectors.toList())));

        // 组装通知数据
        ContractProjectPointDTO projectPoint = new ContractProjectPointDTO()
                .setContractId(contract.getId())
                .setStatus(status.getCode())
                .setProjects(projects.stream().map(project -> {
                    return new ContractProjectPointDTO.Project()
                            .setCode(project.getProjectCode())
                            .setName(project.getProjectName())
                            .setStartDate(project.getStartDate())
                            .setEndDate(project.getEndDate())
                            .setPriceApplyCodes(priceApplyMap.getOrDefault(project.getId(), Collections.emptyList()))
                            .setPointCodes(projectPointMap.getOrDefault(project.getId(), Collections.emptyList()));
                }).toList());

        try {
            String json = JSON.toJSONString(projectPoint);
            log.info("[发送合同({})状态变更通知] \n\t原始数据: {} \n\tJSON: {}", contract.getId(), projectPoint, json);
            kafkaTemplate.send(new ProducerRecord<>(TOPIC_NAME, String.valueOf(contract.getId()), json));
            return true;
        } catch (Exception ex) {
            log.error("发送合同({})状态变更通知失败, {}", contract.getId(), ex.getMessage(), ex);
            return false;
        }
    }


    /**
     * 通知楼宇系统，商机状态发生变更
     *
     * @param contractId 合同ID
     * @param statusCode 商机状态, 方案报价(审批拒绝):0043-4, 合同阶段(提交时):0043-5, 成交(归档时):0043-6
     */
    public boolean notifyBusinessOnStatusChanged(Integer contractId, String statusCode) {
        // 查询合同使用的项目(商机)
        List<ContractProjectEntity> projects = projectService.lambdaQuery()
                .select(ContractProjectEntity::getId, ContractProjectEntity::getProjectCode,
                        ContractProjectEntity::getProjectName,
                        ContractProjectEntity::getStartDate, ContractProjectEntity::getEndDate)
                .eq(ContractProjectEntity::getContractId, contractId)
                .list();
        if (CollectionUtils.isEmpty(projects)) {
            return false;
        }

        // 更新用户相关信息
        Integer userId = 0;
        CachedUser user = UserThreadLocal.getUser();
        if (Objects.isNull(user) || Objects.isNull(user.getId())) {
            user = userCacheHelper.getUser(userId);
        }

        String wno = null, name = null;
        if (Objects.nonNull(user) && Objects.nonNull(user.getId())) {
            wno = user.getWno();
            name = user.getName();
            userId = user.getId();
        }

        // 发送状态变化消息
        for (ContractProjectEntity project : projects) {
            try {
                // 发送到业务系统
                BusinessStatusChangeDTO businessStatusChangeDTO = new BusinessStatusChangeDTO();
                businessStatusChangeDTO.setBusinessCode(project.getProjectCode());
                businessStatusChangeDTO.setStatus(statusCode);
                businessStatusChangeDTO.setOperatorId(userId);
                businessStatusChangeDTO.setChangeTime(LocalDateTime.now());
                businessStatusChangeDTO.setOperatorWno(wno);
                businessStatusChangeDTO.setOperatorName(name);
                String jsonData = JSON.toJSONString(businessStatusChangeDTO);
                kafkaTemplate.send(BUSINESS_STATUS_CHANGE, jsonData);
                log.info("发送商机({})状态变更通知成功,topic:{},message:{}", project.getProjectCode(),  BUSINESS_STATUS_CHANGE, jsonData);
            } catch (Exception ex) {
                log.error("发送商机({})状态变更通知失败, {}", project.getProjectCode(), ex.getMessage(), ex);
            }
        }
        return true;
    }
}
