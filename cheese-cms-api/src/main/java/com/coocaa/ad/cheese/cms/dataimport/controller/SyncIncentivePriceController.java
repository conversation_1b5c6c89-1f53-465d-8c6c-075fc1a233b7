package com.coocaa.ad.cheese.cms.dataimport.controller;

import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractDeviceEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractDevicePointEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractPriceApplyEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractDevicePointService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractDeviceService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractPriceApplyService;
import com.coocaa.ad.cheese.cms.common.tools.common.result.ResultTemplate;
import com.coocaa.ad.cheese.cms.venue.rpc.FeignMethH5Rpc;
import com.coocaa.ad.cheese.cms.venue.rpc.vo.PriceApplyDevicePointJoinVO;
import com.coocaa.ad.common.anno.AuthIgnore;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 同步设备激励金
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-29 09:56
 */
@Slf4j
@RestController
@RequestMapping("/sync/incentive-price")
@Tag(name = "同步设备激励金", description = "同步设备激励金")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class SyncIncentivePriceController {
    private final IContractDeviceService contractDeviceService;
    private final IContractDevicePointService contractDevicePointService;
    private final IContractPriceApplyService priceApplyService;
    private final FeignMethH5Rpc feignMethH5Rpc;

    @Operation(summary = "同步设备激励金")
    @AuthIgnore
    @PutMapping
    public ResultTemplate<Integer> syncIncentivePrice() {
        log.info("同步设备激励金");
        // 查询所有需要同步的设备记录
        Map<Integer, Integer> deviceMap = contractDeviceService.lambdaQuery()
                .select(ContractDeviceEntity::getId, ContractDeviceEntity::getPriceApplyId, ContractDeviceEntity::getIncentivePrice)
                .eq(ContractDeviceEntity::getIncentivePrice, "")
                .or()
                .isNull(ContractDeviceEntity::getIncentivePrice)
                .list().stream()
                .collect(Collectors.toMap(ContractDeviceEntity::getId, ContractDeviceEntity::getPriceApplyId));
        if (MapUtils.isEmpty(deviceMap)) {
            log.warn("未找到需要同步的激励金的设备记录！");
            return null;
        }
        // 查询设备记录对应的价格申请code
        Map<Integer, String> priceApplyCodeMap = priceApplyService.lambdaQuery()
                .select(ContractPriceApplyEntity::getId, ContractPriceApplyEntity::getApplyCode)
                .in(ContractPriceApplyEntity::getId, deviceMap.values())
                .list().stream()
                .collect(Collectors.toMap(ContractPriceApplyEntity::getId, ContractPriceApplyEntity::getApplyCode));
        if (MapUtils.isEmpty(priceApplyCodeMap)) {
            log.warn("未找到需要同步激励金的价格申请记录！");
            return null;
        }
        // 查询设备记录对应的点位code
        Map<String, Set<Integer>> pointCodeMap = contractDevicePointService.lambdaQuery()
                .select(ContractDevicePointEntity::getDeviceId, ContractDevicePointEntity::getCode)
                .in(ContractDevicePointEntity::getDeviceId, deviceMap.keySet())
                .list().stream()
                .filter(e -> Objects.nonNull(e.getCode()))
                .collect(Collectors.groupingBy(ContractDevicePointEntity::getCode, Collectors.mapping(ContractDevicePointEntity::getDeviceId, Collectors.toSet())));

        // H5请求价格申请数据
        List<PriceApplyDevicePointJoinVO> priceApplyDevicePointJoinVOS = feignMethH5Rpc.getIncentivePrice(pointCodeMap.keySet()).getData();
        if (CollectionUtils.isEmpty(priceApplyDevicePointJoinVOS)) {
            log.warn("未找到需要同步激励金点位的价格申请记录！");
            return null;
        }

        // H5请求设备上的“项目激励金”数据
        Map<Integer, ContractDeviceEntity> updateEntityMap = new HashMap<>(128);
        for (PriceApplyDevicePointJoinVO vo : priceApplyDevicePointJoinVOS) {
            // 确定设备ID
            Set<Integer> deviceIds = pointCodeMap.get(vo.getPointCode());
            if (CollectionUtils.isEmpty(deviceIds)) {
                log.warn("点位[code={}]未找到对应的设备记录！", vo.getPointCode());
                continue;
            }
            // 处理设备数据
            for (Integer deviceId : deviceIds) {
                Integer priceApplyId = deviceMap.getOrDefault(deviceId, 0);
                if (Objects.equals(priceApplyId, 0)) {
                    log.warn("设备[id={}]未找到对应的价格申请记录！", deviceId);
                    continue;
                }
                // 匹配价格申请
                String priceApplyCode = priceApplyCodeMap.getOrDefault(priceApplyId, "");
                if (priceApplyCode.equalsIgnoreCase(vo.getApplyCode())) {
                    // 记录更新对象
                    ContractDeviceEntity entity = new ContractDeviceEntity();
                    entity.setId(deviceId);
                    entity.setIncentivePrice(vo.getIncentivePrice());
                    updateEntityMap.put(deviceId, entity);
                }
            }
        }

        // 更新数据
        log.info("下列{}条设备激励金数据将被更新：{}", updateEntityMap.size(), updateEntityMap.values().stream().map(ContractDeviceEntity::getId).toList());
        if (CollectionUtils.isNotEmpty(updateEntityMap.values())) {
            contractDeviceService.updateBatchById(updateEntityMap.values());
            log.info("{}条设备激励金数据更新成功", updateEntityMap.size());
        }

        return ResultTemplate.success(updateEntityMap.size());
    }
}
