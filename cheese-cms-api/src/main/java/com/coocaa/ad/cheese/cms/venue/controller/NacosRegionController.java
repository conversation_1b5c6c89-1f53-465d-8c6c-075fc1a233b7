package com.coocaa.ad.cheese.cms.venue.controller;

import com.coocaa.ad.cheese.cms.common.tools.common.result.ResultTemplate;
import com.coocaa.ad.cheese.cms.venue.service.NacosRegionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@Deprecated
@Slf4j
@RestController
@RequestMapping("/nacos/region")
@Tag(name = "城市大区管理", description = "城市大区管理")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class NacosRegionController {
    private final NacosRegionService nacosRegionService;

    /**
     * 根据城市名称获取城市大区信息
     */
    @Operation(summary = "根据城市名称获取城市大区信息")
    @GetMapping("/{cityName}")
    public ResultTemplate<String> getRegionInfoByCityName(@PathVariable(name = "cityName") String cityName) {
        return ResultTemplate.success(nacosRegionService.getRegionInfoByCityName(cityName));
    }

    /**
     * 根据城市名称获取城市大区信息
     */
    @Operation(summary = "根据城市名称批量获取城市大区信息")
    @GetMapping("/batch")
    public ResultTemplate<Map<String, String>> getRegionInfoByCityNames(@RequestParam(name = "cityNames") List<String> cityName) {
        return ResultTemplate.success(nacosRegionService.getRegionInfoByCityNames(cityName));
    }

    /**
     * 获取所有大区信息
     */
    @Operation(summary = "获取所有大区信息")
    @GetMapping("/children")
    public ResultTemplate<List<String>> getAllRegionInfo() {
        return ResultTemplate.success(nacosRegionService.getAllRegion());
    }

    /**
     * 根据大区信息获取下属城市
     */
    @Operation(summary = "根据大区信息获取下属城市")
    @GetMapping("/getCityNameByRegions")
    public ResultTemplate<List<String>> getCityNameByRegions(@RequestParam("regionList") List<String> regionList) {
        return ResultTemplate.success(nacosRegionService.getCityNameByRegions(regionList));
    }

}
