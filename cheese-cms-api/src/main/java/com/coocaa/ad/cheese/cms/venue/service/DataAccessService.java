package com.coocaa.ad.cheese.cms.venue.service;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.AgentEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.DataAccessEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IAgentService;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IDataAccessService;
import com.coocaa.ad.cheese.cms.common.exception.BusinessException;
import com.coocaa.ad.cheese.cms.common.rpc.FeignAuthorityRpc;
import com.coocaa.ad.cheese.cms.common.tools.common.bean.common.CodeNameVO;
import com.coocaa.ad.cheese.cms.common.tools.common.bean.common.UserVO;
import com.coocaa.ad.cheese.cms.common.tools.common.result.PageRequestVo;
import com.coocaa.ad.cheese.cms.common.tools.common.result.PageResponseVo;
import com.coocaa.ad.cheese.cms.common.tools.common.result.ResultTemplate;
import com.coocaa.ad.cheese.cms.common.tools.common.util.StringUtils;
import com.coocaa.ad.cheese.cms.common.tools.venue.bean.UserDataAccessDTO;
import com.coocaa.ad.cheese.cms.common.tools.venue.bean.UserDataAccessV2DTO;
import com.coocaa.ad.cheese.cms.common.tools.venue.constant.VenueConstants;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.DataAccessTypeEnum;
import com.coocaa.ad.cheese.cms.common.util.CodeNameHelper;
import com.coocaa.ad.cheese.cms.venue.bean.access.DataAccessAgentParam;
import com.coocaa.ad.cheese.cms.venue.bean.access.DataAccessBatchParam;
import com.coocaa.ad.cheese.cms.venue.bean.access.DataAccessCityParam;
import com.coocaa.ad.cheese.cms.venue.bean.access.DataAccessParam;
import com.coocaa.ad.cheese.cms.venue.bean.access.DataAccessQueryParam;
import com.coocaa.ad.cheese.cms.venue.convert.access.DataAccessConvert;
import com.coocaa.ad.cheese.cms.venue.rpc.FeignMethWebRpc;
import com.coocaa.ad.cheese.cms.venue.vo.access.DataAccessVO;
import com.coocaa.ad.common.core.context.UserThreadLocal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class DataAccessService {
    private final FeignAuthorityRpc feignAuthorityRpc;
    private final IDataAccessService dataAccessService;
    private final StringRedisTemplate stringRedisTemplate;
    private final CodeNameHelper codeNameHelper;
    private final IAgentService agentService;
    private final FeignMethWebRpc feignMethWebRpc;


    /**
     * 查询当前登陆用户的数据访问权限
     */
    public UserDataAccessDTO getUserDataAccess() {
        Integer userId = UserThreadLocal.getUserId();
        String cacheKey = VenueConstants.USER_DATA_ACCESS_KEY + userId;
        String dataAccessCache = stringRedisTemplate.opsForValue().get(cacheKey);
        if (StringUtils.isNotBlank(dataAccessCache)) {
            try {
                return JSON.parseObject(dataAccessCache, UserDataAccessDTO.class);
            } catch (Exception e) {
                log.error("从缓存中获取用户[{}]的数据访问权限反序列化异常！详情为：{}", userId, e.getMessage());
                return null;
            }
        }

        List<Integer> all = List.of(0);
        UserDataAccessDTO result;

        // 超管，看全部 
        if (Objects.equals(userId, 0)) {
            log.info("超级管理员，拥有所有数据访问权限");
            result = new UserDataAccessDTO().setAccessType(DataAccessTypeEnum.ALL).setUserIds(all).setCityIds(all);
            stringRedisTemplate.opsForValue().set(cacheKey, JSON.toJSONString(result), 12, TimeUnit.HOURS);
            return result;
        }

        // 查询用户的数据权限
        DataAccessEntity entity = dataAccessService.lambdaQuery()
                .select(DataAccessEntity::getUserId, DataAccessEntity::getAccessType, DataAccessEntity::getCityListStr)
                .eq(DataAccessEntity::getUserId, UserThreadLocal.getUserId()).last("LIMIT 1").one();

        // 无数据权限
        DataAccessTypeEnum accessType = null;
        if (Objects.isNull(entity) || Objects.isNull(accessType = DataAccessTypeEnum.parse(entity.getAccessType()))) {
            log.info("用户({})未配置数据访问权限", userId);
            return null;
        }

        // 查全部
        if (Objects.equals(DataAccessTypeEnum.ALL, accessType)) {
            log.info("用户({})权限类型:{}, 可查用户:{}, 可查城市:{}", userId, accessType.getDesc(), "所有", "所有");
            result = new UserDataAccessDTO().setAccessType(accessType).setUserIds(all).setCityIds(all);
            stringRedisTemplate.opsForValue().set(cacheKey, JSON.toJSONString(result), 12, TimeUnit.HOURS);
            return result;
        }

        // 解析城市ID
        List<Integer> cityIds = Collections.emptyList();
        if (StringUtils.isNotBlank(entity.getCityListStr())) {
            cityIds = Optional.ofNullable(JSON.parseArray(entity.getCityListStr(), DataAccessCityParam.class))
                    .map(city -> city.stream().map(DataAccessCityParam::getId).toList())
                    .orElse(Collections.emptyList());
        }
        // 看自己
        if (Objects.equals(DataAccessTypeEnum.SELF, accessType)) {
            log.info("用户({})权限类型:{}, 可查用户:{}, 可查城市:{}", userId, accessType.getDesc(), "自己", StringUtils.join(cityIds, ","));
            result = new UserDataAccessDTO().setAccessType(accessType).setUserIds(List.of(userId)).setCityIds(cityIds);
            stringRedisTemplate.opsForValue().set(cacheKey, JSON.toJSONString(result), 12, TimeUnit.HOURS);
            return result;
        }

        // 看所有城市
        if (Objects.equals(DataAccessTypeEnum.CITY_ALL, accessType)) {
            log.info("用户({})权限类型:{}, 可查用户:{}, 可查城市:{}", userId, accessType.getDesc(), "不限制", StringUtils.join(cityIds, ","));
            result = new UserDataAccessDTO().setAccessType(accessType).setUserIds(Collections.emptyList())
                    .setCityIds(cityIds);
            stringRedisTemplate.opsForValue().set(cacheKey, JSON.toJSONString(result), 12, TimeUnit.HOURS);
            return result;
        }

        // 看自己及下属
        if (Objects.equals(DataAccessTypeEnum.SELF_AND_SUB, accessType)) {
            List<Integer> userIds = Optional.ofNullable(feignAuthorityRpc.getSubordinates(userId))
                    .map(ResultTemplate::getData).orElse(List.of(userId));
            log.info("用户({})权限类型:{}, 可查用户:{}, 可查城市:{}", userId, accessType.getDesc(), StringUtils.join(userIds, ","), StringUtils.join(cityIds, ","));
            result = new UserDataAccessDTO().setAccessType(accessType).setUserIds(userIds).setCityIds(cityIds);
            stringRedisTemplate.opsForValue().set(cacheKey, JSON.toJSONString(result), 12, TimeUnit.HOURS);
            return result;
        }
        return null;
    }

    /**
     * 获取用户数据权限
     * <p>
     * 公司    null -> 不限制
     * 用户ID  null -> 不限制
     * 城市ID  null -> 不限制
     *
     * @return
     */
    public UserDataAccessV2DTO getUserDataAccessV2() {
        Integer userId = UserThreadLocal.getUserId();

        // 尝试从缓存获取
        UserDataAccessV2DTO cachedAccess = getFromCache(userId);
        if (cachedAccess != null) {
            return cachedAccess;
        }

        // 超管特殊处理
        if (Objects.equals(userId, 0)) {
            return createAndCacheAccess(userId, createSuperAdminAccess());
        }

        // 查询用户的数据权限
        DataAccessEntity entity = dataAccessService.lambdaQuery().eq(DataAccessEntity::getUserId, userId)
                .last("LIMIT 1").one();

        // 无数据权限
        DataAccessTypeEnum accessType = entity != null ? DataAccessTypeEnum.parse(entity.getAccessType()) : null;
        if (accessType == null) {
            log.info("用户({})未配置数据访问权限", userId);
            return null;
        }

        // 处理不同访问类型
        UserDataAccessV2DTO result = processAccessType(userId, accessType, entity);
        return createAndCacheAccess(userId, result);
    }

    private UserDataAccessV2DTO getFromCache(Integer userId) {
        String cacheKey = VenueConstants.USER_DATA_ACCESS_KEY_V2 + userId;
        String dataAccessCache = stringRedisTemplate.opsForValue().get(cacheKey);
        if (StringUtils.isNotBlank(dataAccessCache)) {
            log.info("从缓存中获取用户[{}]的数据访问权限", userId);
            try {
                return JSON.parseObject(dataAccessCache, UserDataAccessV2DTO.class);
            } catch (Exception e) {
                log.error("从缓存中获取用户[{}]的数据访问权限反序列化异常！详情为：{}", userId, e.getMessage());
            }
        }
        return null;
    }

    private UserDataAccessV2DTO createAndCacheAccess(Integer userId, UserDataAccessV2DTO access) {
        if (access != null) {
            String cacheKey = VenueConstants.USER_DATA_ACCESS_KEY_V2 + userId;
            stringRedisTemplate.opsForValue().set(cacheKey, JSON.toJSONString(access), 12, TimeUnit.HOURS);
        }
        return access;
    }

    private UserDataAccessV2DTO createSuperAdminAccess() {
        log.info("超级管理员，拥有所有数据访问权限");
        List<CodeNameVO> cities = Optional.ofNullable(feignAuthorityRpc.getAvailableCities())
                .map(ResultTemplate::getData).orElse(Collections.emptyList());
        List<Integer> allCityIds = cities.stream().map(CodeNameVO::getId).toList();
        return new UserDataAccessV2DTO().setAccessType(DataAccessTypeEnum.ALL.getCode()).setCityIds(allCityIds);
    }

    private UserDataAccessV2DTO processAccessType(Integer userId, DataAccessTypeEnum accessType, DataAccessEntity entity) {
        List<Integer> cityIds = parseCityIds(entity.getCityListStr());
        List<Integer> agentIds = parseAgentIds(entity.getAgentListStr());
        if (agentIds.contains(0)) {
            // 可查看所有代理商
            agentIds = null;
        }
        //目前可用城市
        List<CodeNameVO> cities = Optional.ofNullable(feignAuthorityRpc.getAvailableCities())
                .map(ResultTemplate::getData).orElse(Collections.emptyList());
        List<Integer> allCityIds = cities.stream().map(CodeNameVO::getId).toList();
        if (cityIds.contains(0)) {
            // 获取所有城市
            cityIds = allCityIds;
        } else {
            // 过滤掉不存在的城市
            cityIds = cityIds.stream().filter(allCityIds::contains).toList();
        }
        UserDataAccessV2DTO result = new UserDataAccessV2DTO();
        result.setAccessType(accessType.getCode());

        return switch (accessType) {
            case ALL -> {
                log.info("用户({})权限类型:{}, 可查用户:{}, 可查城市:{}", userId, accessType.getDesc(), "所有", "所有");
                yield result.setCityIds(allCityIds);
            }
            case SELF -> {
                log.info("用户({})权限类型:{}, 可查用户:{}, 可查城市:{}", userId, accessType.getDesc(), "自己", StringUtils.join(cityIds, ","));
                yield result.setUserIds(List.of(userId)).setCityIds(cityIds);
            }
            case CITY_ALL -> {
                log.info("用户({})权限类型:{}, 可查用户:{}, 可查城市:{}", userId, accessType.getDesc(), "不限制", StringUtils.join(cityIds, ","));
                yield result.setAgentIds(agentIds).setCityIds(cityIds);
            }
            case SELF_AND_SUB -> {
                List<Integer> userIds = Optional.ofNullable(feignAuthorityRpc.getSubordinates(userId))
                        .map(ResultTemplate::getData).orElse(List.of(userId));
                log.info("用户({})权限类型:{}, 可查用户:{}, 可查城市:{}", userId, accessType.getDesc(), StringUtils.join(userIds, ","), StringUtils.join(cityIds, ","));
                yield result.setUserIds(userIds).setCityIds(cityIds);
            }
            default -> null;
        };
    }

    private List<Integer> parseAgentIds(String agentListStr) {
        if (StringUtils.isBlank(agentListStr)) {
            return Collections.emptyList();
        }
        return Optional.ofNullable(JSON.parseArray(agentListStr, DataAccessAgentParam.class))
                .map(city -> city.stream().map(DataAccessAgentParam::getId).toList()).orElse(Collections.emptyList());
    }

    private List<Integer> parseCityIds(String cityListStr) {
        if (StringUtils.isBlank(cityListStr)) {
            return Collections.emptyList();
        }
        return Optional.ofNullable(JSON.parseArray(cityListStr, DataAccessCityParam.class))
                .map(city -> city.stream().map(DataAccessCityParam::getId).toList()).orElse(Collections.emptyList());
    }


    /**
     * 创建或更新用户数据权限
     *
     * @param accessParam 申请供应商的参数
     * @return 用户ID
     */
    public Integer createOrUpdateDataAccess(DataAccessParam accessParam) {
        // 验证参数
        validateParam(accessParam);

        // 根据用户ID确认此用户是否已经存在
        DataAccessEntity entity = dataAccessService.lambdaQuery()
                .eq(DataAccessEntity::getUserId, accessParam.getUserId()).one();
        if (Objects.nonNull(entity)) {
            accessParam.setId(entity.getId());
        }
        // applyParam 转换成DataAccessEntity
        DataAccessEntity dataAccessEntity = DataAccessConvert.INSTANCE.toEntity(accessParam);
        dataAccessEntity.setCityListStr(convertCityIdsToCityListStr(accessParam.getCityList()));
        dataAccessEntity.setAgentListStr(convertAgentIdsToAgentListStr(accessParam.getAgentList()));

        boolean result = dataAccessService.saveOrUpdate(dataAccessEntity);
        if (result) {
            stringRedisTemplate.delete(VenueConstants.USER_DATA_ACCESS_KEY + accessParam.getUserId());
            stringRedisTemplate.delete(VenueConstants.USER_DATA_ACCESS_KEY_V2 + accessParam.getUserId());
            // 同步用户标签
            feignMethWebRpc.initUserTag(accessParam.getWno());
            return dataAccessEntity.getUserId();
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    public Integer batchCreateOrUpdateAccess(DataAccessBatchParam accessParam) {
        // 验证参数并预处理
        validateAndPrepareBatchParam(accessParam);

        // 获取并验证用户列表
        List<UserVO> userList = getUserList(accessParam.getUserList());

        // 准备公共数据
        String cityListStr = convertCityIdsToCityListStr(accessParam.getCityList());
        String agentListStr = convertAgentIdsToAgentListStr(accessParam.getAgentList());

        // 批量构建实体
        List<DataAccessEntity> entities = userList.stream()
                .map(user -> buildDataAccessEntity(user, accessParam.getAccessType(), cityListStr, agentListStr))
                .collect(Collectors.toList());

        // 批量保存或更新
        boolean success = dataAccessService.saveOrUpdateBatch(entities);
        if (!success) {
            throw new BusinessException("批量保存用户数据权限失败");
        }

        // 清除缓存
        clearUserAccessCache(userList);
        return entities.size();
    }

    private void validateAndPrepareBatchParam(DataAccessBatchParam accessParam) {
        if (accessParam == null || CollectionUtils.isEmpty(accessParam.getUserList())) {
            throw new IllegalArgumentException("参数不能为空");
        }

        String accessType = accessParam.getAccessType();
        if (StringUtils.isBlank(accessType) || DataAccessTypeEnum.parse(accessType) == null) {
            throw new IllegalArgumentException("无效的访问权限类型");
        }

        // 处理全部访问权限的特殊情况
        if (Objects.equals(accessType, DataAccessTypeEnum.ALL.getCode())) {
            accessParam.setCityList(List.of(0));
        } else if (CollectionUtils.isEmpty(accessParam.getCityList())) {
            throw new IllegalArgumentException("城市列表不能为空");
        }
    }

    private List<UserVO> getUserList(List<Integer> userIds) {
        ResultTemplate<List<UserVO>> result = feignAuthorityRpc.getUserByIds(userIds);
        if (!result.getSuccess()) {
            throw new BusinessException("请求权限系统接口失败");
        }

        List<UserVO> userList = result.getData();
        if (CollectionUtils.isEmpty(userList)) {
            throw new BusinessException("用户不存在");
        }
        return userList;
    }

    private DataAccessEntity buildDataAccessEntity(UserVO user, String accessType, String cityListStr, String agentListStr) {
        DataAccessEntity entity = dataAccessService.lambdaQuery().eq(DataAccessEntity::getUserId, user.getId()).one();

        DataAccessEntity dataAccessEntity = new DataAccessEntity();
        if (entity != null) {
            dataAccessEntity.setId(entity.getId());
        }

        dataAccessEntity.setUserId(user.getId());
        dataAccessEntity.setUserName(user.getName());
        dataAccessEntity.setWno(user.getWno());
        dataAccessEntity.setAccessType(accessType);
        dataAccessEntity.setCityListStr(cityListStr);
        dataAccessEntity.setAgentListStr(agentListStr);
        return dataAccessEntity;
    }

    private void clearUserAccessCache(List<UserVO> userList) {
        userList.forEach(user -> {
            stringRedisTemplate.delete(VenueConstants.USER_DATA_ACCESS_KEY + user.getId());
            stringRedisTemplate.delete(VenueConstants.USER_DATA_ACCESS_KEY_V2 + user.getId());
        });
    }

    private String convertAgentIdsToAgentListStr(List<Integer> agentList) {
        List<DataAccessAgentParam> result = new ArrayList<>();
        if (agentList.contains(0)) {
            result.add(new DataAccessAgentParam(0, "全部"));
            return JSON.toJSONString(result);
        }
        if (CollectionUtils.isEmpty(agentList)) {
            return JSON.toJSONString(result);
        }
        List<AgentEntity> agentEntities = agentService.lambdaQuery().in(AgentEntity::getId, agentList).list();
        if (CollectionUtils.isEmpty(agentEntities)) {
            throw new BusinessException("代理商不存在");
        }
        agentEntities.forEach(item -> {
            result.add(new DataAccessAgentParam(item.getId(), item.getAgentName()));
        });
        return JSON.toJSONString(result);
    }

    private String convertCityIdsToCityListStr(List<Integer> cityList) {
        List<DataAccessCityParam> result = new ArrayList<>();
        if (cityList.contains(0)) {
            result.add(new DataAccessCityParam(0, "全部"));
            return JSON.toJSONString(result);
        }
        if (CollectionUtils.isEmpty(cityList)) {
            return JSON.toJSONString(result);
        }
        Map<Integer, String> cityMapping = codeNameHelper.getCityMapping(cityList);
        if (MapUtils.isEmpty(cityMapping)) {
            return JSON.toJSONString(result);
        }
        cityMapping.forEach((k, v) -> result.add(new DataAccessCityParam(k, v)));
        return JSON.toJSONString(result);
    }

    /**
     * 验证参数
     *
     * @param accessParam 数据权限参数
     */
    private void validateParam(DataAccessParam accessParam) {
        // 如果数据权限是查看全部，则不能设置城市
        if (Objects.equals(accessParam.getAccessType(), DataAccessTypeEnum.ALL.getCode())) {
            accessParam.setCityList(List.of(0));
        } else {
            if (CollectionUtils.isEmpty(accessParam.getCityList())) {
                throw new IllegalArgumentException("城市列表不能为空");
            }
        }
    }

    /**
     * 分页获取数据权限列表
     *
     * @param pageRequest 分页参数
     * @return 数据权限分页数据结果
     */
    public PageResponseVo<DataAccessVO> getDadaAccessList(PageRequestVo<DataAccessQueryParam> pageRequest) {
        // 验证并规范化分页参数
        validateAndNormalizePageParams(pageRequest);

        // 构建查询条件
        LambdaQueryWrapper<DataAccessEntity> queryWrapper = buildQueryWrapper(pageRequest.getQuery());

        // 执行分页查询
        Page<DataAccessEntity> page = dataAccessService.page(new Page<>(pageRequest.getCurrentPage(), pageRequest.getPageSize()), queryWrapper);

        if (CollectionUtils.isEmpty(page.getRecords())) {
            return new PageResponseVo<>(page.getCurrent(), page.getSize(), 0L, 0L, Collections.emptyList(), 0L);
        }

        // 转换基础数据
        List<DataAccessVO> dataAccessList = page.getRecords().stream().map(DataAccessConvert.INSTANCE::toVO)
                .collect(Collectors.toList());

        // 填充访问类型名称
        dataAccessList.forEach(vo -> vo.setAccessTypeName(accessTypeToName(vo.getAccessType())));

        return new PageResponseVo<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), dataAccessList, page.getTotal());
    }

    private String accessTypeToName(String accessType) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(accessType)) {
            return null;
        }
        DataAccessTypeEnum dataAccessTypeEnum = DataAccessTypeEnum.parse(accessType);
        if (dataAccessTypeEnum == null) {
            return null;
        }
        return dataAccessTypeEnum.getDesc();
    }


    /**
     * 验证并规范化分页参数
     */
    private void validateAndNormalizePageParams(PageRequestVo<?> pageRequest) {
        pageRequest.setCurrentPage(Optional.ofNullable(pageRequest.getCurrentPage()).filter(page -> page > 0)
                .orElse(1L));

        pageRequest.setPageSize(Optional.ofNullable(pageRequest.getPageSize()).filter(size -> size > 0)
                .orElse(VenueConstants.DEFAULT_PAGE_SIZE));
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<DataAccessEntity> buildQueryWrapper(DataAccessQueryParam query) {
        LambdaQueryWrapper<DataAccessEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (query == null) {
            return queryWrapper;
        }

        return queryWrapper.like(StringUtils.isNotBlank(query.getWno()), DataAccessEntity::getWno, query.getWno())
                .like(StringUtils.isNotBlank(query.getUserName()), DataAccessEntity::getUserName, query.getUserName())
                .eq(StringUtils.isNotBlank(query.getAccessType()), DataAccessEntity::getAccessType, query.getAccessType())
                .like(query.getCityId() != null, DataAccessEntity::getCityListStr, getCityNameById(query.getCityId()))
                .orderByDesc(DataAccessEntity::getCreateTime);
    }

    private String getCityNameById(Integer cityId) {
        if (cityId != null) {
            if (cityId == 0) {
                return "全部";
            }
            Map<Integer, String> cityMapping = codeNameHelper.getCityMapping(List.of(cityId));
            if (MapUtils.isNotEmpty(cityMapping)) {
                return cityMapping.get(cityId);
            }
        }
        return "";
    }

    /**
     * 删除用户数据权限
     *
     * @param id 用户数据权限ID
     */
    public void deleteDataAccess(Integer id) {
        DataAccessEntity dataAccessEntity = dataAccessService.getById(id);
        if (dataAccessEntity == null) {
            return;
        }
        dataAccessService.removeById(id);
        stringRedisTemplate.delete(VenueConstants.USER_DATA_ACCESS_KEY + dataAccessEntity.getUserId());
        stringRedisTemplate.delete(VenueConstants.USER_DATA_ACCESS_KEY_V2 + dataAccessEntity.getUserId());
    }

    /**
     * 获取用户数据权限详情
     *
     * @param wno 用户ID
     */
    public DataAccessVO getUserDataAccessDetail(String wno) {
        DataAccessEntity entity = dataAccessService.lambdaQuery().eq(DataAccessEntity::getWno, wno).last("LIMIT 1")
                .one();
        // 如果entity为空，则返回null 或者用户权限是看自己或者看自己及下属则返回null
        if (entity == null || Objects.equals(entity.getAccessType(), DataAccessTypeEnum.SELF.getCode()) || Objects.equals(entity.getAccessType(), DataAccessTypeEnum.SELF_AND_SUB.getCode())) {
            return null;
        }
        return DataAccessConvert.INSTANCE.toVO(entity);
    }

    /**
     * 根据用户工号获取用户数据权限详情
     *
     * @param wno
     * @return
     */
    public UserDataAccessV2DTO getWnoDataAccessV2(String wno) {
        ResultTemplate<UserVO> userInfo = feignAuthorityRpc.getUserInfo(wno);
        if (userInfo.getSuccess() == false || userInfo.getData() == null) {
            log.error("根据工号获取用户信息失败，wno:{}", wno);
            return null;
        }
        Integer userId = userInfo.getData().getId();

        // 尝试从缓存获取
        UserDataAccessV2DTO cachedAccess = getFromCache(userId);
        if (cachedAccess != null) {
            return cachedAccess;
        }

        // 超管特殊处理
        if (Objects.equals(userId, 0)) {
            return createAndCacheAccess(userId, createSuperAdminAccess());
        }

        // 查询用户的数据权限
        DataAccessEntity entity = dataAccessService.lambdaQuery().eq(DataAccessEntity::getUserId, userId)
                .last("LIMIT 1").one();

        // 无数据权限
        DataAccessTypeEnum accessType = entity != null ? DataAccessTypeEnum.parse(entity.getAccessType()) : null;
        if (accessType == null) {
            log.info("用户({})未配置数据访问权限", userId);
            return null;
        }

        // 处理不同访问类型
        UserDataAccessV2DTO result = processAccessType(userId, accessType, entity);
        return createAndCacheAccess(userId, result);
    }

    public List<String> getAllConfigUsers(){
        return dataAccessService.lambdaQuery()
                .select(DataAccessEntity::getWno).list()
                .stream().map(DataAccessEntity::getWno).distinct().collect(Collectors.toList());
    }
}
