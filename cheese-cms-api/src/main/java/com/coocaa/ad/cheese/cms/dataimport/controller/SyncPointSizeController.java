package com.coocaa.ad.cheese.cms.dataimport.controller;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.ContractDevicePointEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.service.IContractDevicePointService;
import com.coocaa.ad.cheese.cms.common.tools.common.result.ResultTemplate;
import com.coocaa.ad.cheese.cms.venue.rpc.FeignMethWebRpc;
import com.coocaa.ad.cheese.cms.venue.vo.building.PointDto;
import com.coocaa.ad.common.anno.AuthIgnore;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 同步点位尺寸
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-21
 */
@Slf4j
@RestController
@RequestMapping("/sync/point-size")
@Tag(name = "同步点位尺寸", description = "同步点位尺寸")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class SyncPointSizeController {
    private final FeignMethWebRpc feignMethWebRpc;
    private final IContractDevicePointService devicePointService;

    @Operation(summary = "同步点位尺寸")
    @AuthIgnore
    @PutMapping
    public ResultTemplate<Integer> syncPointSize(@RequestParam(name = "contractId", required = false) Integer contractId) {
        // 查询要同步点位尺寸的点位信息
        StopWatch stopWatch = new StopWatch("同步点位尺寸");

        stopWatch.start("获取点位信息");
        LambdaQueryChainWrapper<ContractDevicePointEntity> queryWrapper = devicePointService.lambdaQuery()
                .select(ContractDevicePointEntity::getId, ContractDevicePointEntity::getCode);
        if (Objects.nonNull(contractId) && contractId > 0) {
            queryWrapper.eq(ContractDevicePointEntity::getContractId, contractId);
        } else {
            queryWrapper.last("WHERE LENGTH(size) <= 0");
        }
        List<ContractDevicePointEntity> points = queryWrapper.list();
        stopWatch.stop();

        // 建立点位编号与点位id的映射关系
        Map<Integer, String> pointMapping = points.stream()
                .collect(Collectors.toMap(ContractDevicePointEntity::getId, ContractDevicePointEntity::getCode));

        // 调用楼宇服务获取点位尺寸
        List<String> pointCodes = points.stream().map(ContractDevicePointEntity::getCode).distinct().toList();
        log.info("查询到[{}]个点位编码", pointCodes.size());
        Map<String, String> pointCodeSizeMapping = Maps.newHashMapWithExpectedSize(pointCodes.size());
        List<List<String>> partitionPointCodes = Lists.partition(pointCodes, 100);

        stopWatch.start("获取点位尺寸");
        int count = partitionPointCodes.size(), index = 0;
        for (List<String> codes : partitionPointCodes) {
            index++;
            Map<String, String> codeSizeMap = Optional.ofNullable(feignMethWebRpc.listPointSizeByCodes(codes))
                    .map(ResultTemplate::getData)
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(point -> StringUtils.isNotBlank(point.getCode()))
                    .filter(point -> StringUtils.isNotBlank(point.getDeviceSize()))
                    .collect(Collectors.toMap(PointDto::getCode, PointDto::getDeviceSize));
            log.info("调用远程服务[{}/{}]获取点位尺寸: {}", index, count, codeSizeMap);
            if (MapUtils.isNotEmpty(codeSizeMap)) {
                pointCodeSizeMapping.putAll(codeSizeMap);
            }
        }
        stopWatch.stop();

        if (MapUtils.isEmpty(pointCodeSizeMapping)) {
            log.info("所有点位都没有找到尺寸！！！");
            log.info("查询点位尺寸消耗: {}", stopWatch.prettyPrint());
            return ResultTemplate.success(0);
        }

        // 更新点位尺寸
        stopWatch.start("更新点位尺寸");
        count = points.size();
        index = 0;
        for (ContractDevicePointEntity point : points) {
            index++;
            String pointCode = pointMapping.get(point.getId());
            if (StringUtils.isBlank(pointCode)) {
                continue;
            }

            String pointSize = pointCodeSizeMapping.get(pointCode);
            if (StringUtils.isBlank(pointSize)) {
                continue;
            }
            log.info("更新[{}/{}]点位尺寸, 点位:{}, 尺寸:{}", index, count, pointCode, pointSize);
            devicePointService.lambdaUpdate()
                    .eq(ContractDevicePointEntity::getId, point.getId())
                    .set(ContractDevicePointEntity::getSize, pointSize)
                    .update();
        }
        stopWatch.stop();
        log.info("更新点位尺寸消耗: {}", stopWatch.prettyPrint());

        return ResultTemplate.success(index);
    }
}
