package com.coocaa.ad.cheese.cms.venue.vo.contract;

import com.coocaa.ad.cheese.cms.common.tools.venue.constant.VenueConstants;
import com.coocaa.ad.cheese.cms.common.util.translate.VenueTransTypes;
import com.coocaa.ad.translate.anno.TransField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 合同操作记录
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-10
 */
@Data
public class ContractLogVO {
    @Schema(description = "日志ID", type = "integer", example = "1")
    private Integer id;

    @Schema(description = "合同ID", type = "integer", example = "1")
    private Integer contractId;

    @Schema(description = "操作说明", type = "string", example = "甲方盖章")
    private String content;

    @Schema(description = "操作历史")
    private List<ContractHistoryVO> history;

    @Schema(description = "创建人", type = "Integer", example = "1")
    @TransField(type = VenueTransTypes.USER)
    private Integer creator;
    private String creatorName;

    @Schema(description = "操作时间", type = "String", example = "2024-01-01 12:00:00")
    @JsonFormat(pattern = VenueConstants.DATE_TIME_FORMAT)
    private LocalDateTime createTime;
}
