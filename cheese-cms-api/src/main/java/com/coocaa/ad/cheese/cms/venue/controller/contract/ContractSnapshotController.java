package com.coocaa.ad.cheese.cms.venue.controller.contract;

import cn.hutool.core.bean.BeanUtil;
import com.coocaa.ad.cheese.cms.common.tools.common.result.ResultTemplate;
import com.coocaa.ad.cheese.cms.common.tools.common.util.AesUtils;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractDetailDiffParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractSnapshotParam;
import com.coocaa.ad.cheese.cms.venue.service.ContractSnapshotService;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractDetailDiffVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractDetailVO;
import com.coocaa.ad.common.anno.AuthIgnore;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;
import java.util.Optional;

/**
 * 合同快照数据管理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-12 19:38
 */
@Slf4j
@RestController
@RequestMapping("/venue/contract/snapshot")
@Tag(name = "合同快照数据管理", description = "合同快照数据管理")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ContractSnapshotController {
    private final ContractSnapshotService contractSnapshotService;

    @Operation(summary = "合同快照保存")
    @PostMapping
    public ResultTemplate<Boolean> saveSnapshot(@RequestBody ContractSnapshotParam param) {
        return ResultTemplate.success(contractSnapshotService.saveSnapshot(param));
    }

    @Operation(summary = "合同快照数据详情")
    @Parameter(name = "id", description = "合同快照ID", required = true, example = "1")
    @GetMapping("/{id}/detail")
    public ResultTemplate<ContractDetailVO> convertSnapshotDetail(@PathVariable(name = "id") @NotBlank String idText) {
        int id;
        try {
            id = Integer.parseInt(idText);
        } catch (Exception e) {
            id = Integer.parseInt(Optional.ofNullable(AesUtils.decryptStr(idText)).orElse("0"));
        }
        if (Objects.equals(0, id)) {
            return ResultTemplate.fail("合同快照ID不正确");
        }
        return ResultTemplate.success(contractSnapshotService.convertSnapshotDetail(id));
    }

    @AuthIgnore
    @Operation(summary = "合同快照数据详情（免登录）")
    @PostMapping("/detail")
    public ResultTemplate<ContractDetailDiffVO> convertSnapshotDetail2(@RequestBody ContractDetailDiffParam param) {
        int id = Integer.parseInt(Optional.ofNullable(AesUtils.decryptStr(param.getEncryptId())).orElse("0"));
        if (Objects.equals(0, id)) {
            return ResultTemplate.fail("合同快照ID不正确");
        }
        ContractDetailVO detailVO = contractSnapshotService.convertSnapshotDetail(id);
        if (Objects.isNull(detailVO)) {
            return ResultTemplate.fail("合同快照数据不存在");
        }
        ContractDetailDiffVO detailDiffVO = BeanUtil.toBean(detailVO, ContractDetailDiffVO.class);
        detailDiffVO.setReplaceItems(param.getReplaceItems());
        // detailDiffVO.setAddItems(param.getAddItems());
        return ResultTemplate.success(detailDiffVO);
    }
}
