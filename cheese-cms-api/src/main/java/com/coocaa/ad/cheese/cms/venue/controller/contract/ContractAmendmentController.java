package com.coocaa.ad.cheese.cms.venue.controller.contract;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.coocaa.ad.cheese.cms.common.tools.common.exception.CommonException;
import com.coocaa.ad.cheese.cms.common.tools.common.result.PageRequestVo;
import com.coocaa.ad.cheese.cms.common.tools.common.result.PageResponseVo;
import com.coocaa.ad.cheese.cms.common.tools.common.result.ResultTemplate;
import com.coocaa.ad.cheese.cms.common.tools.common.util.AesUtils;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractTypeEnum;
import com.coocaa.ad.cheese.cms.common.validation.ValidationGroup;
import com.coocaa.ad.cheese.cms.download.enums.DownLoadTypeEnum;
import com.coocaa.ad.cheese.cms.download.service.AnomalousContractService;
import com.coocaa.ad.cheese.cms.download.service.ContractChangeProjectPayService;
import com.coocaa.ad.cheese.cms.download.service.ContractChangeService;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractAmendmentApplyParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractArchiveParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractAttachmentParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractCancelParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractQueryParam;
import com.coocaa.ad.cheese.cms.venue.controller.contract.helper.ContractValidateHelper;
import com.coocaa.ad.cheese.cms.venue.service.ContractAmendmentService;
import com.coocaa.ad.cheese.cms.venue.service.ContractReadService;
import com.coocaa.ad.cheese.cms.venue.validation.contract.ApplyValidatorFactory;
import com.coocaa.ad.cheese.cms.venue.validation.contract.BusinessAvailableValidator;
import com.coocaa.ad.cheese.cms.venue.vo.building.PointDetail;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractDetailDiffVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractEditDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractPageVO;
import com.coocaa.ad.common.anno.AuthIgnore;
import com.coocaa.ad.downloader.bean.dto.TaskDTO;
import com.google.common.collect.Sets;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;
import java.util.concurrent.TimeUnit;

/**
 * 变更合同控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-07 14:36
 */
@Slf4j
@RestController
@RequestMapping("/venue/contract/amendment")
@Tag(name = "变更合同管理", description = "变更合同管理")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ContractAmendmentController {
    // 新写一个service来封装合同原始的逻辑
    private final ContractAmendmentService contractAmendmentService;
    private final ContractReadService contractReadService;
    private final StringRedisTemplate stringRedisTemplate;
    private final ApplyValidatorFactory validatorFactory;
    private final BusinessAvailableValidator businessAvailableValidator;
    private final Validator validator;
    private final ContractValidateHelper contractValidateHelper;
    private final ContractChangeProjectPayService contractChangeProjectPayService;
    private final ContractChangeService contractChangeService;

    // 合同类型：变更合同
    private static final List<Integer> CONTRACT_TYPES = List.of(ContractTypeEnum.AMENDMENT.getCode());


    /**
     * 变更合同详情
     */
    @Operation(summary = "变更合同详情")
    @Parameter(name = "id", description = "变更合同ID", required = true, example = "1")
    @GetMapping("/{id}")
    public ResultTemplate<ContractDetailVO> getDetail(@PathVariable(name = "id") Integer id) {
        return ResultTemplate.success(contractAmendmentService.getAmendmentDetail(id, false));
    }

    /**
     * 变更合同详情（免登陆）
     */
    @AuthIgnore
    @Operation(summary = "变更合同详情")
    @Parameter(name = "id", description = "变更合同ID", required = true, example = "1")
    @GetMapping("/detail")
    public ResultTemplate<ContractDetailDiffVO> getDetailWithoutAuth(@RequestParam(name = "id") String encryptedId) {
        String plainText = AesUtils.decryptStr(encryptedId);
        Integer id = StringUtils.isBlank(plainText) ? null : NumberUtils.toInt(plainText);
        if (id == null || id <= 0) {
            throw new CommonException("变更合同ID不正确");
        }
        return ResultTemplate.success(contractReadService.contractDetailDiffWrapper(
                contractAmendmentService.getAmendmentDetail(id, true)
        ));
    }

    /**
     * 变更合同申请单详情
     */
    @Operation(summary = "变更合同申请单详情")
    @Parameter(name = "id", description = "变更合同ID", required = true, example = "1")
    @GetMapping("/apply/{id}")
    public ResultTemplate<ContractEditDetailVO> getSupplementApplyDetail(
            @PathVariable(name = "id") Integer id,
            @RequestParam(name = "force", required = false, defaultValue = "false") boolean force) {
        return ResultTemplate.success(contractAmendmentService.getAmendmentApplyDetail(id, force));
    }

    /**
     * 变更合同列表(分页)
     */
    @Operation(summary = "变更合同列表(分页)")
    @PostMapping("/list")
    public ResultTemplate<PageResponseVo<ContractPageVO>> pageListAmendments(@RequestBody PageRequestVo<ContractQueryParam> pageRequest) {
        pageRequest.setQuery(Optional.ofNullable(pageRequest.getQuery()).orElseGet(ContractQueryParam::new));
        pageRequest.getQuery().setContractTypes(CONTRACT_TYPES);
        return ResultTemplate.success(contractAmendmentService.pageListAmendments(pageRequest));
    }

    /**
     * 变更合同导出
     */
    @Operation(summary = "变更合同导出")
    @PostMapping("/export")
    public ResultTemplate<Long> exportContracts(@RequestBody ContractQueryParam queryParam) {
        queryParam.setContractTypes(CONTRACT_TYPES);
        TaskDTO taskDTO = new TaskDTO().setExecuteParams(JSON.toJSONString(queryParam));
        return ResultTemplate.success(this.contractChangeService.downloader(taskDTO));
    }

    /**
     * 上传变更合同附件
     */
    @Operation(summary = "上传变更合同附件")
    @Parameter(name = "id", description = "变更合同ID", required = true, in = ParameterIn.QUERY, example = "1")
    @PostMapping("/attachments")
    public ResultTemplate<Boolean> uploadAttachment(@RequestParam(name = "id") Integer id,
                                                    @RequestBody @Validated List<ContractAttachmentParam> attachments) {
        return CollectionUtils.isEmpty(attachments)
                ? ResultTemplate.fail("附件列表不能为空")
                : ResultTemplate.success(contractAmendmentService.uploadAttachments(id, attachments));
    }

    /**
     * 变更合同归档
     */
    @Operation(summary = "变更合同归档")
    @Parameter(name = "id", description = "变更合同ID", required = true, example = "1")
    @PutMapping("/{id}/archive")
    public ResultTemplate<Boolean> archiveContract(@PathVariable(name = "id") Integer id,
                                                   @Validated @RequestBody ContractArchiveParam archiveParam) {
        return ResultTemplate.success(contractAmendmentService.archiveContract(id, archiveParam));
    }

    /**
     * 变更合同作废
     */
    @Operation(summary = "变更合同作废")
    @Parameter(name = "id", description = "变更合同ID", required = true, example = "1")
    @PutMapping("/{id}/cancel")
    public ResultTemplate<Boolean> cancelContract(@PathVariable(name = "id") Integer id,
                                                  @Validated(ValidationGroup.GroupFirst.class)
                                                  @RequestBody ContractCancelParam cancelParam) {
        return ResultTemplate.success(contractAmendmentService.cancelContract(id, cancelParam));
    }

    /**
     * 变更合同驳回
     */
    @Operation(summary = "变更合同驳回")
    @Parameter(name = "id", description = "变更合同ID", required = true, example = "1")
    @PutMapping("/{id}/reject")
    public ResultTemplate<Boolean> rejectContract(@PathVariable(name = "id") Integer id,
                                                  @Validated(ContractArchiveParam.RejectGroup.class)
                                                  @RequestBody ContractArchiveParam rejectParam) {
        return ResultTemplate.success(contractAmendmentService.rejectContract(id, rejectParam));
    }

    @Operation(summary = "新增合同变更")
    @Parameter(name = "Token", description = "Token", in = ParameterIn.HEADER, required = true)
    @Parameter(name = "submit", description = "暂存还是提交", in = ParameterIn.QUERY)
    @PostMapping
    public ResultTemplate<Integer> create(@RequestParam(name = "submit", required = false, defaultValue = "false") boolean submit,
                                          @RequestBody ContractAmendmentApplyParam contractAmendmentApplyParam) {
        if (Objects.nonNull(contractAmendmentApplyParam.getId()) && contractAmendmentApplyParam.getId() > 0) {
            log.info("新增合同变更id不正确---{}", contractAmendmentApplyParam);
            return ResultTemplate.fail("id不正确");
        }
        // 特殊处理合同ID
        if (Objects.nonNull(contractAmendmentApplyParam.getId())) {
            contractAmendmentApplyParam.setId(null);
        }

        String validateResult = paramCheck(contractAmendmentApplyParam, submit);
        if (com.coocaa.ad.cheese.cms.common.tools.common.util.StringUtils.isNotBlank(validateResult)) {
            log.info("新增合同变更参数校验不通过---{},{}", contractAmendmentApplyParam, validateResult);
            return ResultTemplate.fail(validateResult);
        }

        // 供应商有效性验证
        String supplierCheckRet = contractValidateHelper.doValidateForSupplier(contractAmendmentApplyParam);
        if (StringUtils.isNotBlank(supplierCheckRet)) {
            return ResultTemplate.fail(supplierCheckRet);
        }

        // 提交时进行拦截：提交合同时,包含大屏点位,判断是否已完成大屏评级,包含小屏点位,判断是否已完成小屏评级
        if (submit) {
            String ret = contractValidateHelper.doInterceptOnSubmit(contractAmendmentApplyParam);
            if (StringUtils.isNotBlank(ret)) {
                return ResultTemplate.fail(ret);
            }
        }

        String redisKey = String.format("contract:amendment:%s", contractAmendmentApplyParam.getParentId());
        log.info("新增合同变更 redisKey---{}", redisKey);
        try {
            Boolean locked = stringRedisTemplate.opsForValue().setIfAbsent(redisKey, "1", 2, TimeUnit.SECONDS);
            if (BooleanUtil.isTrue(locked)) {
                log.info("新增合同变更 redisKey 加锁成功---{},有效期2s", redisKey);
                if (contractAmendmentService.canCreateContractAmendment(contractAmendmentApplyParam.getParentId(), null)) {
                    log.info("新增合同变更 redisKey 加锁成功---{},有效期2s", redisKey);
                    Integer id = contractAmendmentService.createOrUpdateContractAmendment(submit, contractAmendmentApplyParam);
                    return ResultTemplate.success(id);
                } else {
                    log.info("新增合同变更 存在在途合同变更或者合同状态不正确---{}", contractAmendmentApplyParam.getParentId());
                    return ResultTemplate.fail("存在流程中的变更，或者合同状态不正确");
                }
            } else {
                log.info("新增合同变更 redisKey {} 加锁失败,重复提交", redisKey);
                return ResultTemplate.fail("请勿重复提交");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            stringRedisTemplate.delete(redisKey);
            return ResultTemplate.fail("操作失败");
        }
    }

    @Operation(summary = "编辑合同变更")
    @Parameter(name = "Token", description = "Token", in = ParameterIn.HEADER, required = true)
    @Parameter(name = "submit", description = "暂存还是提交", in = ParameterIn.QUERY)
    @PutMapping
    public ResultTemplate<Integer> edit(@RequestParam(name = "submit", required = false, defaultValue = "false") boolean submit,
                                        @RequestBody ContractAmendmentApplyParam contractAmendmentApplyParam) {
        // 特殊处理合同ID
        if (Objects.isNull(contractAmendmentApplyParam.getId()) || contractAmendmentApplyParam.getId() <= 0) {
            log.info("编辑合同变更 id不正确---{}", contractAmendmentApplyParam);
            return ResultTemplate.fail("id不正确");
        }

        String validateResult = paramCheck(contractAmendmentApplyParam, submit);
        if (com.coocaa.ad.cheese.cms.common.tools.common.util.StringUtils.isNotBlank(validateResult)) {
            log.info("编辑合同变更 参数校验不通过---{},{}", contractAmendmentApplyParam, validateResult);
            return ResultTemplate.fail(validateResult);
        }

        // 供应商有效性验证
        String supplierCheckRet = contractValidateHelper.doValidateForSupplier(contractAmendmentApplyParam);
        if (StringUtils.isNotBlank(supplierCheckRet)) {
            return ResultTemplate.fail(supplierCheckRet);
        }

        // 提交时进行拦截：提交合同时,包含大屏点位,判断是否已完成大屏评级,包含小屏点位,判断是否已完成小屏评级
        if (submit) {
            String ret = contractValidateHelper.doInterceptOnSubmit(contractAmendmentApplyParam);
            if (StringUtils.isNotBlank(ret)) {
                return ResultTemplate.fail(ret);
            }
        }

        String redisKey = String.format("contract:amendment:%s", contractAmendmentApplyParam.getParentId());
        log.info("编辑合同变更 redisKey---{}", redisKey);
        try {
            Boolean locked = stringRedisTemplate.opsForValue().setIfAbsent(redisKey, "1", 2, TimeUnit.SECONDS);
            if (BooleanUtil.isTrue(locked)) {
                log.info("编辑合同变更 redisKey 加锁成功---{},有效期2s", redisKey);
                if (contractAmendmentService.canCreateContractAmendment(contractAmendmentApplyParam.getParentId(), contractAmendmentApplyParam.getId())) {
                    Integer id = contractAmendmentService.createOrUpdateContractAmendment(submit, contractAmendmentApplyParam);
                    return ResultTemplate.success(id);
                } else {
                    log.info("编辑合同变更 存在在途合同变更或者合同状态不正确---{}", contractAmendmentApplyParam.getParentId());
                    return ResultTemplate.fail("存在流程中的变更，或者合同状态不正确");
                }
            } else {
                log.info("编辑合同变更 redisKey {} 加锁失败,重复提交", redisKey);
                return ResultTemplate.fail("请勿重复提交");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            stringRedisTemplate.delete(redisKey);
            return ResultTemplate.fail("操作失败");
        }
    }

    @Operation(summary = "查询是否可以发起合同变更申请")
    @Parameter(name = "Token", description = "Token", in = ParameterIn.HEADER, required = true)
    @GetMapping("{contractId}/can-create")
    public ResultTemplate<Boolean> canCreate(@PathVariable("contractId") Integer contractId) {
        boolean canCreate = contractAmendmentService.canCreateContractAmendment(contractId, null);
        return ResultTemplate.success(canCreate);
    }

    @Operation(summary = "查询项目点位")
    @Parameter(name = "Token", description = "Token", in = ParameterIn.HEADER, required = true)
    @GetMapping("points/{businessCode}")
    public ResultTemplate<List<PointDetail>> getProjectPoints(@PathVariable("businessCode") String businessCode) {

        List<PointDetail> pointDetails = contractAmendmentService.pointByBusinessCode(businessCode);
        return ResultTemplate.success(pointDetails);
    }

    /**
     * 检验参数
     *
     * @param contractAmendmentApplyParam
     * @param submit
     * @return
     */
    private String paramCheck(ContractAmendmentApplyParam contractAmendmentApplyParam, Boolean submit) {
        if (ObjectUtil.isAllNotEmpty(contractAmendmentApplyParam, submit) && submit) {
            List<String> validateResults;
            StringJoiner errorMessage = new StringJoiner(", ");
            Set<ConstraintViolation<ContractAmendmentApplyParam>> violations = validator.validate(contractAmendmentApplyParam, ValidationGroup.ContractAmendment.class);
            if (!violations.isEmpty()) {
                for (ConstraintViolation<ContractAmendmentApplyParam> violation : violations) {
                    errorMessage.add(violation.getMessage());
                }
            }

            // 如果有错误信息，则返回错误信息
            if (errorMessage.length() > 0) {
                return errorMessage.toString();
            }

            // 参数检查结果
            validateResults = validatorFactory.validate(contractAmendmentApplyParam, submit, true,
                    Sets.newHashSet(businessAvailableValidator));
            if (CollectionUtil.isNotEmpty(validateResults)) {
                log.error("合同变更参数校验失败: \n{}", String.join("\n", validateResults));
                return String.join(", ", validateResults);
            }
        }
        return null;
    }

    @Operation(summary = "合同变更导出-项目+付款周期维度")
    @PostMapping("/project-pay/export")
    public ResultTemplate<Long> exportAmendmentsWithPayPeriod(@RequestBody ContractQueryParam queryParam) {
        queryParam.setContractTypes(CONTRACT_TYPES);
        TaskDTO taskDTO = new TaskDTO().setExecuteParams(JSON.toJSONString(queryParam));
        return ResultTemplate.success(this.contractChangeProjectPayService.downloader(taskDTO));
    }
}
