package com.coocaa.ad.cheese.cms.venue.convert.contract;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.coocaa.ad.cheese.cms.common.tools.venue.bean.ContractAbnormalApprovalDTO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.AbContractApprovalVO;
import com.coocaa.ad.common.result.PageResponseVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-24
 */
@Mapper
public interface AbContractConvert {

    AbContractConvert INSTANCE = Mappers.getMapper(AbContractConvert.class);

    /**
     * dto转成vo
     */
    AbContractApprovalVO toVo(ContractAbnormalApprovalDTO dto);

    /**
     * 转换成分页结果
     */
    @Mappings({
            @Mapping(source = "current", target = "currentPage"),
            @Mapping(source = "total", target = "totalRows"),
            @Mapping(source = "size", target = "pageSize"),
            @Mapping(source = "pages", target = "totalPages"),
            @Mapping(source = "records", target = "rows")
    })
    PageResponseVO<AbContractApprovalVO> toPageResponse(IPage<ContractAbnormalApprovalDTO> pagedRoles);
}
