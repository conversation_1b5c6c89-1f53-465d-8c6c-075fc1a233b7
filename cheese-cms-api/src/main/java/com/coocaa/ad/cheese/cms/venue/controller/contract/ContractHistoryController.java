package com.coocaa.ad.cheese.cms.venue.controller.contract;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.BooleanUtil;
import com.alibaba.fastjson2.JSON;
import com.coocaa.ad.cheese.cms.common.tools.common.bean.common.CodeNameVO;
import com.coocaa.ad.cheese.cms.common.tools.common.constant.ContractConstants;
import com.coocaa.ad.cheese.cms.common.tools.common.enums.BooleFlagEnum;
import com.coocaa.ad.cheese.cms.common.tools.common.result.PageRequestVo;
import com.coocaa.ad.cheese.cms.common.tools.common.result.PageResponseVo;
import com.coocaa.ad.cheese.cms.common.tools.common.result.ResultTemplate;
import com.coocaa.ad.cheese.cms.common.tools.common.util.StringUtils;
import com.coocaa.ad.cheese.cms.common.tools.venue.enums.ContractTypeEnum;
import com.coocaa.ad.cheese.cms.common.validation.ValidationGroup;
import com.coocaa.ad.cheese.cms.download.enums.DownLoadTypeEnum;
import com.coocaa.ad.cheese.cms.download.service.ValidContractHistoryProjectPayService;
import com.coocaa.ad.cheese.cms.download.service.ValidContractHistoryService;
import com.coocaa.ad.cheese.cms.venue.bean.contract.BatchContractTransferParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractCancelParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractEditParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractQueryParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.ContractTransferParam;
import com.coocaa.ad.cheese.cms.venue.controller.BaseController;
import com.coocaa.ad.cheese.cms.venue.controller.contract.helper.ContractValidateHelper;
import com.coocaa.ad.cheese.cms.venue.rpc.FeignMethH5Rpc;
import com.coocaa.ad.cheese.cms.venue.service.ContractExportService;
import com.coocaa.ad.cheese.cms.venue.service.ContractReadService;
import com.coocaa.ad.cheese.cms.venue.service.ContractWriteService;
import com.coocaa.ad.cheese.cms.venue.vo.building.PointDetail;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractCommonEditDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractDetailVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.ContractPageVO;
import com.coocaa.ad.downloader.bean.dto.TaskDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;
import java.util.concurrent.TimeUnit;

/**
 * 历史合同管理（飞书导入）
 *
 * <AUTHOR>
 * @since 2025-02-10
 */
@Slf4j
@RestController
@RequestMapping("/venue/contract/history")
@Tag(name = "历史合同管理", description = "历史合同管理")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class ContractHistoryController extends BaseController {
    private final ContractReadService contractReadService;
    private final ContractWriteService contractWriteService;
    private final ContractExportService contractExportService;
    private final StringRedisTemplate stringRedisTemplate;
    private final Validator validator;
    private final FeignMethH5Rpc feignMethH5Rpc;
    private final ContractValidateHelper contractValidateHelper;
    private final ValidContractHistoryService validContractHistoryService;
    private final ValidContractHistoryProjectPayService validContractHistoryProjectPayService;

    // 合同类型，历史合同类型为3
    private static final List<Integer> CONTRACT_TYPES = List.of(ContractTypeEnum.HISTORY.getCode());

    /**
     * 历史合同列表(分页)
     */
    @Operation(summary = "历史合同列表(分页)")
    @Parameter(name = "Token", description = "Token", in = ParameterIn.HEADER, required = true)
    @PostMapping("/list")
    public ResultTemplate<PageResponseVo<ContractPageVO>> pageListHistoryContracts(@RequestBody PageRequestVo<ContractQueryParam> pageRequest) {
        pageRequest.setQuery(Optional.ofNullable(pageRequest.getQuery()).orElseGet(ContractQueryParam::new));
        pageRequest.getQuery().setContractTypes(CONTRACT_TYPES);
        return ResultTemplate.success(contractReadService.pageListApplies(pageRequest));
    }

    /**
     * 历史合同详情
     */
    @Operation(summary = "历史合同详情")
    @Parameter(name = "id", description = "合同ID", required = true, example = "1")
    @GetMapping("/{id}")
    public ResultTemplate<ContractDetailVO> getHistoricDetail(@PathVariable(name = "id") Integer id) {
        return ResultTemplate.success(contractReadService.getContractHistoricDetail(id));
    }

    /**
     * 历史合同详情申请单详情
     */
    @Operation(summary = "获取历史合同详情申请单详情")
    @Parameter(name = "id", description = "合同ID", required = true, example = "1")
    @Parameter(name = "agreement", description = "涉及补充协议", required = false, in = ParameterIn.QUERY, example = "false")
    @GetMapping("/apply/{id}")
    public ResultTemplate<ContractCommonEditDetailVO> getHistoricApplyDetail(
            @PathVariable(name = "id") Integer id,
            @RequestParam(name = "force", required = false, defaultValue = "false") boolean force,
            @RequestParam(name = "agreement", required = false, defaultValue = "false") boolean agreement) {
        return ResultTemplate.success(
                BeanUtil.copyProperties(contractReadService.getHistoricApplyDetail(id, force, agreement), ContractCommonEditDetailVO.class));
    }

    /**
     * 历史合同导出
     */
    @Operation(summary = "历史合同导出")
    @PostMapping("/export")
    public ResultTemplate<Long> exportHistoryContracts(@RequestBody ContractQueryParam queryParam) {
        queryParam.setContractTypes(CONTRACT_TYPES);
        TaskDTO taskDTO = new TaskDTO().setExecuteParams(JSON.toJSONString(queryParam));
        return ResultTemplate.success(this.validContractHistoryService.downloader(taskDTO));
    }


    /**
     * 历史合同作废
     */
    @Operation(summary = "历史合同作废")
    @Parameter(name = "id", description = "合同ID", required = true, example = "1")
    @PutMapping("/{id}/cancel")
    public ResultTemplate<Boolean> cancelHistoryContract(@PathVariable(name = "id") Integer id,
                                                         @Validated @RequestBody ContractCancelParam archiveParam) {
        return ResultTemplate.success(contractWriteService.cancelContract(id, archiveParam));
    }

    /**
     * 历史合同转交
     */
    @Operation(summary = "历史合同转交")
    @PostMapping("/{id}/transfer")
    public ResultTemplate<Boolean> transferHistoryContract(@PathVariable(name = "id") Integer id,
                                                           @Validated @RequestBody ContractTransferParam transferParam) {
        BatchContractTransferParam param = new BatchContractTransferParam();
        param.setContractIds(List.of(id));
        param.setFollower(transferParam.getFollower());
        param.setFollowerName(transferParam.getFollowerName());
        return ResultTemplate.success(contractWriteService.batchTransferContract(param));
    }

    /**
     * 历史合同批量转交
     */
    @Operation(summary = "历史合同批量转交")
    @PostMapping("/transfer/batch")
    public ResultTemplate<Boolean> batchTransferHistoryContract(@Validated @RequestBody BatchContractTransferParam transferParam) {
        return ResultTemplate.success(contractWriteService.batchTransferContract(transferParam));
    }

    @Operation(summary = "编辑历史合同")
    @Parameter(name = "Token", description = "Token", in = ParameterIn.HEADER, required = true)
    @PutMapping
    public ResultTemplate<Integer> edit(@RequestBody ContractEditParam contractEditParam) {
        // 特殊处理合同ID
        if (Objects.isNull(contractEditParam.getId()) || contractEditParam.getId() <= 0) {
            log.info("编辑历史合同 id不正确---{}", contractEditParam);
            return ResultTemplate.fail("id不正确");
        }

//        String validateResult = paramCheck(contractEditParam);
//        if (StringUtils.isNotBlank(validateResult)) {
//            log.info("编辑历史合同 参数校验不通过---{},{}", contractEditParam, validateResult);
//            return ResultTemplate.fail(validateResult);
//        }
        // 供应商有效性验证
        String supplierCheckRet = contractValidateHelper.doValidateForSupplier(contractEditParam);
        if (StringUtils.isNotBlank(supplierCheckRet)) {
            return ResultTemplate.fail(supplierCheckRet);
        }

        String redisKey = String.format("contract:agreement:%s", contractEditParam.getId());
        log.info("编辑历史合同 redisKey---{}", redisKey);
        try {
            Boolean locked = stringRedisTemplate.opsForValue().setIfAbsent(redisKey, "1", 2, TimeUnit.SECONDS);
            if (BooleanUtil.isTrue(locked)) {
                log.info("编辑历史合同 redisKey 加锁成功---{},有效期2s", redisKey);
                if (contractReadService.canCreate(contractEditParam.getId(), null)) {
                    Integer id = contractWriteService.updateContractWithLog(contractEditParam);
                    return ResultTemplate.success(id);
                } else {
                    log.info("编辑历史合同 存在在途补充协议---{}", contractEditParam.getId());
                    return ResultTemplate.fail("存在在途补充协议");
                }
            } else {
                log.info("编辑历史合同 redisKey {} 加锁失败,重复提交", redisKey);
                return ResultTemplate.fail("请勿重复提交");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            stringRedisTemplate.delete(redisKey);
            return ResultTemplate.fail("操作失败");
        }
    }


    private String paramCheck(ContractEditParam contractEditParam) {
        StringJoiner errorMessage = new StringJoiner(", ");
        Set<ConstraintViolation<ContractEditParam>> violations = validator.validate(contractEditParam, ValidationGroup.ContractAgreement.class);
        if (!violations.isEmpty()) {
            for (ConstraintViolation<ContractEditParam> violation : violations) {
                errorMessage.add(violation.getMessage());
            }
        }
        // 如果有错误信息，则返回错误信息
        if (errorMessage.length() > 0) {
            return errorMessage.toString();
        }
        return null;
    }

    @Operation(summary = "历史合同导出-项目+付款周期维度")
    @PostMapping("/project-pay/export")
    public ResultTemplate<Long> exportHistoryContractsWithPayPeriod(@RequestBody ContractQueryParam queryParam) {
        queryParam.setContractTypes(CONTRACT_TYPES);
        TaskDTO taskDTO = new TaskDTO().setExecuteParams(JSON.toJSONString(queryParam));
        return ResultTemplate.success(this.validContractHistoryProjectPayService.downloader(taskDTO));
    }

    @Operation(summary = "模糊查询合同编码")
    @PostMapping("/query-code")
    public ResultTemplate<PageResponseVo<CodeNameVO>> queryContractCode(@RequestBody PageRequestVo<String> queryParam) {
        if (StringUtils.isBlank(queryParam.getQuery())) {
            return ResultTemplate.success();
        }
        return ResultTemplate.success(contractReadService.queryContractCode(queryParam));
    }

    @Operation(summary = "模糊查询项目名称")
    @PostMapping("/query-project")
    public ResultTemplate<PageResponseVo<CodeNameVO>> queryProjectName(@RequestBody PageRequestVo<String> queryParam) {
        if (StringUtils.isBlank(queryParam.getQuery())) {
            return ResultTemplate.success();
        }
        return ResultTemplate.success(contractReadService.queryProjectName(queryParam.getQuery(), CONTRACT_TYPES, queryParam.getPageSize()));
    }


    @Operation(summary = "历史有效合同ssp点位")
    @GetMapping("/ssp-point/{businessCode}")
    public ResultTemplate<List<PointDetail>> sspPointByBusinessCode(@PathVariable String businessCode) {
        if (StringUtils.isBlank(businessCode)) {
            return ResultTemplate.success();
        }
        return ResultTemplate.success(feignMethH5Rpc.sspPointByBusinessCode(businessCode).getData());
    }
}
