package com.coocaa.ad.cheese.cms.venue.service;

import com.alibaba.fastjson2.JSON;
import com.coocaa.ad.cheese.cms.common.db.venue.entity.CityEntity;
import com.coocaa.ad.cheese.cms.common.db.venue.service.ICityService;
import com.coocaa.ad.cheese.cms.common.exception.BusinessException;
import com.coocaa.ad.cheese.cms.common.rpc.FeignAuthorityRpc;
import com.coocaa.ad.cheese.cms.common.tools.common.bean.common.CodeNameVO;
import com.coocaa.ad.cheese.cms.common.tools.common.enums.BooleFlagEnum;
import com.coocaa.ad.cheese.cms.common.tools.common.result.ResultTemplate;
import com.coocaa.ad.cheese.cms.common.tools.common.util.StringUtils;
import com.coocaa.ad.cheese.cms.common.tools.venue.bean.UserDataAccessV2DTO;
import com.coocaa.ad.cheese.cms.venue.bean.contract.CityEditParam;
import com.coocaa.ad.cheese.cms.venue.bean.contract.CityQueryParam;
import com.coocaa.ad.cheese.cms.venue.convert.contract.CityConvert;
import com.coocaa.ad.cheese.cms.venue.vo.contract.CityListVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.CitySimpleListVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.CityVO;
import com.coocaa.ad.cheese.cms.venue.vo.contract.RegionCityVO;
import com.coocaa.ad.translate.anno.AutoTranslate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/3/24
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class CityService {
    private final FeignAuthorityRpc feignAuthorityRpc;
    private final CityConvert cityConvert;
    private final ICityService cityService;
    private final DataAccessService dataAccessService;

    /**
     * 从权限系统获取城市 用于初始化城市配置
     */
    public void syncFromAuth() {
        Map<Integer, CityEntity> cityEntityMap = cityService.lambdaQuery().list().stream()
                .collect(Collectors.toMap(CityEntity::getId, cityEntity -> cityEntity));

        ResultTemplate<List<CodeNameVO>> resultTemplate = feignAuthorityRpc.getAvailableCities();
        if (!resultTemplate.getSuccess()) {
            log.error("获取权限系统城市异常:{}", JSON.toJSONString(resultTemplate));
        }
        log.info("获取权限系统城市:{}", JSON.toJSONString(resultTemplate));
        List<CodeNameVO> codeNames = resultTemplate.getData();
        List<CityEntity> cityEntities = codeNames.stream().map(
                codeNameVO -> {
                    if (cityEntityMap.containsKey(codeNameVO.getId())) {
                        return cityConvert.toEntity(codeNameVO, cityEntityMap.get(codeNameVO.getId()));
                    } else {
                        return cityConvert.toEntity(codeNameVO);
                    }
                }
        ).toList();
        cityService.saveOrUpdateBatch(cityEntities);
    }

    /**
     * 更新城市
     */
    public void updateCity(CityEditParam cityEditParam) {
        CityEntity cityEntity = Optional.ofNullable(cityService.getById(cityEditParam.getId()))
                .orElseThrow(() -> new BusinessException("根据城市id未找到对应的城市"));
        cityEntity = cityConvert.toEntity(cityEditParam, cityEntity);
        cityService.updateById(cityEntity);
    }

    /**
     * 城市列表-详细版
     */
    @AutoTranslate
    public List<CityListVO> listCities(CityQueryParam cityQueryParam) {
        List<CityEntity> cities = cityService.lambdaQuery()
                .eq(StringUtils.isNotBlank(cityQueryParam.getName()), CityEntity::getName, cityQueryParam.getName())
                .orderByAsc(CityEntity::getPriority)
                .list();
        return cities.stream().map(cityConvert::toCityListVO).toList();

    }

    /**
     * 获取城市名称列表
     */
    public List<String> listCityNames() {
        return cityService.lambdaQuery().list()
                .stream().map(CityEntity::getName)
                .toList();
    }

    /**
     * 更新城市所属大区
     */
    public void updateCityRegion(List<Integer> cityIds, Integer regionId) {
        if (Objects.isNull(regionId)) {
            return;
        }
        List<CityEntity> cityEntitiesBefore = cityService.lambdaQuery()
                .eq(CityEntity::getRegionId, regionId)
                .list();
        List<CityEntity> cityEntitiesAfter = Collections.emptyList();
        if (CollectionUtils.isNotEmpty(cityIds)) {
            cityEntitiesAfter = cityService.lambdaQuery()
                    .in(CityEntity::getId, cityIds)
                    .list();
        }

        List<CityEntity> cityEntityListToRemove = new ArrayList<>(cityEntitiesBefore);
        cityEntityListToRemove.removeAll(cityEntitiesAfter);

        List<CityEntity> cityEntityListToAdd = new ArrayList<>(cityEntitiesAfter);
        cityEntityListToAdd.removeAll(cityEntitiesBefore);

        cityEntityListToRemove = cityEntityListToRemove.stream()
                .peek(cityEntity -> cityEntity.setRegionId(0))
                .toList();

        cityEntityListToAdd = cityEntityListToAdd.stream()
                .peek(cityEntity -> cityEntity.setRegionId(regionId))
                .toList();

        List<CityEntity> cityEntityList = new ArrayList<>();
        cityEntityList.addAll(cityEntityListToRemove);
        cityEntityList.addAll(cityEntityListToAdd);
        cityService.updateBatchById(cityEntityList);
    }

    /**
     * 根据大区分组城市名称
     */
    public Map<Integer, List<String>> listCityNamesGroupByRegion() {
        List<CityEntity> cityEntities = cityService.lambdaQuery()
                .select(CityEntity::getRegionId, CityEntity::getName)
                .eq(CityEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .list();
        return cityEntities.stream().collect(Collectors.groupingBy(CityEntity::getRegionId,
                Collectors.mapping(CityEntity::getName, Collectors.toList())));
    }

    /**
     * 城市详情
     */
    @AutoTranslate
    public CityVO getCity(Integer id) {
        CityEntity cityEntity = cityService.getById(id);
        return cityConvert.toCityVO(cityEntity);
    }

    /**
     * 获取大区对应的城市
     */
    public List<RegionCityVO> getRegionCities(Integer regionId) {
        List<CityEntity> cityEntities = cityService.lambdaQuery().select(CityEntity::getId, CityEntity::getName)
                .eq(CityEntity::getRegionId, regionId)
                .eq(CityEntity::getDeleteFlag, BooleFlagEnum.NO)
                .list();
        return cityEntities.stream().map(cityConvert::toRegionCityVO).toList();
    }

    /**
     * 获取大区的城市-简略版
     */
    public List<CitySimpleListVO> listCitiesByRegions(List<Integer> regionIds) {
        List<CityEntity> cityEntities = cityService.lambdaQuery().select(CityEntity::getId, CityEntity::getName)
                .eq(CityEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .in(CollectionUtils.isNotEmpty(regionIds), CityEntity::getRegionId, regionIds)
                .list();
        if (CollectionUtils.isEmpty(cityEntities)) {
            return Collections.emptyList();
        }
        // 本地根据大区查询到的
        List<CitySimpleListVO> citySimpleList = cityEntities.stream().map(cityConvert::toCitySimpleListVO).toList();

        ResultTemplate<List<CodeNameVO>> resultTemplate = feignAuthorityRpc.getAvailableCities();
        if (!resultTemplate.getSuccess()) {
            throw new RuntimeException("获取权限系统城市异常");
        }
        List<CodeNameVO> codeNames = resultTemplate.getData();
        if (CollectionUtils.isEmpty(codeNames)) {
            return Collections.emptyList();
        }
        List<Integer> ids = codeNames.stream().map(CodeNameVO::getId).toList();

        citySimpleList = citySimpleList.stream()
                .filter(citySimpleListVO -> ids.contains(citySimpleListVO.getId()))
                .toList();

        //合同数据权限配置的城市
        UserDataAccessV2DTO dataAccessV2DTO = dataAccessService.getUserDataAccessV2();
        List<Integer> userDataAccessCities = dataAccessV2DTO.getCityIds();
        if (!userDataAccessCities.contains(0)) {
            citySimpleList = citySimpleList.stream()
                    .filter(citySimpleListVO -> userDataAccessCities.contains(citySimpleListVO.getId()))
                    .toList();
        }
        return citySimpleList;
    }

    public Set<Integer> getRegionIdsByCityIds(List<Integer> cityIds) {
        return cityService.lambdaQuery().eq(CityEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .in(CityEntity::getId, cityIds)
                .list().stream().map(CityEntity::getRegionId)
                .collect(Collectors.toSet());
    }

    @AutoTranslate
    public List<CityVO> getBusinessHead(Set<String> cityNames) {
        List<CityEntity> cityEntities = cityService.lambdaQuery()
                .eq(CityEntity::getDeleteFlag, BooleFlagEnum.NO.getCode())
                .in(CityEntity::getName, cityNames)
                .list();

        return cityEntities.stream().map(cityConvert::toCityVO).toList();
    }

}
