# APP单页引导功能设计文档

## 1. 功能概述

简洁的后端系统，支持APP在特定页面上线新功能时向用户展示一次性引导提示。系统通过记录用户已读状态，确保每个功能引导只向用户展示一次。

## 2. 数据库设计

### 2.1 用户已读记录表(user_guide_read)

```sql
CREATE TABLE `user_guide_read` (
  `id` bigint(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_code` varchar(10) NOT NULL DEFAULT '' COMMENT '用户编码',
  `feature_code` varchar(10) NOT NULL DEFAULT '' COMMENT '页面功能编码',
  `read_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '阅读时间',
  `create_by` VARCHAR(10) NOT NULL DEFAULT '' COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` VARCHAR(10) NOT NULL DEFAULT '' COMMENT '修改人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `delete_flag` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '是否删除: 0否,1是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_guide` (`user_code`,`feature_code`)
) ENGINE=InnoDB COMMENT='用户已读引导记录';
```

## 3. 接口设计

### 3.1 检查功能引导是否已读

#### 请求

```
GET /api/guides/check
```

#### 参数


| 参数名      | 类型   | 必填 | 描述         |
| ----------- | ------ | ---- | ------------ |
| featureCode | String | 是   | 页面功能编码 |
| userCode    | String | 是   | 用户编码     |

#### 响应

```json
{
  "code": 200,
  "data": {
    "hasRead": false
  }
}
```

### 3.2 标记已读

#### 请求

```
POST /api/guides/read
```

#### 参数

```json
{
  "userCode": "U1001",
  "featureCode": "FILTER01",
  "createBy": "system"
}
```

#### 响应

```json
{
  "code": 200,
  "data": true
}
```

## 4. 核心逻辑

### 4.1 引导展示逻辑

1. 用户进入页面，客户端请求后端检查功能引导是否已读
2. 后端查询用户是否已读过该功能引导
3. 返回查询结果给客户端
4. 客户端根据结果决定是否展示引导提示（未读则展示）

### 4.2 标记已读逻辑

1. 用户查看引导后，客户端调用标记已读接口
2. 后端记录用户已读状态
3. 已读的引导不再向该用户展示

## 5. 实现示例

```java
@Service
public class UserGuideServiceImpl implements UserGuideService {
  
    @Resource
    private UserGuideReadMapper readMapper;
  
    @Override
    public boolean checkUserRead(String featureCode, String userCode) {
        return readMapper.checkUserRead(userCode, featureCode);
    }
  
    @Override
    public boolean markRead(String userCode, String featureCode, String operator) {
        UserGuideRead record = new UserGuideRead();
        record.setUserCode(userCode);
        record.setFeatureCode(featureCode);
        record.setReadTime(new Date());
        record.setCreateBy(operator);
        record.setUpdateBy(operator);
        return readMapper.insert(record) > 0;
    }
}
```

## 6. 客户端实现

客户端负责：

1. 定义各功能的引导内容、图片和位置等
2. 调用后端接口检查是否需要展示引导
3. 展示引导UI
4. 用户查看后调用标记已读接口
