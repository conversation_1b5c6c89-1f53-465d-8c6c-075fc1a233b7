-- 创建用户已读引导记录表
CREATE TABLE IF NOT EXISTS `user_guide_read` (
  `id` bigint(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_code` varchar(10) NOT NULL DEFAULT '' COMMENT '用户编码',
  `feature_code` varchar(10) NOT NULL DEFAULT '' COMMENT '页面功能编码',
  `read_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '阅读时间',
  `create_by` VARCHAR(10) NOT NULL DEFAULT '' COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` VARCHAR(10) NOT NULL DEFAULT '' COMMENT '修改人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `delete_flag` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '是否删除: 0否,1是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_guide` (`user_code`,`feature_code`)
) ENGINE=InnoDB COMMENT='用户已读引导记录'; 