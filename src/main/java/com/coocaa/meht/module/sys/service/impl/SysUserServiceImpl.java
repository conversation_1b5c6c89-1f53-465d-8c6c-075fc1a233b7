package com.coocaa.meht.module.sys.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.Maps;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.common.*;
import com.coocaa.meht.common.bean.ResultTemplate;
import com.coocaa.meht.common.bean.TokenResultVO;
import com.coocaa.meht.common.exception.CommonException;
import com.coocaa.meht.common.exception.ErrorCode;
import com.coocaa.meht.common.exception.ServerException;
import com.coocaa.meht.module.api.beisen.BeisenApiService;
import com.coocaa.meht.module.api.fs.FsApiService;
import com.coocaa.meht.module.api.ldap.LdapApiService;
import com.coocaa.meht.module.api.ldap.LdapUserDto;
import com.coocaa.meht.module.sys.dao.SysUserDao;
import com.coocaa.meht.module.sys.dto.*;
import com.coocaa.meht.module.sys.entity.StsTokenEntity;
import com.coocaa.meht.module.sys.entity.SysUserEntity;
import com.coocaa.meht.module.sys.enums.SmsType;
import com.coocaa.meht.module.sys.service.StsTokenService;
import com.coocaa.meht.module.sys.service.SysUserService;
import com.coocaa.meht.module.web.entity.AgentPersonnelEntity;
import com.coocaa.meht.module.web.service.AgentPersonnelService;
import com.coocaa.meht.module.web.vo.common.UserVO;
import com.coocaa.meht.rpc.FeignAuthorityRpc;
import com.coocaa.meht.utils.*;
import feign.FeignException;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 用户管理
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysUserServiceImpl extends ServiceImpl<SysUserDao, SysUserEntity> implements SysUserService {

    private static final String captchaKey = "meht:captcha:";
    private final FsApiService fsApiService;
    private final BeisenApiService beisenApiService;
    private final LdapApiService ldapApiService;
    private final RedisUtils redisUtils;
    private final StsTokenService stsTokenService;
    private final AgentPersonnelService agentPersonnelService;
    @Autowired(required = false)
    private  RsaExample rsaExample;
    private final FeignAuthorityRpc feignAuthorityRpc;

    @Value("${user.login.exp:86400}")
    private Long userLoginKey;

    @Value("${sms.code.show:false}")
    private boolean showCode;

    @Value("${login.sms.template:2435099}")
    private String smsTemplate;

    @Value("${login.sms.type:2}")
    private Integer smsType;
    /**
     * 发送验证码
     *
     * @param phone
     * @param type  登录:login
     * @return
     */
    @Override
    public UserAuthVO captcha( String phone, String type) {
        try {
            ResultTemplate<UserAuthVO> userByAuth=feignAuthorityRpc.getUserByAuth(phone);
            if(ObjectUtil.isNull(userByAuth.getData()) || !userByAuth.getSuccess()){
                throw new ServerException(userByAuth.getMsg());
            }

            String randomNumbers = new Random().ints(4, 0, 10)
                    .mapToObj(String::valueOf)
                    .collect(Collectors.joining());
            String content = String.format("您好, 本次登陆验证码为:%s, 请在5分钟内完成验证。", randomNumbers);
            log.info("【媒资工作台登录发送短信】手机:{}, 验证码:{}", phone, randomNumbers);
            String lckKey = captchaKey + type + ":" + phone;
            redisUtils.set(lckKey, randomNumbers, 5 * 60);
            if(showCode){
                // 发送短信
                if (Objects.equals(smsType, 2)) {
                    SmsSendParam param = new SmsSendParam();
                    param.setPhoneNumbers(Collections.singletonList(phone).toArray(new String[0]));
                    param.setSmsType(SmsType.VERIFICATION_CODE.name());
                    param.setSignName("广东创视科技");
                    param.setTemplateId(smsTemplate);
                    param.setTemplateParams(Collections.singletonList(randomNumbers).toArray(new String[0]));
                    param.setCode(randomNumbers);
                    feignAuthorityRpc.sendSms(param);
                } else {
                    SmsUtils.send(phone, content);
                }

            }else {
                userByAuth.getData().setMessage(content).setCode(randomNumbers);
            }

            return userByAuth.getData();
        }catch (FeignException exception){
            log.info("用户获取验证码异常：{}",exception.getMessage());
            throw new ServerException(exception.getMessage());
        }
    }

    /**
     * 域登录
     *
     * @param dto
     * @return
     */
    @Override
    public SysLoginTokenDto loginByEmail(SysEmailLoginDto dto) {
        LdapUserDto ldapUser = ldapApiService.getUser(dto.getEmail(), dto.getPassword());
        if (ldapUser == null) {
            throw new ServerException("邮箱或密码错误");
        }
        SysUserEntity user = this.baseMapper.getByEmail(ldapUser.getEmail());
        if (user == null) {
            throw new ServerException("该用户尚未同步");
        }
        return login(LoginUser.build(user), null, dto.getPlatform());
    }

    /**
     * 飞书登录
     *
     * @param dto
     * @return
     */
    @Override
    public LoginResultVO loginByFsCode(SysFsLoginDto dto) {
        String key = "meht:fs:code:" + dto.getCode();
        if (StringUtils.isNotBlank(redisUtils.get(key))) {
            throw new ServerException("code已使用或过期，请重新进行登录授权");
        }
        redisUtils.set(key, "1", 5 * 60);
        Map<String, String> userToken = fsApiService.getUserToken(dto.getCode());
        log.info("飞书登录授权信息：{}",JsonUtils.toJson(userToken));
        if (CollectionUtils.isEmpty(userToken)) {
            throw new ServerException("飞书登录授权已过期，请重新登录");
        }
        Map<String, Object> fsUserInfo = fsApiService.getUserInfo(userToken.get("accessToken"));
        log.info("飞书登录用户信息：{}",JsonUtils.toJson(fsUserInfo));
        if (CollectionUtils.isEmpty(fsUserInfo)) {
            throw new ServerException("飞书登录登录异常，请联系管理员");
        }
        ResultTemplate<UserAuthVO> userByAuth=feignAuthorityRpc.getUserByAuth(fsUserInfo.get("mobile").toString().replace("+86", ""));
        log.info("飞书登录用户信息userByAuth：{}",JsonUtils.toJson(userByAuth));
        if(ObjectUtil.isNull(userByAuth.getData())){
            throw new CommonException(userByAuth.getMsg());
        }
        ResultTemplate<LoginResultVO> loginResultVO=feignAuthorityRpc.generateToken(userByAuth.getData().getHash());
        log.info("飞书登录用户信息loginResultVO：{}",JsonUtils.toJson(loginResultVO));
        ResultTemplate<UserVO> userVO= feignAuthorityRpc.userInfoByIdentity(fsUserInfo.get("mobile").toString().replace("+86", ""));
        log.info("飞书登录用户信息feignAuthorityRpc：{}",JsonUtils.toJson(userVO));
        if(ObjectUtil.isNull(userByAuth.getData()) ||  ObjectUtil.isNull(userVO.getData()) ){
            throw new CommonException("查询用户信息失败");
        }
        buildLogin(loginResultVO.getData().getToken(),"phone",userVO.getData(), fsUserInfo);
        return loginResultVO.getData();
    }

    /**
     * 手机验证码登录
     *
     * @param dto
     * @return
     */
    @Override
    public LoginResultVO loginByPhone(SysPhoneLoginDto dto) {
        String lckKey = captchaKey + "login:" + dto.getPhone();
        String captcha = redisUtils.get(lckKey);

        if (!Objects.equals(captcha, dto.getCaptcha())) {
            throw new ServerException("验证码错误");
        }
        ResultTemplate<LoginResultVO> userByAuth=feignAuthorityRpc.generateToken(dto.getHash());
        ResultTemplate<UserVO> userVO= feignAuthorityRpc.userInfoByIdentity(dto.getPhone());
        if(ObjectUtil.isNull(userByAuth.getData()) ||  ObjectUtil.isNull(userVO.getData()) ){
            throw new CommonException("查询用户信息失败");
        }
        redisUtils.delete(lckKey);
        buildLogin(userByAuth.getData().getToken(),"phone",userVO.getData(),null);
        return userByAuth.getData();
    }

    @Override
    public TokenResultVO getTokenByRefreshToken(String refreshToken) {
        if(StringUtils.isEmpty(refreshToken)){
            throw new ServerException("refreshToken不能为空");
        }
        ResultTemplate<TokenResultVO> tokenResultVO=feignAuthorityRpc.getTokenByRefreshToken(refreshToken);

        if(StringUtils.isNotEmpty(tokenResultVO.getErrCode()) && String.valueOf(ErrorCode.UNAUTHORIZED.getCode()).equals(tokenResultVO.getErrCode())){
            throw new ServerException(tokenResultVO.getMsg());
        }
        DecodedJWT decodedJWT = JWTUtil.parseToken(tokenResultVO.getData().getToken());
        String usedCode = decodedJWT.getClaim("userCode").asString();

        ResultTemplate<UserVO> userVO= feignAuthorityRpc.userInfoByIdentity(usedCode);
        if( ObjectUtil.isNotNull(userVO.getData()) ){
            buildLogin(tokenResultVO.getData().getToken(),"phone",userVO.getData(),null);
        }

        return tokenResultVO.getData();
    }

    private SysLoginTokenDto buildLogin(String token,String type,UserVO user,Map<String, Object> fsUserInfo){
        LoginUser loginUser=new LoginUser()
                .setId(user.getId().longValue())
                .setPhone(user.getMobile())
                .setUserName(user.getName())
                .setEmail(user.getEmail())
                .setUserCode(user.getWno())
                .setUserType(user.getType());
        if(ObjectUtil.isNotNull(user.getType()) && user.getType()==2){
            AgentPersonnelEntity agentPersonnel=  agentPersonnelService.lambdaQuery().eq(AgentPersonnelEntity::getEmpCode,user.getWno()).one();
            if(ObjectUtil.isNotNull(agentPersonnel)){
                loginUser.setId(agentPersonnel.getId());
            }
        }else{
            SysUserEntity userEntity= this.lambdaQuery().eq(SysUserEntity::getEmpCode,user.getWno()).one();
            if(ObjectUtil.isNotNull(userEntity)){
                loginUser.setId(userEntity.getId());
                if (!CollectionUtils.isEmpty(fsUserInfo)) {
                    loginUser.setAvatar(Converts.toStr(fsUserInfo.get("avatarMiddle")));
                    if(ObjectUtil.isNotNull(userEntity)){
                        this.updateById(new SysUserEntity().setId(userEntity.getId())
                                .setFsUserId(Converts.toStr(fsUserInfo.get("userId")))
                                .setFsOpenId(Converts.toStr(fsUserInfo.get("openId")))
                                .setFsUnionId(Converts.toStr(fsUserInfo.get("unionId")))
                                .setAvatar(Converts.toStr(fsUserInfo.get("avatarMiddle"))));
                    }


                }
            }
        }
        log.info("loginUser:{},fsUserInfo:{}",JsonUtils.toJson(loginUser),JsonUtils.toJson(fsUserInfo));
        redisUtils.set(SecurityUser.userLoginKey(token),loginUser, userLoginKey);
        SecurityUser.login(loginUser);
        return new SysLoginTokenDto(token);
}

    private SysLoginTokenDto login(LoginUser loginUser, Map<String, Object> fsUserInfo, String platform) {
        String token = UUID.randomUUID().toString().replace("-", "");
        loginUser.setToken(token);
        if (!CollectionUtils.isEmpty(fsUserInfo)) {
            loginUser.setAvatar(Converts.toStr(fsUserInfo.get("avatarMiddle")));
            this.updateById(new SysUserEntity().setId(loginUser.getId())
                    .setFsUserId(Converts.toStr(fsUserInfo.get("userId")))
                    .setFsOpenId(Converts.toStr(fsUserInfo.get("openId")))
                    .setFsUnionId(Converts.toStr(fsUserInfo.get("unionId")))
                    .setAvatar(Converts.toStr(fsUserInfo.get("avatarMiddle"))));
        }
        List<StsTokenEntity> userTokenList = stsTokenService.getListByUser(loginUser.getUserCode(), platform);
        if (!CollectionUtils.isEmpty(userTokenList)) {
            userTokenList.forEach(ele -> redisUtils.delete(SecurityUser.userLoginKey(ele.getToken())));
            stsTokenService.delete(loginUser.getUserCode(), platform);
        }
        stsTokenService.save(new StsTokenEntity().setUserCode(loginUser.getUserCode())
                .setPlatform(platform).setToken(token));
        redisUtils.set(SecurityUser.userLoginKey(token), loginUser, RedisUtils.DAY_ONE_EXPIRE);
        SecurityUser.login(loginUser);
        return new SysLoginTokenDto(token);
    }

    /**
     * 退出
     *
     * @param accessToken
     */
    @Override
    public void logout(String accessToken) {
        redisUtils.delete(SecurityUser.userLoginKey(accessToken));
    }

    @Override
    public SysUserEntity getByCode(String code) {
        return this.baseMapper.getByCode(code);
    }

    @Override
    public String getName(String userCode) {
        return baseMapper.getName(userCode);
    }

    /**
     * 获取飞书ID
     *
     * @param userCode
     * @return
     */
    @Override
    public String getFsUserId(String userCode) {
        return baseMapper.getFsUserId(userCode);
    }

    @Override
    public String getFsOpenId(String userCode) {
        return baseMapper.getFsUserId(userCode);
    }

    @Override
    public Map<String, SysUserDto> getNameMaps(List<String> userCodes) {
        Map<String, SysUserDto> map = new HashMap<>();
        List<SysUserDto> list = baseMapper.getNameList(userCodes);
        list.forEach(ele -> map.put(ele.getEmpCode(), ele));
        return map;
    }

    @Override
    public Map<String, SysUserDto> getUserNameMaps(List<String> userCodes) {
        List<SysUserDto> list = baseMapper.getNameList(userCodes);
        Map<String, SysUserDto> collect = list.stream().collect(Collectors.toMap(SysUserDto::getEmpCode, v -> v, (v1, v2) -> v1));
        return collect;
    }

    @Override
    public void syncUser() {
        List<Object> errorUser = new ArrayList<>();
        List<SysUserEntity> addUser = new ArrayList<>();
        List<SysUserEntity> updateUser = new ArrayList<>();
        List<Map<String, Object>> employList = beisenApiService.getEmployList(null);
        log.info("北森同步用户开始: {}", employList.size());
        if (!CollectionUtils.isEmpty(employList)) {
            List<SysUserEntity> userAll = this.list();
            Map<String, SysUserEntity> userMapAll = userAll.stream()
                    .filter(u -> StringUtils.isNotBlank(u.getEmpCode()))
                    .collect(Collectors.toMap(SysUserEntity::getEmpCode, v -> v, (v1, v2) -> v1));
            for (Map<String, Object> map : employList) {
                SysUserEntity sysUser = new SysUserEntity();
                sysUser.setAvatar("https://res-ttc.coocaa.com/ttc/master/4b6b8edcd86f48ce976c174ebdbd39f4.png");
                //获取北森ID和工号
                Map<?, ?> recordInfo = (Map<?, ?>) map.get("recordInfo");
                if (Objects.nonNull(recordInfo)) {
                    sysUser.setBeisenId(Converts.toStr(recordInfo.get("userID")));
                    sysUser.setEmpCode(Converts.toStr(recordInfo.get("jobNumber")));
                    //8等于离职
                    sysUser.setStatus("8".equals(Converts.toStr(recordInfo.get("employeeStatus"))) ? 0 : 1);
                    Map<?, ?> translateProperties = (Map<?, ?>) recordInfo.get("translateProperties");
                    if (!CollectionUtils.isEmpty(translateProperties)) {
                        sysUser.setBeisenPostName(Converts.toStr(translateProperties.get("OIdJobPostText")));
                        sysUser.setEmpTypeByTypeText(Converts.toStr(translateProperties.get("EmploymentTypeText")));
                        sysUser.setAssessDept(Converts.toStr(translateProperties.get("extyijizuzhiguanli_606008_681388774Text")));
                    }
                    sysUser.setJoinedDate(DateUtils.toDateTime(Converts.toStr(recordInfo.get("entryDate"))));
                    sysUser.setTerminationDate(DateUtils.toDateTime(Converts.toStr(recordInfo.get("lastWorkDate"))));
                }
                //获取姓名
                Map<?, ?> employeeInfo = (Map<?, ?>) map.get("employeeInfo");
                if (Objects.nonNull(employeeInfo)) {
                    sysUser.setRealName(Converts.toStr(employeeInfo.get("name")));
                    sysUser.setEmail(Converts.toStr(employeeInfo.get("email")));
                    sysUser.setMobile(Converts.toStr(employeeInfo.get("mobilePhone")));
                    sysUser.setGender(Converts.toInt(employeeInfo.get("gender")));
                    sysUser.setEmail(rsaExample.encryptByPublic(sysUser.getEmail()));
                    sysUser.setMobile(rsaExample.encryptByPublic(sysUser.getMobile()));
                }
                SysUserEntity oldUser = userMapAll.get(sysUser.getEmpCode());
                if (Objects.nonNull(oldUser)) {
                    Map<String, Object> oldMap = Converts.beanToMap(oldUser);
                    Map<String, Object> newMap = Converts.beanToMap(sysUser);
                    Map<String, Map<String, Object>> compare = Converts.compare(oldMap, newMap);
                    if (!CollectionUtils.isEmpty(compare.get("new"))) {
                        updateUser.add(sysUser.setId(oldUser.getId()));
                    }
                } else {
                    if (StringUtils.isBlank(sysUser.getEmpCode())) {
                        errorUser.add(map);
                    } else {
                        addUser.add(sysUser);
                    }
                }
            }
        }
        if (!addUser.isEmpty()) {
            this.saveBatch(addUser);
        }
        if (!updateUser.isEmpty()) {
            this.updateBatchById(updateUser);
        }
        /*List<SysUserEntity> notfsIdList = this.lambdaQuery().select(
                        SysUserEntity::getId,
                        SysUserEntity::getEmail,
                        SysUserEntity::getFsUserId)
                .eq(SysUserEntity::getFsUserId, "").list();
        if (!CollectionUtils.isEmpty(notfsIdList)) {
            List<String> mes = new ArrayList<>();
            Map<String, SysUserEntity> userMap = new HashMap<>();
            for (SysUserEntity u : notfsIdList) {
                u.setEmail(rsaExample.decryptByPrivate(u.getEmail()));
                userMap.put(u.getEmail(), u);
                mes.add(u.getEmail());
            }
            Map<String, String> fsUserIds = fsApiService.getUserIds(mes, null);
            fsUserIds.forEach((email, fsUserId) -> {
                SysUserEntity emUser = userMap.get(email);
                if (emUser != null) {
                    try {
                        Map<String, Object> fsUserInfo = fsApiService.getUserInfoByUserId(fsUserId);
                        if (!CollectionUtils.isEmpty(fsUserInfo)) {
                            this.baseMapper.updateById(new SysUserEntity()
                                    .setId(emUser.getId())
                                    .setFsUserId(fsUserId)
                                    .setAvatar(Converts.toStr(fsUserInfo.get("avatar"))));
                        }
                    } catch (Exception e) {
                        log.error("获取飞书用户信息异常:", e);
                    }
                }
            });
        }*/
        log.info("北森同步用户结束，无工号用户: {}", JsonUtils.toJson(errorUser));
    }

    @Override
    public LoginUser getUserByEmpCode(String empCode) {
        if (StringUtils.isBlank(empCode)) {
            return null;
        }

        //员工
        SysUserEntity sysUserEntity = this.getByCode(empCode);
        if (Objects.nonNull(sysUserEntity)) {
            if (StringUtils.isNotEmpty(sysUserEntity.getMobile()) && sysUserEntity.getMobile().length() > 11) {
                sysUserEntity.setMobile(rsaExample.decryptByPrivate(sysUserEntity.getMobile()));
            }
            return LoginUser.build(sysUserEntity);
        }

        //外部代理商
        AgentPersonnelEntity agentPersonnel = agentPersonnelService.getByEmpCode(empCode);
        if (Objects.nonNull(agentPersonnel)) {
            LoginUser loginUser = new LoginUser();
            loginUser.setUserType(1);
            loginUser.setId(agentPersonnel.getId());
            loginUser.setUserCode(agentPersonnel.getEmpCode());
            loginUser.setUserName(agentPersonnel.getEmpName());
            loginUser.setPhone(rsaExample.decryptByPublic(agentPersonnel.getEmpMobile()));
            return loginUser;
        }
        return null;
    }

    @Override
    public LoginUser getUserByPhone(String phone, String wno) {
        if (StringUtils.isAllBlank(phone, wno)) return null;

        SysUserEntity sysUserEntity = lambdaQuery()
                .eq(StringUtils.isNotBlank(wno), SysUserEntity::getEmpCode, wno)
                .or()
                .eq(StringUtils.isNotBlank(phone), SysUserEntity::getMobile, rsaExample.encryptByPrivate(phone))
                .last(" limit 1")
                .one();
        if (Objects.nonNull(sysUserEntity)) {
            return LoginUser.build(sysUserEntity);
        }

        AgentPersonnelEntity agentPersonnel = agentPersonnelService.getOne(Wrappers.<AgentPersonnelEntity>lambdaQuery()
                .eq(StringUtils.isNotBlank(wno), AgentPersonnelEntity::getEmpCode, wno)
                .or()
                .eq(StringUtils.isNotBlank(phone), (AgentPersonnelEntity::getEmpMobile), rsaExample.encryptByPrivate(phone)));

        if (Objects.nonNull(agentPersonnel)) {
            LoginUser loginUser = new LoginUser();
            loginUser.setUserType(1);
            loginUser.setId(agentPersonnel.getId());
            loginUser.setUserCode(agentPersonnel.getEmpCode());
            loginUser.setUserName(agentPersonnel.getEmpName());
            loginUser.setPhone(rsaExample.decryptByPublic(agentPersonnel.getEmpMobile()));
            return loginUser;
        }

        return null;
    }

    @Override
    public Map<String, LoginUser> getUsers(Collection<String> wnos) {
        if (CollUtil.isEmpty(wnos)) return Collections.emptyMap();

        Map<String, LoginUser> userMapping = Maps.newHashMapWithExpectedSize(wnos.size());

        // 查用户表
        userMapping.putAll(this.lambdaQuery()
                .in(SysUserEntity::getEmpCode, wnos)
                .list().stream()
                .map(LoginUser::build)
                .peek(user -> {
                    user.setPhone(rsaExample.decryptByPrivate(user.getPhone()));
                    user.setEmail(rsaExample.decryptByPrivate(user.getEmail()));
                })
                .collect(Collectors.toMap(LoginUser::getUserCode, Function.identity(), (o, n) -> n)));

        // 查代理商表
        userMapping.putAll(agentPersonnelService.getByEmpCodes(wnos.stream().toList()).stream()
                .map(user -> {
                    LoginUser loginUser = new LoginUser();
                    loginUser.setUserType(1);
                    loginUser.setId(user.getId());
                    loginUser.setUserCode(user.getEmpCode());
                    loginUser.setUserName(user.getEmpName());
                    loginUser.setPhone(rsaExample.decryptByPublic(user.getEmpMobile()));
                    return loginUser;
                })
                .collect(Collectors.toMap(LoginUser::getUserCode, Function.identity(), (o, n) -> n)));

        return userMapping;
    }

    @Override
    public void buildUserInfo(String token) {
        System.out.println(UserIdUtils.getUserId());
        ResultTemplate<UserVO> userVOResultTemplate= feignAuthorityRpc.getDetail(UserIdUtils.getUserId(),false);
        if(ObjectUtil.isNull(userVOResultTemplate.getData()) ||  ObjectUtil.isNull(userVOResultTemplate.getData()) ){
            throw new CommonException("查询用户详情失败");
        }
        UserVO UserVO=userVOResultTemplate.getData();
        ResultTemplate<UserVO> userVO= feignAuthorityRpc.userInfoByIdentity(UserVO.getMobile());
        if(ObjectUtil.isNull(userVO.getData()) ||  ObjectUtil.isNull(userVO.getData()) ){
            throw new CommonException("查询用户信息失败");
        }
        buildLogin(token,"phone",userVO.getData(),null);
    }

    @Override
    public void updateFeiShuUser(FeiShuUserInfoDto feiShuUserInfoDto) {
        SysUserEntity userEntity = this.lambdaQuery()
                .eq(SysUserEntity::getEmpCode, feiShuUserInfoDto.getEmpCode())
                .one();

        if (Objects.nonNull(userEntity)) {
            userEntity.setFsUserId(feiShuUserInfoDto.getFsUserId());
            userEntity.setFsOpenId(feiShuUserInfoDto.getFsOpenId());
            userEntity.setFsUnionId(feiShuUserInfoDto.getFsUnionId());
            this.updateById(userEntity);
        }
    }


    @Override
    public List<LoginUser> listUserByEmpCode(List<String> empCodeList) {
        ArrayList<LoginUser> loginUsers = new ArrayList<>();

        LambdaQueryWrapper<SysUserEntity> queryWrapper = new QueryWrapper<SysUserEntity>().lambda().in(SysUserEntity::getEmpCode, empCodeList);
        List<SysUserEntity> list = this.list(queryWrapper);
        if (!CollectionUtils.isEmpty(list)) {
            for (SysUserEntity sysUserEntity : list) {
                LoginUser loginUser = LoginUser.build(sysUserEntity);
                loginUsers.add(loginUser);
            }
        }

        List<AgentPersonnelEntity> personnelEntityList = agentPersonnelService.getByEmpCodes(empCodeList);
        if (!CollectionUtils.isEmpty(personnelEntityList)) {
            for (AgentPersonnelEntity agentPersonnel : personnelEntityList) {
                LoginUser loginUser = new LoginUser();
                loginUser.setUserType(1);
                loginUser.setId(agentPersonnel.getId());
                loginUser.setUserCode(agentPersonnel.getEmpCode());
                loginUser.setUserName(agentPersonnel.getEmpName());
                loginUser.setPhone(rsaExample.decryptByPublic(agentPersonnel.getEmpMobile()));
                loginUsers.add(loginUser);
            }
        }

        return loginUsers;
    }

}
