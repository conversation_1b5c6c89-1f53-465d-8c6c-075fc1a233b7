package com.coocaa.meht.module.crm.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.alibaba.nacos.shaded.com.google.common.collect.Sets;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.meht.common.BaseEntity;
import com.coocaa.meht.common.KeyValue;
import com.coocaa.meht.common.LoginUser;
import com.coocaa.meht.common.PageResult;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.common.SecurityUser;
import com.coocaa.meht.common.bean.CodeNameVO;
import com.coocaa.meht.common.bean.ResultTemplate;
import com.coocaa.meht.module.crm.dto.CmsBusinessDto;
import com.coocaa.meht.module.crm.dto.CrmBusinessDto;
import com.coocaa.meht.module.crm.dto.CrmBusinessFlowDto;
import com.coocaa.meht.module.crm.dto.req.CmsBusinessReq;
import com.coocaa.meht.module.crm.dto.req.CrmBusinessReq;
import com.coocaa.meht.module.crm.enums.DataAccessTypeEnum;
import com.coocaa.meht.module.crm.enums.SceneTypeEnum;
import com.coocaa.meht.module.crm.service.CrmBusinessService;
import com.coocaa.meht.module.crm.vo.BusinessVO;
import com.coocaa.meht.module.sys.service.SysUserService;
import com.coocaa.meht.module.web.dao.BuildingDetailsDao;
import com.coocaa.meht.module.web.dao.BuildingRatingDao;
import com.coocaa.meht.module.web.dao.BusinessOpportunityMapper;
import com.coocaa.meht.module.web.dto.convert.BuildingRatingConvert;
import com.coocaa.meht.module.web.dto.req.BusinessReq;
import com.coocaa.meht.module.web.entity.BuildingDetailsEntity;
import com.coocaa.meht.module.web.entity.BuildingMetaEntity;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.entity.BusinessOpportunityEntity;
import com.coocaa.meht.module.web.entity.PointContractSnapshotEntity;
import com.coocaa.meht.module.web.entity.PointPlanEntity;
import com.coocaa.meht.module.web.entity.PointPriceSnapshotEntity;
import com.coocaa.meht.module.web.enums.BusinessChangeStatusEnum;
import com.coocaa.meht.module.web.service.BuildingRatingService;
import com.coocaa.meht.module.web.service.BusinessOpportunityService;
import com.coocaa.meht.module.web.service.DataHandlerService;
import com.coocaa.meht.module.web.service.IBuildingMetaService;
import com.coocaa.meht.module.web.service.IPointContractSnapshotService;
import com.coocaa.meht.module.web.service.IPointPriceSnapshotService;
import com.coocaa.meht.module.web.service.PointPlanService;
import com.coocaa.meht.rpc.FeignAuthorityRpc;
import com.coocaa.meht.rpc.FeignCmsRpc;
import com.coocaa.meht.rpc.dto.UserDataAccessV2DTO;
import com.coocaa.meht.utils.JsonUtils;
import com.coocaa.meht.utils.RsaExample;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * CRM 商机代理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-25
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class CrmBusinessServiceImpl extends CrmBaseService implements CrmBusinessService {
    private final BuildingRatingDao buildingRatingDao;
    private final BuildingDetailsDao buildingDetailsDao;
    private final SysUserService sysUserService;
    private final PointPlanService pointPlanService;
    private final IPointPriceSnapshotService pointPriceSnapshotService;
    private final IPointContractSnapshotService pointContractSnapshotService;
    private final IBuildingMetaService buildingMetaService;
    private final BuildingRatingService buildingRatingService;
    private final RsaExample rsaExample;
    private final BuildingRatingConvert buildingRatingConvert;
    private final DataHandlerService dataHandlerService;
    private final BusinessOpportunityService businessOpportunityService;
    private final BusinessOpportunityMapper businessOpportunityMapper;
    private final FeignAuthorityRpc feignAuthorityRpc;
    private final FeignCmsRpc feignCmsRpc;

    @Override
    public PageResult<BusinessVO> list(CrmBusinessReq req) {
        Page<BusinessOpportunityEntity> page = new Page<>(req.getPage(), req.getLimit());

        LambdaQueryWrapper<BusinessOpportunityEntity> queryWrapper = new QueryWrapper<BusinessOpportunityEntity>().lambda()
                .like(StringUtils.isNotBlank(req.getSearch()), BusinessOpportunityEntity::getName, req.getSearch())
                .in(CollUtil.isNotEmpty(req.getStatus()) , BusinessOpportunityEntity::getStatus, req.getStatus())
                .orderByDesc(BaseEntity::getCreateTime);

        if (req.getSceneType().equals(SceneTypeEnum.MY_CUSTOMERS.name())) {
            queryWrapper.eq(BusinessOpportunityEntity::getSubmitUser, SecurityUser.getUser().getUserCode());
        } else if (req.getSceneType().equals(SceneTypeEnum.SUBORDINATE_CUSTOMERS.name())) {
            ResultTemplate<UserDataAccessV2DTO> userDataAccessV2 = feignCmsRpc.getUserDataAccessV2();
            log.info("商机权限数据userDataAccessV2:{}", JsonUtils.toJson(userDataAccessV2));
            if (Objects.isNull(userDataAccessV2.getData())) {
                return new PageResult<>(Collections.emptyList(), 0);
            }
            UserDataAccessV2DTO data = userDataAccessV2.getData();
            List<Integer> userIds = data.getUserIds();
            if (data.getAccessType().equals(DataAccessTypeEnum.SELF.getCode()) || data.getAccessType().equals(DataAccessTypeEnum.SELF_AND_SUB.getCode())) {
                ResultTemplate<List<CodeNameVO>> listResultTemplate = feignAuthorityRpc.listUserByIds(userIds);
                List<String> userCodes = listResultTemplate.getData().stream().map(CodeNameVO::getWno).collect(Collectors.toList());
                userCodes.remove(SecurityUser.getUser().getUserCode());
                //差距查询站位，防止报错
                userCodes.add("xxx999");
                queryWrapper.in(BusinessOpportunityEntity::getSubmitUser, userCodes);
            }

        } else if (req.getSceneType().equals(SceneTypeEnum.ALL_CUSTOMERS.name())) {
            List<String> userCodes = new ArrayList<>();
            userCodes.add(SecurityUser.getUser().getUserCode());
            ResultTemplate<UserDataAccessV2DTO> userDataAccessV2 = feignCmsRpc.getUserDataAccessV2();
            log.info("权限数据userDataAccessV2:{}", JsonUtils.toJson(userDataAccessV2));
            if (Objects.isNull(userDataAccessV2.getData())) {
                return new PageResult<>(Collections.emptyList(), 0);
            }
            UserDataAccessV2DTO data = userDataAccessV2.getData();
            if (data.getAccessType().equals(DataAccessTypeEnum.ALL.getCode())) {
                userCodes.clear();
            } else if (data.getAccessType().equals(DataAccessTypeEnum.SELF_AND_SUB.getCode())) {
                List<Integer> userIds = data.getUserIds();
                ResultTemplate<List<CodeNameVO>> listResultTemplate = feignAuthorityRpc.listUserByIds(userIds);
                List<String> collect = listResultTemplate.getData().stream().map(CodeNameVO::getWno).collect(Collectors.toList());
                userCodes.addAll(collect);
            } else if (data.getAccessType().equals(DataAccessTypeEnum.CITY_ALL.getCode())) {
                userCodes.clear();
            }
            queryWrapper.in(CollectionUtil.isNotEmpty(userCodes), BusinessOpportunityEntity::getSubmitUser, userCodes);

        }

        queryWrapper.ne(BusinessOpportunityEntity::getSubmitUser,"");

        Page<BusinessOpportunityEntity> entityPage = businessOpportunityService.page(page, queryWrapper);

        if (CollUtil.isEmpty(entityPage.getRecords())) {
            return new PageResult<>(Collections.emptyList(), 0);
        }
        Set<String> buildingNo = entityPage.getRecords().stream().map(BusinessOpportunityEntity::getBuildingNo).collect(Collectors.toSet());
        Map<String, BuildingRatingEntity> buildingRatingEntityMap = buildingRatingService.list(Wrappers.<BuildingRatingEntity>lambdaQuery()
                        .in(BuildingRatingEntity::getBuildingNo, buildingNo)).stream()
                .collect(Collectors.toMap(BuildingRatingEntity::getBuildingNo, Function.identity(), (o, n) -> n));
        Set<String> userCode = entityPage.getRecords().stream().map(BusinessOpportunityEntity::getSubmitUser).collect(Collectors.toSet());
        Map<String, LoginUser> users = sysUserService.getUsers(userCode);
        List<BusinessVO> businessVOList = new ArrayList<>(entityPage.getRecords().size());
        for (BusinessOpportunityEntity record : entityPage.getRecords()) {
            BusinessVO businessVO = new BusinessVO();
            businessVO.setBusinessCode(record.getCode());
            businessVO.setBusinessName(record.getName());
            businessVO.setBuildingNo(record.getBuildingNo());
            businessVO.setBusinessStatus(record.getStatus());
            businessVO.setStatus(BusinessChangeStatusEnum.getByCode(record.getStatus()));
            businessVO.setCreateBy(record.getSubmitUser());
            businessVO.setOwner(Objects.isNull(users.get(record.getSubmitUser())) ? "" : users.get(record.getSubmitUser()).getUserName());
            Optional.ofNullable(buildingRatingEntityMap.get(record.getBuildingNo()))
                    .ifPresent(building -> {
                        StringJoiner area = new StringJoiner(",");
                        if (StringUtils.isNotBlank(building.getMapProvince())) area.add(building.getMapProvince());
                        if (StringUtils.isNotBlank(building.getMapCity())) area.add(building.getMapCity());
                        if (StringUtils.isNotBlank(building.getMapRegion())) area.add(building.getMapRegion());
                        businessVO.setArea(area.toString());
                        businessVO.setName(building.getBuildingName());
                        businessVO.setLevel(building.getProjectLevel());
                        businessVO.setBuildingNo(building.getBuildingNo());
                        businessVO.setType(BuildingRatingEntity.BuildingType.getNameByValue(building.getBuildingType()));
                    });
            businessVOList.add(businessVO);
        }
        //补充点位方案
        fillPointPlan(businessVOList);
        return new PageResult<>(businessVOList, entityPage.getTotal());
    }


    @Override
    public PageResult<CrmBusinessDto> crmList(CrmBusinessReq req) {
        String url = "/crmBusiness/queryPageList";
        if (Objects.isNull(req.getType())) req.setType(5);

        // 调用CRM
        String crmRsp = callCrmApi(url, req);
        if (StringUtils.isBlank(crmRsp)) {
            return new PageResult<>(Collections.emptyList(), req.getLimit());
        }

        // 解析客户信息
        List<CrmBusinessDto> customers = parseCustomers(crmRsp);
        if (CollUtil.isEmpty(customers)) {
            return new PageResult<>(Collections.emptyList(), req.getLimit());
        }

        return new PageResult<>(customers, req.getLimit());
    }

    private void fillPointPlan(List<BusinessVO> businessVOS) {
        //填充点位方案id
        List<String> businessCodeList = businessVOS.stream().map(BusinessVO::getBusinessCode)
                .collect(Collectors.toList());
        List<PointPlanEntity> pointPlanEntities = pointPlanService.listByBusinessCode(businessCodeList);
        Map<String, PointPlanEntity> pm = pointPlanEntities.stream().collect(Collectors.toMap(PointPlanEntity::getBusinessCode,
                Function.identity(), (e1, e2) -> e1));
        businessVOS.forEach(e -> {
            PointPlanEntity pointPlanEntity = pm.get(e.getBusinessCode());
            if (pointPlanEntity != null) {
                e.setPointPlanId(pointPlanEntity.getId());
                e.setPointPlanStatus(pointPlanEntity.getStatus());
            }
        });
    }

    @Override
    public Result<BusinessVO> getDetail(String businessCode) {
        BusinessVO businessVO = new BusinessVO();
        BusinessOpportunityEntity businessOpportunityEntity = businessOpportunityService.getOne(Wrappers.<BusinessOpportunityEntity>lambdaQuery()
                .eq(BusinessOpportunityEntity::getCode, businessCode));

        if (Objects.isNull(businessOpportunityEntity)){
            return Result.ok(businessVO);
        }

        businessVO.setBusinessName(businessOpportunityEntity.getName());
        businessVO.setBusinessStatus(businessOpportunityEntity.getStatus());
        businessVO.setBuildingNo(businessOpportunityEntity.getBuildingNo());
        businessVO.setStatus(BusinessChangeStatusEnum.getByCode(businessOpportunityEntity.getStatus()));
        businessVO.setBusinessCode(businessOpportunityEntity.getCode());
        businessVO.setCreateBy(businessOpportunityEntity.getSubmitUser());
        Map<String, LoginUser> users = sysUserService.getUsers(Sets.newHashSet(businessOpportunityEntity.getSubmitUser()));
        businessVO.setOwner(Objects.isNull(users.get(businessOpportunityEntity.getSubmitUser())) ? "" : users.get(businessOpportunityEntity.getSubmitUser()).getUserName());

        // 填充楼盘信息
        fillingCustomer(businessVO);
        //填充点位方案
        fillPointPlan(Lists.newArrayList(businessVO));
        //填充加个点位快照
        fillPricePointPlan(Lists.newArrayList(businessVO));
        //填充合同点位快照
        fillContractPointPlan(Lists.newArrayList(businessVO));
        //填充buildingMeta表的信息
        fillByBuildingMeta(Lists.newArrayList(businessVO));
        return Result.ok(businessVO);
    }

    private void fillByBuildingMeta(List<BusinessVO> businessVOS) {
        if (CollectionUtil.isNotEmpty(businessVOS)) {
            Set<String> buildingNos = businessVOS.stream().map(BusinessVO::getBuildingNo)
                    .collect(Collectors.toSet());
            List<BuildingMetaEntity> list = buildingMetaService.listByBuildingNos(buildingNos);
            Map<String, BuildingMetaEntity> map = list.stream().collect(Collectors.toMap(BuildingMetaEntity::getBuildingRatingNo, Function.identity()));
            businessVOS.forEach(e -> {
                BuildingMetaEntity buildingMetaEntity = map.get(e.getBuildingNo());
                if (buildingMetaEntity != null) {
                    e.setTargetPointCount(buildingMetaEntity.getTargetPointCount());
                    e.setCompetitorPointCount(buildingMetaEntity.getCompetitorPointCount());
                }
            });
        }
    }

    private void fillContractPointPlan(List<BusinessVO> businessVOS) {
        if (CollectionUtil.isNotEmpty(businessVOS)) {
            Set<String> set = businessVOS.stream().map(BusinessVO::getBusinessCode)
                    .collect(Collectors.toSet());

            //根据结果businessCode字段去重
            List<PointContractSnapshotEntity> entities = pointContractSnapshotService.lambdaQuery()
                    .select(PointContractSnapshotEntity::getBusinessCode, PointContractSnapshotEntity::getPointPlanId)
                    .in(PointContractSnapshotEntity::getBusinessCode, set).list().stream().distinct().collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(entities)) {
                Map<String, List<PointContractSnapshotEntity>> collect = entities.stream().collect(Collectors.groupingBy(PointContractSnapshotEntity::getBusinessCode));
                businessVOS.forEach(crmBusinessDto -> {
                    List<PointContractSnapshotEntity> pointContractSnapshotEntities = collect.get(crmBusinessDto.getBusinessCode());
                    if (CollectionUtil.isNotEmpty(pointContractSnapshotEntities)) {
                        PointContractSnapshotEntity pointContractSnapshotEntity = pointContractSnapshotEntities.get(0);
                        crmBusinessDto.setContractPointPlanId(pointContractSnapshotEntity.getPointPlanId());
                    }
                });

            }
        }
    }

    private void fillPricePointPlan(List<BusinessVO> businessVOS) {
        if (CollectionUtil.isNotEmpty(businessVOS)) {
            Set<String> set = businessVOS.stream().map(BusinessVO::getBusinessCode)
                    .collect(Collectors.toSet());
            List<PointPriceSnapshotEntity> entities = pointPriceSnapshotService.lambdaQuery()
                    .select(PointPriceSnapshotEntity::getBusinessCode, PointPriceSnapshotEntity::getPointPlanId)
                    .in(PointPriceSnapshotEntity::getBusinessCode, set).list().stream().distinct().collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(entities)) {
                Map<String, List<PointPriceSnapshotEntity>> map = entities.stream().collect(Collectors.groupingBy(PointPriceSnapshotEntity::getBusinessCode));
                businessVOS.forEach(e -> {
                    List<PointPriceSnapshotEntity> price = map.get(e.getBusinessCode());
                    e.setPricePointPlanId(price.get(0).getPointPlanId());
                });

            }
        }
    }

    @Override
    public Result<List<CrmBusinessFlowDto>> getFlows(String businessId) {
        String url = "/crmFlow/queryFlowSettingList/5?typeId=" + businessId;

        // 调用CRM
        String crmRsp = callCrmApi(url);
        if (StringUtils.isBlank(crmRsp)) {
            return Result.ok();
        }

        // 结构: data.settingList[N].id/flowName
        JSON parsed = JSONUtil.parse(crmRsp);

        // 当前流程ID
        Long dataId = parsed.getByPath("data.dataId", Long.class);
        JSONArray jsonArray = (JSONArray) JSONUtil.getByPath(parsed, "data.settingList");
        List<CrmBusinessFlowDto> flows = Lists.newArrayListWithExpectedSize(jsonArray.size());
        for (Object obj : jsonArray) {
            JSONObject jsonObject = (JSONObject) obj;
            flows.add(new CrmBusinessFlowDto()
                    .setFlowId(jsonObject.getStr("id"))
                    .setFlowName(jsonObject.getStr("flowName"))
                    .setCurrent(Objects.equals(dataId, jsonObject.getLong("id"))));
        }
        return Result.ok(flows);
    }

    @Override
    public Result<List<KeyValue<String, String>>> listQueryScenes() {
        return Result.ok(super.listQueryScenes("5", Sets.newHashSet("全部", "我负责", "下属负责")));
    }

    @Override
    public PageResult<CmsBusinessDto> listCmsBusinessByPhone(CmsBusinessReq cmsBusinessReq) {
        log.info("合同系统取商机, 请求参数: {}", cmsBusinessReq);

        // 获取用户信息
        LoginUser loginUser = sysUserService.getUserByPhone(cmsBusinessReq.getPhone(), cmsBusinessReq.getWno());
        if (Objects.isNull(loginUser)) {
            log.info("用户(手机:{}, 工号:{})不存在", cmsBusinessReq.getPhone(), cmsBusinessReq.getWno());
            return new PageResult<>(Collections.emptyList(), 0);
        }
        cmsBusinessReq.setUserCode(loginUser.getUserCode());

        // 查询用户商机
        IPage<BusinessOpportunityEntity> businessPage = businessOpportunityMapper.pageByOwner(cmsBusinessReq);
        if (CollUtil.isEmpty(businessPage.getRecords())) {
            return new PageResult<>(Collections.emptyList(), businessPage.getTotal());
        }

        Set<String> buildingCodes = businessPage.getRecords().stream()
                .map(BusinessOpportunityEntity::getBuildingNo)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        Map<String, BuildingRatingEntity> buildingRatingMap = buildingRatingDao.selectList(
                        Wrappers.<BuildingRatingEntity>lambdaQuery().in(BuildingRatingEntity::getBuildingNo, buildingCodes))
                .stream().collect(Collectors.toMap(BuildingRatingEntity::getBuildingNo, Function.identity()));

        List<CmsBusinessDto> cmsBusinessDtoList = businessPage.getRecords().stream()
                .filter(business -> buildingRatingMap.containsKey(business.getBuildingNo()))
                .map(business -> {
                    BuildingRatingEntity buildingRating = buildingRatingMap.get(business.getBuildingNo());
                    CmsBusinessDto businessDto = buildingRatingConvert.convertToCmsBusinessDto(buildingRating);
                    businessDto.setCustomerId(business.getCustomerId());
                    businessDto.setBuildingNo(business.getBuildingNo());
                    businessDto.setBusinessCode(business.getCode());
                    businessDto.setBusinessName(business.getName());

                    businessDto.setProjectLevel(buildingRating.getProjectLevel());
                    businessDto.setProjectAiLevel(buildingRating.getProjectAiLevel());

                    return businessDto;
                }).toList();

        return new PageResult<>(cmsBusinessDtoList, businessPage.getTotal());
    }

    @Override
    @Transactional
    public Result<Boolean> addBusiness(BusinessReq req) {
        log.info("新增商机, 请求参数: {}", req);
        Boolean result = false;
        try {
            dataHandlerService.addBusiness(req);
            result = true;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return Result.ok(result);
    }

    @Override
    public Result<Boolean> updateBusiness(BusinessReq req) {
        Boolean result = false;
        try {
            dataHandlerService.updateBusiness(req);
            result = true;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return Result.ok(result);
    }

    @Override
    public Result<Boolean> checkBusinessStatus(String buildingNo) {
        Boolean result = true;
        int count = (int) businessOpportunityService.count(Wrappers.<BusinessOpportunityEntity>lambdaQuery()
                .eq(BusinessOpportunityEntity::getBuildingNo, buildingNo)
                .eq(BusinessOpportunityEntity::getStatus, BusinessChangeStatusEnum.TO_BE_DISCUSSED.getCode()));
        if (count > 0) {
            result = false;
        }
        return Result.ok(result);

    }


    /**
     * 填充楼盘信息
     */
    private void fillingCustomer(BusinessVO businessVO) {
        if (Objects.isNull(businessVO) || StringUtils.isBlank(businessVO.getBuildingNo())) return;

        // 根据客户信息查询楼盘信息
        BuildingRatingEntity buildingRating = buildingRatingDao.selectOne(Wrappers.<BuildingRatingEntity>lambdaQuery().eq(BuildingRatingEntity::getBuildingNo, businessVO.getBuildingNo()).last("LIMIT 1"));

        // 同步到CRM的楼盘未更新客户ID
        if (Objects.isNull(buildingRating)) return;

        businessVO.setName(buildingRating.getBuildingName());
        businessVO.setAddress(rsaExample.decryptByPrivate(buildingRating.getMapAddress()));
        businessVO.setType(BuildingRatingEntity.BuildingType.getNameByValue(buildingRating.getBuildingType()));
        businessVO.setLevel(buildingRating.getProjectLevel());

        // 查询楼盘扩展信息
        BuildingDetailsEntity buildingDetail = buildingDetailsDao.selectOne(Wrappers.<BuildingDetailsEntity>lambdaQuery().eq(BuildingDetailsEntity::getBuildingNo, buildingRating.getBuildingNo()).last("LIMIT 1"));

        Optional.ofNullable(buildingDetail).ifPresent(detail -> {
            String floorCount = Optional.ofNullable(detail.getBuildingNumber()).map(String::valueOf).orElse(detail.getBuildingNumberInput());
            String rent = Optional.ofNullable(detail.getBuildingPrice()).map(Object::toString).orElse(detail.getBuildingPriceInput());
            businessVO.setFloorCount(floorCount);
            businessVO.setRent(rent);
        });

    }


    /**
     * 从商机信息上解析客户信息
     *
     * @param json CRM 返回的结果
     * @return 客户列表
     */
    private List<CrmBusinessDto> parseCustomers(String json) {
        if (StringUtils.isBlank(json)) {
            return Collections.emptyList();
        }

        // 将CRM返回结果解析成JSON对象
        JSONObject crmResult = parseToJson(json);
        JSONArray jsonArray = (JSONArray) crmResult.getByPath("list");
        if (Objects.isNull(jsonArray) || jsonArray.isEmpty()) {
            return Collections.emptyList();
        }

        // 结构: data.list[n].customer[0].customerId/businessId/customerName
        List<CrmBusinessDto> businessList = Lists.newArrayListWithExpectedSize(jsonArray.size());
        for (Object obj : jsonArray) {
            JSONObject jsonObject = (JSONObject) obj;
            JSONObject customer = (JSONObject) jsonObject.getByPath("customer[0]");
            if (Objects.isNull(customer)) {
                continue;
            }
            businessList.add(new CrmBusinessDto()
                    .setOwner(jsonObject.getStr("ownerUserName"))
                    .setCustomerId(customer.getStr("customerId"))
                    .setBusinessId(customer.getStr("businessId"))
                    .setName(customer.getStr("customerName"))
                    .setBatchId(jsonObject.getStr("batchId"))
                    .setTypeId(jsonObject.getStr("typeId")));
        }

        return businessList;
    }

    /**
     * 从商机信息上解析客户信息
     *
     * @param json CRM 返回的结果
     * @return 客户信息
     */
    private CrmBusinessDto parseCustomer(String json) {
        if (StringUtils.isBlank(json)) {
            return null;
        }

        // 将CRM返回结果解析成JSON对象
        JSONObject crmResult = parseToJson(json);
        JSONObject customer = (JSONObject) crmResult.getByPath("customer[0]");
        if (Objects.isNull(customer)) {
            return null;
        }

        return new CrmBusinessDto()
                .setOwner(crmResult.getStr("ownerUserName"))
                .setCustomerId(customer.getStr("customerId"))
                .setBusinessId(customer.getStr("businessId"))
                .setName(customer.getStr("customerName"))
                .setBatchId(crmResult.getStr("batchId"))
                .setTypeId(crmResult.getStr("typeId"));
    }
}
