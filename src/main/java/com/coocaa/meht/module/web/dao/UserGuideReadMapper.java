package com.coocaa.meht.module.web.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coocaa.meht.module.web.entity.UserGuideRead;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户已读引导记录Mapper接口
 */
@Mapper
public interface UserGuideReadMapper extends BaseMapper<UserGuideRead> {

    /**
     * 检查用户是否已读某功能引导
     *
     * @param userCode    用户编码
     * @param featureCode 功能编码
     * @return 是否已读
     */
    Boolean checkUserRead(@Param("userCode") String userCode, @Param("featureCode") String featureCode);
} 