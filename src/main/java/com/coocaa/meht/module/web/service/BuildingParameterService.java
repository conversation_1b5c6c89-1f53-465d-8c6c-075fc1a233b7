package com.coocaa.meht.module.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.meht.module.web.dto.BuildingTypesDto;
import com.coocaa.meht.module.web.entity.BuildingParameterEntity;

import java.util.List;
import java.util.Map;

public interface BuildingParameterService extends IService<BuildingParameterEntity> {


    List<BuildingTypesDto> getBuildingType(String buildingNo);

    List<BuildingTypesDto> getParameterBuildingType(Integer  dataVersion);

    Map<String,BuildingParameterEntity> getByMapIds(List<Long> ids);
}
