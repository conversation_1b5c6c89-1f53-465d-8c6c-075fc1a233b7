package com.coocaa.meht.module.web.controller;

import com.coocaa.meht.common.Result;
import com.coocaa.meht.module.web.service.ttc.ITtcService;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Set;

/**
 * Redis  缓存相关接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-10
 */
@RestController
@RequestMapping("/redis")
@AllArgsConstructor
public class RedisController {


    @Resource
    private  StringRedisTemplate stringRedisTemplate;

    /**
     * 查询string类型缓存
     */
    @Operation(summary = "查询string类型缓存")
    @GetMapping("/string/query")
    public Result<String> stringQuery(@RequestParam(name = "key", required = true) String key) {
        return Result.ok(stringRedisTemplate.opsForValue().get(key));
    }

    /**
     * 删除string类型缓存
     */
    @Operation(summary = "删除string类型缓存")
    @GetMapping("/string/delete")
    public Result<Boolean> stringDelete(@RequestParam(name = "key", required = true) String key) {
        Set<String> keys = stringRedisTemplate.keys("kanban:building*");
        if (CollectionUtils.isEmpty(keys)){
            return Result.ok();
        }
        for (String eachKey : keys){
            stringRedisTemplate.delete(eachKey);
        }
        return Result.ok();
    }

    /**
     * 更新string类型缓存
     */
    @Operation(summary = "更新string类型缓存")
    @GetMapping("/string/add-update")
    public Result<String> stringUpdate(@RequestParam(name = "key", required = true) String key,
                                               @RequestParam(name = "value", required = true) String value) {
        stringRedisTemplate.opsForValue().set(key, value);
        return Result.ok();
    }
}
