package com.coocaa.meht.module.web.service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 用户引导服务接口
 * @since 2023-11-20
 */

/**
 * 用户引导服务接口
 */
public interface UserGuideService {

    /**
     * 检查用户是否已读某功能引导
     *
     * @param featureCode 功能编码
     * @param userCode    用户编码
     * @return 是否已读
     */
    boolean checkUserRead(String featureCode, String userCode);

    /**
     * 标记用户已读某功能引导
     *
     * @param userCode    用户编码
     * @param featureCode 功能编码
     * @param operator    操作人
     * @return 是否标记成功
     */
    boolean markRead(String userCode, String featureCode, String operator);
} 