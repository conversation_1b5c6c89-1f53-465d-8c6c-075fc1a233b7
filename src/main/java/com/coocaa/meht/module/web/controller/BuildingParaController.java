package com.coocaa.meht.module.web.controller;

import com.coocaa.meht.aop.Anonymous;
import com.coocaa.meht.aop.Reqlog;
import com.coocaa.meht.common.Result;
import com.coocaa.meht.module.web.dto.BuildingTypesDto;
import com.coocaa.meht.module.web.service.BuildingParameterService;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 配置参数
 */
@RestController
@RequestMapping("/parameter")
@AllArgsConstructor
public class BuildingParaController {

    @Resource
    private BuildingParameterService buildingParameterService;

    /**
     * 已做迁移todo待删除
     */
    @GetMapping("/list")
    @Reqlog(value = "参数列表", type = Reqlog.LogType.SELECT)
    @Anonymous
    public Result<List<BuildingTypesDto>> list(@RequestParam(value = "buildingNo", required = false) String buildingNo) {
        List<BuildingTypesDto> buildingTypesDtoList = buildingParameterService.getBuildingType(buildingNo);
        return Result.ok(buildingTypesDtoList);
    }

    /**
     * pc 端用
     */
    @GetMapping("/list/dataFlag")
    @Reqlog(value = "参数列表", type = Reqlog.LogType.SELECT)
    @Anonymous
    public Result<List<BuildingTypesDto>> list(@RequestParam("dataFlag") Integer dataFlag) {
        List<BuildingTypesDto> buildingTypesDtoList = buildingParameterService.getParameterBuildingType(dataFlag);
        return Result.ok(buildingTypesDtoList);
    }
}
