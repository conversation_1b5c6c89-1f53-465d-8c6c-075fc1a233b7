<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.meht.module.web.dao.UserGuideReadMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.coocaa.meht.module.web.entity.UserGuideRead">
        <id column="id" property="id" />
        <result column="user_code" property="userCode" />
        <result column="feature_code" property="featureCode" />
        <result column="read_time" property="readTime" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="delete_flag" property="deleteFlag" />
    </resultMap>

    <!-- 检查用户是否已读某功能引导 -->
    <select id="checkUserRead" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM user_guide_read
        WHERE user_code = #{userCode}
          AND feature_code = #{featureCode}
          AND delete_flag = 0
    </select>

</mapper> 