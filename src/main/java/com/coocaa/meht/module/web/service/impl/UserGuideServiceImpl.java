package com.coocaa.meht.module.web.service.impl;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 用户引导服务实现类
 * @since 2023-11-20
 */
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.module.web.dao.UserGuideReadMapper;
import com.coocaa.meht.module.web.entity.UserGuideRead;
import com.coocaa.meht.module.web.service.UserGuideService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 用户引导服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserGuideServiceImpl extends ServiceImpl<UserGuideReadMapper, UserGuideRead> implements UserGuideService {

    private final UserGuideReadMapper userGuideReadMapper;

    @Override
    public boolean checkUserRead(String featureCode, String userCode) {
        log.info("检查用户是否已读功能引导: featureCode={}, userCode={}", featureCode, userCode);
        Boolean hasRead = userGuideReadMapper.checkUserRead(userCode, featureCode);
        log.info("检查用户是否已读功能引导结果: featureCode={}, userCode={}, hasRead={}", featureCode, userCode, hasRead);
        return hasRead != null && hasRead;
    }

    @Override
    public boolean markRead(String userCode, String featureCode, String operator) {
        log.info("标记用户已读功能引导: userCode={}, featureCode={}, operator={}", userCode, featureCode, operator);
        UserGuideRead record = new UserGuideRead();
        record.setUserCode(userCode);
        record.setFeatureCode(featureCode);
        record.setReadTime(new Date());
        record.setCreateBy(operator);
        record.setUpdateBy(operator);
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());
        record.setDeleteFlag(0);
        boolean result = userGuideReadMapper.insert(record) > 0;
        log.info("标记用户已读功能引导结果: userCode={}, featureCode={}, result={}", userCode, featureCode, result);
        return result;
    }
} 