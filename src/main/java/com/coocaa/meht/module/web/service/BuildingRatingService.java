package com.coocaa.meht.module.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.meht.module.crm.dto.CrmCustomerListDto;
import com.coocaa.meht.module.crm.dto.req.TransferReq;
import com.coocaa.meht.module.crm.vo.BuildingVO;
import com.coocaa.meht.module.web.dto.BigScreenCalculateDTO;
import com.coocaa.meht.module.web.dto.BuildingRateDto;
import com.coocaa.meht.module.web.dto.BuildingRatingExportDTO;
import com.coocaa.meht.module.web.dto.RatingApplyDto;
import com.coocaa.meht.module.web.dto.RatingApproveDto;
import com.coocaa.meht.module.web.dto.RatingCalculateDto;
import com.coocaa.meht.module.web.dto.RatingCalculateManualDto;
import com.coocaa.meht.module.web.dto.VerifyRatingDTO;
import com.coocaa.meht.module.web.dto.req.BuildingPicReq;
import com.coocaa.meht.module.web.entity.BuildingDetailsEntity;
import com.coocaa.meht.module.web.entity.BuildingParameterEntity;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.vo.BuildingMarginVO;
import com.coocaa.meht.module.web.vo.BuildingRatingPicVO;
import com.coocaa.meht.module.web.vo.BuildingStatusVO;
import com.coocaa.meht.module.web.vo.BuildingTrialCalculateVO;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface BuildingRatingService extends IService<BuildingRatingEntity> {

    Map<String, Object> getByRating(String name, String mapNo);

    Map<String, Object> apply(RatingApplyDto dto);

    List<Map<String, Object>> getUv();

    Map<String, Object> applyList(String name ,Integer pageSize , Integer pageNum) ;

    Map<String, Object> approveList(String name, Integer status ,Integer pageSize , Integer pageNum);

    Map<String, Object> approve(RatingApproveDto dto);

    Map<String, Object> info(String buildingNo);

    Map<String, Object> applyUpdate(RatingApplyDto dto);

    Map<String, Object> getScoringDetails(String buildingNo);

    Map<String, Object> scoringFsDetails(String buildingNo);

    BuildingRateDto infoFs(String buildingNo);

    /**
     * 定时任务重新推送(兜底) 推送失败的楼宇信息到crm
     * @param buildingNo
     */
    void cronBuildingPushCrm(String buildingNo);

    List<Map<String, Object>> getStatusChart();

    List<Map<String, Object>> getList();

    List<Map<String, Object>> getProvinceList();

    List<Map<String, Object>> getTotal();

    /**
     * 填充crm楼宇信息
     */
    void fillCrmBuilding(List<CrmCustomerListDto> crmCustomerDtoList);

    List<BuildingRatingEntity> listNotCustomerId();

    List<BuildingRatingEntity> listPushCrm(Long buildingId);

    void updateCustomerId(Long id, String customerId);


    /**
     * 根据名称查询已经审批的楼宇
     *
     * @param name    楼宇名称
     * @param allUser 不检查当前登陆用户
     * @return 匹配的数据
     */
    List<Map<String, Object>> listAudited(String name, boolean allUser);


    /**
     * 根据楼宇编号 获取楼宇基本信息
     * @param buildingNo
     * @return
     */
    BuildingRatingEntity getByBuildingNo(String buildingNo);

    /**
     * 根据楼宇编号 获取楼宇基本信息
     * @param buildingNoList
     * @return
     */
    List<BuildingRatingEntity> listByBuildingNo(List<String> buildingNoList);

    /**
     * 楼宇试算
     * @param dto
     * @return
     */
    BuildingTrialCalculateVO calculate(RatingCalculateDto dto);

    /**
     * 获取关联了crm流程的楼宇记录列表
     * @return
     */
    List<BuildingRatingEntity> listCrmFlow();

    /**
     * 获取楼宇图片列表
     * @return
     */
    BuildingRatingPicVO buildingPic(BuildingPicReq buildingPicReq);

    /**
     * 指定某个客户转移
     * @param req
     * @return
     */
    Boolean transferCustomer(TransferReq req);

    /**
     * 批量客户转移
     * @param req
     * @return
     */
    Boolean transferCustomerBatch(TransferReq req);


    /**
     * 判断是否有合同
     * @param mapNo
     * @return
     */
    Boolean hasContract(String mapNo);

    /**
     * 获取所有城市
     * @return
     */
    List<String> buildingCIty();


    /**
     * 楼宇助手的状态
     * @return
     */
    BuildingStatusVO buildingStatus(String mapNo);


    /**
     * 楼宇助手的余量信息
     * @return
     */
    BuildingMarginVO buildingMargin(String mapNo);


    String importAiData(MultipartFile file);


    String topLevel(String buildingNo);

    String largeScreenCalculate(BigScreenCalculateDTO param);

    BuildingVO getBuildingLocation(String buildingNo);

    BuildingTrialCalculateVO manualCalculate(RatingCalculateManualDto dto);

    int isFinishRating(List<VerifyRatingDTO> dtoList);

    /**
     * 完善评级按楼宇编号回滚操作
     * @param buildingNo 楼宇编号
     */
    void rollBack(String buildingNo);

    /**
     * 完善评级按快照数据id回滚操作
     * @param id 快照数据id
     */
    void rollBack(Integer id);

    List<String> getHighSeaCities();

    BuildingDetailsEntity processDetailData(List<BuildingParameterEntity> parameterList, BuildingDetailsEntity details, Integer buildingType, String mapAdCode);

    BigDecimal calculateAIScoreByBuildingType(BuildingDetailsEntity details, String adCode, Integer buildingType, BigDecimal coefficient);

    String calculateProjectLevel2(BigDecimal score);

    void export(HttpServletResponse response, BuildingRatingExportDTO dto);

    void processNumberMatch(BuildingDetailsEntity details, String code,
                            Map<String, List<BuildingParameterEntity>> ruleMap);

    void processRentMatch(BuildingDetailsEntity details, String code,
                          Map<String, List<BuildingParameterEntity>> ruleMap, String mapAdCode, Integer buildingType);

    BigDecimal calculateScoreByBuildingType(BuildingDetailsEntity details, String adCode, Integer buildingType , Integer dataFlag);
}
