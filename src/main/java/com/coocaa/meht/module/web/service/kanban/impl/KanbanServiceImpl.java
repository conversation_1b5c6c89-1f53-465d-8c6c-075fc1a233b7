package com.coocaa.meht.module.web.service.kanban.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.coocaa.meht.common.SecurityUser;
import com.coocaa.meht.common.bean.CodeNameVO;
import com.coocaa.meht.common.bean.ResultTemplate;
import com.coocaa.meht.common.exception.ErrorCode;
import com.coocaa.meht.common.exception.ServerException;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.entity.BuildingStatusChangeLogEntity;
import com.coocaa.meht.module.web.entity.BusinessOpportunityEntity;
import com.coocaa.meht.module.web.service.BuildingRatingService;
import com.coocaa.meht.module.web.service.BusinessOpportunityService;
import com.coocaa.meht.module.web.service.IBuildingStatusChangeLogService;
import com.coocaa.meht.module.web.service.kanban.BuildingIncrementStatisticsHandler;
import com.coocaa.meht.module.web.service.kanban.BuildingTotalStatisticsHandler;
import com.coocaa.meht.module.web.service.kanban.IKanbanService;
import com.coocaa.meht.module.web.vo.kanban.BuildingDataVO;
import com.coocaa.meht.module.web.vo.kanban.CityStatisticsItemVO;
import com.coocaa.meht.module.web.vo.kanban.CityStatisticsVO;
import com.coocaa.meht.module.web.vo.kanban.ContractPointStatisticsVO;
import com.coocaa.meht.module.web.vo.kanban.DataAccessCityParam;
import com.coocaa.meht.module.web.vo.kanban.DataAccessVO;
import com.coocaa.meht.module.web.vo.kanban.DataItemVO;
import com.coocaa.meht.module.web.vo.kanban.KanbanDeviceStatisticsVO;
import com.coocaa.meht.module.web.vo.kanban.KanbanPointStatisticsVO;
import com.coocaa.meht.module.web.vo.kanban.KanbanVO;
import com.coocaa.meht.module.web.vo.kanban.ProjectDataVO;
import com.coocaa.meht.module.web.vo.kanban.StatusChangeVO;
import com.coocaa.meht.rpc.FeignAuthorityRpc;
import com.coocaa.meht.rpc.FeignCmsRpc;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Function;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.connection.RedisStringCommands;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.types.Expiration;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @file IKanbanServiceImpl
 * @date 2025/1/2 15:27
 * @description 媒资平台 - 数据看板
 */

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class KanbanServiceImpl implements IKanbanService {
    /**
     * 看板楼宇转化率缓存key格式
     * 如 "kanban:building:{cityName}"
     */
    private static final String CACHE_KEY_FORMAT = "kanban:building:%s";

    private final BuildingRatingService buildingRatingService;
    private final IBuildingStatusChangeLogService buildingStatusChangeLogService;
    private final BusinessOpportunityService businessOpportunityService;
    private final StringRedisTemplate stringRedisTemplate;
    private final BuildingTotalStatisticsHandler buildingTotalStatisticsHandler;
    private final BuildingIncrementStatisticsHandler buildingIncrementStatisticsHandler;
    private final FeignCmsRpc feignCmsRpc;
    private final FeignAuthorityRpc feignAuthorityRpc;
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();


    @Value("${point.status.viewed.days:14}")
    private int pointStatusViewedDays;

    @Value("${building.ratio.min.days:3}")
    private int buildingMinDays;
    @Value("${building.ratio.max.days:7}")
    private int buildingMaxDays;
    @Value("${project.ratio.min.days:7}")
    private int projectMinDays;
    @Value("${project.ratio.max.days:14}")
    private int projectMaxDays;


    /**
     * 获取当前用户城市列表map
     *
     * @param cityId 城市ID
     */
    private Map<Integer, String> getUserCityIdNameMap(Integer cityId) {
        // 验证用户城市列表权限
        DataAccessVO dataAccessVO = getUserDataAccess();
        if (dataAccessVO == null || dataAccessVO.getCityList().isEmpty()) {
            throw new ServerException(ErrorCode.NO_PERMISSION);
        }
        Map<Integer, String> cityIdNameMap = dataAccessVO.getCityList().stream()
                .collect(Collectors.toMap(DataAccessCityParam::getId, DataAccessCityParam::getCityName));
        if (cityId != 0 && !cityIdNameMap.containsKey(cityId)) {
            throw new ServerException(ErrorCode.NO_PERMISSION);
        }

        return cityIdNameMap;
    }

    /**
     * 获取合同统计信息
     *
     * @param cityId 城市ID
     */
    @Override
    public KanbanVO getContractStatusInfo(Integer cityId) {
        // 获取用户城市列表权限
        Map<Integer, String> userCityIdNameMap = getUserCityIdNameMap(cityId);
        // 获取当前用户wno
        String wno = SecurityUser.getUserCode();
        List<Integer> cityIdList = cityId == 0 ? userCityIdNameMap.keySet().stream().toList() : List.of(cityId);
        ResultTemplate<KanbanVO> contractStatusInfo = feignCmsRpc.getContractStatusInfo(wno, cityIdList);
        //
        if (contractStatusInfo.getSuccess()) {
            return contractStatusInfo.getData();
        }
        return null;
    }

    /**
     * 获取楼宇总量统计信息
     *
     * @param cityId 城市ID
     */
    @Override
    public KanbanVO getBuildingTotalStatisticsInfo(Integer cityId) {
        // 获取用户城市列表权限
        Map<Integer, String> userCityIdNameMap = getUserCityIdNameMap(cityId);
        KanbanVO result = cityId == 0
                ? computeCityKanbanVO(userCityIdNameMap.values().stream()
                .toList(), BuildingTotalStatisticsHandler.CACHE_KEY_FORMAT, buildingTotalStatisticsHandler)
                : computeCityKanbanVO(userCityIdNameMap.get(cityId), BuildingTotalStatisticsHandler.CACHE_KEY_FORMAT, BuildingTotalStatisticsHandler.KPI_FLAG);

        return result == null ? buildingTotalStatisticsHandler.getDefaultKanbanVO() : result;
    }

    /**
     * 楼宇 - 总量统计
     */
    @Override
    public void buildingTotalStatistics() {
        // 获取昨天23:59:59的时间
        LocalDateTime yesterdayEndTime = LocalDateTime.of(LocalDate.now().minusDays(1), LocalTime.of(23, 59, 59));
        // 查询所有楼宇信息
        LambdaQueryWrapper<BuildingRatingEntity> queryWrapper = new QueryWrapper<BuildingRatingEntity>().lambda();
        queryWrapper.eq(BuildingRatingEntity::getDeleted, 0)
                .select(BuildingRatingEntity::getId, BuildingRatingEntity::getBuildingNo, BuildingRatingEntity::getBuildingStatus,
                        BuildingRatingEntity::getStatus, BuildingRatingEntity::getMapCity, BuildingRatingEntity::getMapAdCode,
                        BuildingRatingEntity::getProjectLevel, BuildingRatingEntity::getApproveTime);
        Map<String, List<BuildingRatingEntity>> buildingRatingByCityCodeMap = buildingRatingService.list(queryWrapper)
                .stream().collect(Collectors.groupingBy(BuildingRatingEntity::getMapCity));
        // 查询商机记录
        LambdaQueryWrapper<BusinessOpportunityEntity> queryWrapper2 = new LambdaQueryWrapper<>();
        queryWrapper2.le(BusinessOpportunityEntity::getCreateTime, yesterdayEndTime)
                .select(BusinessOpportunityEntity::getId, BusinessOpportunityEntity::getName, BusinessOpportunityEntity::getCode,
                        BusinessOpportunityEntity::getStatus, BusinessOpportunityEntity::getBuildingNo, BusinessOpportunityEntity::getCreateTime);
        Map<String, List<BusinessOpportunityEntity>> businessOpportunityByBuildingNoMap = businessOpportunityService.list(queryWrapper2)
                .stream().collect(Collectors.groupingBy(BusinessOpportunityEntity::getBuildingNo));
        // 查询商机动态记录
        LambdaQueryWrapper<BuildingStatusChangeLogEntity> queryWrapper3 = new QueryWrapper<BuildingStatusChangeLogEntity>().lambda();
        queryWrapper3.le(BuildingStatusChangeLogEntity::getChangeTime, yesterdayEndTime)
                .in(BuildingStatusChangeLogEntity::getType, List.of(BuildingStatusChangeLogEntity.BizType.RATING.getCode(), BuildingStatusChangeLogEntity.BizType.BUSINESS.getCode()))
                .eq(BuildingStatusChangeLogEntity::getDeleteFlag, 0)
                .select(BuildingStatusChangeLogEntity::getBizId, BuildingStatusChangeLogEntity::getType, BuildingStatusChangeLogEntity::getBizCode,
                        BuildingStatusChangeLogEntity::getStatus, BuildingStatusChangeLogEntity::getChangeTime);
        Map<String, Map<String, List<BuildingStatusChangeLogEntity>>> buildingStatusChangeLogByBizCodeMap = buildingStatusChangeLogService.list(queryWrapper3)
                .stream().collect(Collectors.groupingBy(
                        BuildingStatusChangeLogEntity::getType, // 按type进行分组
                        Collectors.groupingBy(BuildingStatusChangeLogEntity::getBizCode)));// 按bizCode进行分组

        // 对楼宇记录进行统计 - 按城市
        Map<String, KanbanVO> kanbanVOByCityCodeMap = new HashMap<>(buildingRatingByCityCodeMap.size());
        buildingTotalStatisticsHandler.buildingRecordsAnalysedByCity(buildingRatingByCityCodeMap,
                businessOpportunityByBuildingNoMap, buildingStatusChangeLogByBizCodeMap, kanbanVOByCityCodeMap);

        // 数据放入缓存
        saveResultToCache(kanbanVOByCityCodeMap, BuildingTotalStatisticsHandler.CACHE_KEY_FORMAT);
    }

    /**
     * 获取楼宇新增统计信息
     *
     * @param cityId 城市ID
     */
    @Override
    public KanbanVO getBuildingIncrementStatisticsInfo(Integer cityId) {
        // 获取用户城市列表权限
        Map<Integer, String> userCityIdNameMap = getUserCityIdNameMap(cityId);// 当cityId=0时，代表要查询该用户的所有可用的城市列表
        KanbanVO result = cityId == 0
                ? computeCityKanbanVO(userCityIdNameMap.values().stream()
                .toList(), BuildingIncrementStatisticsHandler.CACHE_KEY_FORMAT, buildingIncrementStatisticsHandler)
                : computeCityKanbanVO(userCityIdNameMap.get(cityId), BuildingIncrementStatisticsHandler.CACHE_KEY_FORMAT, BuildingIncrementStatisticsHandler.KPI_FLAG);

        return result == null ? buildingIncrementStatisticsHandler.getDefaultKanbanVO() : result;
    }

    /**
     * 楼宇 - 新增统计
     */
    @Override
    public void buildingIncrementStatistics() {
        // 获取指标数据类型的数据
        Map<String, KanbanVO> kanbanVOByCityCodeMap = buildingIncrementStatisticsHandler.getKanbanVOByCityCodeMap();

        // 数据放入缓存
        saveResultToCache(kanbanVOByCityCodeMap, BuildingIncrementStatisticsHandler.CACHE_KEY_FORMAT);
    }

    /**
     * 计算城市的指标数据
     */
    private KanbanVO computeCityKanbanVO(String cityName, String cacheFormat, String kpi_flag) {
        // 查询缓存数据
        try {
            String cacheResult = stringRedisTemplate.opsForValue().get(String.format(cacheFormat, cityName));
            if (cacheResult == null) {
                return null;
            }
            return OBJECT_MAPPER.readValue(cacheResult, KanbanVO.class);
        } catch (JsonProcessingException e) {
            log.error("{}缓存数据转换失败！", kpi_flag, e.getMessage());
            throw new RuntimeException(e);
        }
    }

    private KanbanVO computeCityKanbanVO(List<String> cityNameList, String cacheFormat, Object instHandler) {
        if (cityNameList == null || cityNameList.isEmpty()) {
            return null;
        }
        List<String> cityResultList = stringRedisTemplate.opsForValue()
                .multiGet(cityNameList.stream().map(cityName -> String.format(cacheFormat, cityName)).toList()).stream()
                .filter(e -> StringUtils.isNotBlank(e)).toList();
        if (cityResultList == null || cityResultList.isEmpty()) {
            return null;
        }
        // 汇总城市数据
        if (instHandler instanceof BuildingTotalStatisticsHandler) {
            return ((BuildingTotalStatisticsHandler) instHandler).getKanbanVOByCityResultList(cityResultList);
        } else if (instHandler instanceof BuildingIncrementStatisticsHandler) {
            return ((BuildingIncrementStatisticsHandler) instHandler).getKanbanVOByCityResultList(cityResultList);
        }
        return null;
    }

    /**
     * 将结果数据放进缓存
     */
    private void saveResultToCache(Map<String, KanbanVO> kanbanVOByCityCodeMap, String cacheKeyFormat) {
        // 按城市保存结果数据
        stringRedisTemplate.executePipelined((RedisCallback<Void>) conn -> {
            kanbanVOByCityCodeMap.forEach((cityCode, kanbanVO) -> {
                String cacheKey = String.format(cacheKeyFormat, cityCode);
                try {
                    long timeout = 86400 + ThreadLocalRandom.current().nextInt(1, 100);
                    conn.stringCommands()
                            .set(cacheKey.getBytes(StandardCharsets.UTF_8), OBJECT_MAPPER.writeValueAsBytes(kanbanVO), Expiration.seconds(timeout), RedisStringCommands.SetOption.upsert());
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
            });
            return null;
        });

        log.info("[{}]下{}个城市的缓存数据统计结果已更新！", cacheKeyFormat, kanbanVOByCityCodeMap.size());
    }


    @Override
    public void executeRatioStatistics(LocalDateTime date) {
        try {
            // 昨日23:59:59
            if (date == null) {
                date = LocalDateTime.of(LocalDate.now().minusDays(1), LocalTime.of(23, 59, 59));
            }

            // 并行执行统计任务
            Map<String, List<?>> statisticsData = executeStatisticsParallel(date);

            // 转换数据为Map
            Map<String, Map<String, ?>> dataMaps = convertToDataMaps(statisticsData);

            // 收集所有城市名称
            Set<String> cityNames = collectAllCityNames(statisticsData);

            // 批量构建统计数据并写入缓存
            batchSaveStatistics(cityNames, dataMaps);

            log.info("Successfully executed ratio statistics for {} cities", cityNames.size());
        } catch (Exception e) {
            log.error("Failed to execute ratio statistics", e);
            throw new RuntimeException("Failed to execute ratio statistics", e);
        }
    }

    @Override
    public DataAccessVO getUserDataAccess() {
        // 1. 获取并验证用户编码
        String wno = Optional.ofNullable(SecurityUser.getUserCode())
                .orElseThrow(() -> new IllegalArgumentException("获取用户为空"));

        // 2. 获取用户数据访问权限
        DataAccessVO userAccess = getUserDataAccessDetail(wno);
        if (userAccess == null) {
            return null;
        }

        // 3. 获取可用城市列表
        List<CodeNameVO> availableCities = getAvailableCities();
        if (availableCities == null) {
            return null;
        }

        // 4. 处理城市权限
        processCityAccess(userAccess, availableCities);

        return userAccess;
    }

    private DataAccessVO getUserDataAccessDetail(String wno) {
        try {
            ResultTemplate<DataAccessVO> result = feignCmsRpc.getUserDataAccessDetail(wno, wno);
            log.info("User data access detail: {}", JSON.toJSONString(result));

            if (!result.getSuccess() || result.getData() == null || CollectionUtils.isEmpty(result.getData()
                    .getCityList())) {
                return null;
            }
            return result.getData();
        } catch (Exception e) {
            log.error("获取用户权限异常: {}", e.getMessage(), e);
            throw new RuntimeException("获取用户权限异常", e);
        }
    }

    private List<CodeNameVO> getAvailableCities() {
        try {
            ResultTemplate<List<CodeNameVO>> result = feignAuthorityRpc.getAvailableCities();
            if (!result.getSuccess()) {
                return null;
            }
            return result.getData();
        } catch (Exception e) {
            log.error("获取权限系统用户信息异常: {}", e.getMessage(), e);
            throw new RuntimeException("获取权限系统用户信息异常", e);
        }
    }

    private void processCityAccess(DataAccessVO userAccess, List<CodeNameVO> availableCities) {
        Set<Integer> userCityIds = userAccess.getCityList().stream().map(DataAccessCityParam::getId)
                .collect(Collectors.toSet());

        Set<Integer> availableCityIds = availableCities.stream().map(CodeNameVO::getId).collect(Collectors.toSet());

        // 如果用户有全部城市权限(id=0)，则使用所有可用城市
        if (userCityIds.contains(0)) {
            List<DataAccessCityParam> allCities = availableCities.stream()
                    .map(city -> new DataAccessCityParam(city.getId(), city.getName())).collect(Collectors.toList());
            userAccess.setCityList(allCities);
        } else {
            // 否则过滤出用户拥有权限的可用城市
            List<DataAccessCityParam> filteredCities = userAccess.getCityList().stream()
                    .filter(city -> availableCityIds.contains(city.getId())).collect(Collectors.toList());
            userAccess.setCityList(filteredCities);
        }
    }

    private DataAccessVO getCachedUserDataAccess(String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }

        String userInfoStr = stringRedisTemplate.opsForValue().get(key);
        if (StringUtils.isBlank(userInfoStr)) {
            return null;
        }
        return JSON.parseObject(userInfoStr, DataAccessVO.class);
    }

    @Override
    public KanbanPointStatisticsVO getPointStatusInfo(LocalDate date, Integer days, Integer cityId) {
        // 1. 参数处理
        date = Optional.ofNullable(date).orElse(LocalDate.now());
        days = Optional.ofNullable(days).orElse(pointStatusViewedDays);

        // 2. 获取数据
        List<ContractPointStatisticsVO> trendData = fetchPointStatusData(date, days, cityId);
        if (CollectionUtils.isEmpty(trendData)) {
            log.info("获取点位状态信息为空");
            return null;
        }

        // 3. 获取最后一天数据
        ContractPointStatisticsVO oriLastDayData = trendData.get(trendData.size() - 1);
        ContractPointStatisticsVO lastDayData =new ContractPointStatisticsVO();
        BeanUtils.copyProperties(oriLastDayData, lastDayData);
        lastDayData.setInstalledCount(null);

        // 4. 构建返回结果
        return buildPointStatisticsResult(lastDayData, trendData);
    }

    private List<ContractPointStatisticsVO> fetchPointStatusData(LocalDate date, int days, Integer cityId) {
        String wno = SecurityUser.getUserCode();
        String dataParam = date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        ResultTemplate<List<ContractPointStatisticsVO>> response = feignCmsRpc.getPointStatusInfo(wno, dataParam, days, cityId);
        LocalDate startDate = date.minusDays(days - 1);
        List<ContractPointStatisticsVO> result = new ArrayList<>();
        List<ContractPointStatisticsVO> data = response.getData();
        if (!response.getSuccess() || CollectionUtils.isEmpty(data)) {
            data = new ArrayList<>();
        }
        // StatisticsDate -> 日期
        Map<String, ContractPointStatisticsVO> dataMap = data.stream()
                .collect(Collectors.toMap(ContractPointStatisticsVO::getStatisticsDate, Function.identity()));
        // 按照days 遍历
        for (int i = 0; i < days; i++) {
            LocalDate itemLocalDate = startDate.plusDays(i);
            String itemDate = itemLocalDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            ContractPointStatisticsVO item = dataMap.get(itemDate);
            if (item == null) {
                item = new ContractPointStatisticsVO();
                item.setCityId(cityId);
                item.setStatisticsDate(itemDate);
            }
            result.add(item);
        }
        // 日期统计格式：yyyy-MM-dd -> MM/dd 不要yyyy
        result.forEach(item -> item.setStatisticsDate(item.getStatisticsDate().replace("-", "/").substring(5)));
        return result;
    }

    private List<ContractPointStatisticsVO> assembleEmptyData(LocalDate date, int days, Integer cityId) {
        List<ContractPointStatisticsVO> result = new ArrayList<>();
        LocalDate eachDate = date.minusDays(days);
        for (int i = 0; i < days; i++) {
            ContractPointStatisticsVO item = new ContractPointStatisticsVO();
            item.setCityId(cityId);
            item.setStatisticsDate(eachDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            result.add(item);
            eachDate = eachDate.plusDays(1);
        }
        return result;
    }

    private KanbanPointStatisticsVO buildPointStatisticsResult(ContractPointStatisticsVO lastDayData, List<ContractPointStatisticsVO> trendData) {
        // 构建比率数据
        List<DataItemVO> ratioData = Arrays.asList(
                new DataItemVO("归档数", lastDayData.getSignedCount(), null),
                new DataItemVO("计费数", lastDayData.getChargedCount(), calculateRate(lastDayData.getChargedCount(), lastDayData.getSignedCount())),
                new DataItemVO("可售数", lastDayData.getAvailableCount(), calculateRate(lastDayData.getAvailableCount(), lastDayData.getChargedCount())));

        // 构建返回对象
        return new KanbanPointStatisticsVO().setCurrentData(lastDayData).setRatioData(ratioData)
                .setTrendData(trendData);
    }

    /**
     * 并行执行统计
     *
     * @param date
     * @return
     */
    private Map<String, List<?>> executeStatisticsParallel(LocalDateTime date) {
        Map<String, List<?>> result = new ConcurrentHashMap<>();

        CompletableFuture.allOf(
                        CompletableFuture.runAsync(() -> result.put("threeDays", executeBuildingRatingStatistics(date, buildingMinDays))),
                        CompletableFuture.runAsync(() -> result.put("sevenDays", executeBuildingRatingStatistics(date, buildingMaxDays))),
                        CompletableFuture.runAsync(() -> result.put("sevenDaysProject", executeProjectStatistics(date, projectMinDays))),
                        CompletableFuture.runAsync(() -> result.put("fourteenDaysProject", executeProjectStatistics(date, projectMaxDays))))
                .join();

        return result;
    }

    @SuppressWarnings("unchecked")
    private Map<String, Map<String, ?>> convertToDataMaps(Map<String, List<?>> statisticsData) {
        Map<String, Map<String, ?>> dataMaps = new HashMap<>();

        dataMaps.put("threeDays", ((List<BuildingDataVO>) statisticsData.get("threeDays")).stream()
                .collect(Collectors.toMap(BuildingDataVO::getCityName, Function.identity())));
        dataMaps.put("sevenDays", ((List<BuildingDataVO>) statisticsData.get("sevenDays")).stream()
                .collect(Collectors.toMap(BuildingDataVO::getCityName, Function.identity())));
        dataMaps.put("sevenDaysProject", ((List<ProjectDataVO>) statisticsData.get("sevenDaysProject")).stream()
                .collect(Collectors.toMap(ProjectDataVO::getCityName, Function.identity())));
        dataMaps.put("fourteenDaysProject", ((List<ProjectDataVO>) statisticsData.get("fourteenDaysProject")).stream()
                .collect(Collectors.toMap(ProjectDataVO::getCityName, Function.identity())));

        return dataMaps;
    }

    private Set<String> collectAllCityNames(Map<String, List<?>> statisticsData) {
        return statisticsData.values().stream().flatMap(list -> list.stream()).map(obj -> {
            if (obj instanceof BuildingDataVO) {
                return ((BuildingDataVO) obj).getCityName();
            }
            return ((ProjectDataVO) obj).getCityName();
        }).collect(Collectors.toSet());
    }

    @SuppressWarnings("unchecked")
    private void batchSaveStatistics(Set<String> cityNames, Map<String, Map<String, ?>> dataMaps) {
        stringRedisTemplate.executePipelined((RedisCallback<Object>) connection -> {
            cityNames.forEach(cityName -> {
                CityStatisticsVO cityStatisticsVO = new CityStatisticsVO();
                cityStatisticsVO.setCityName(cityName);
                cityStatisticsVO.setThreeDaysBuildingData((BuildingDataVO) dataMaps.get("threeDays").get(cityName));
                cityStatisticsVO.setSevenDaysBuildingData((BuildingDataVO) dataMaps.get("sevenDays").get(cityName));
                cityStatisticsVO.setSevenDaysProjectData((ProjectDataVO) dataMaps.get("sevenDaysProject")
                        .get(cityName));
                cityStatisticsVO.setFourteenDaysProjectData((ProjectDataVO) dataMaps.get("fourteenDaysProject")
                        .get(cityName));
                String cacheKey = String.format(CACHE_KEY_FORMAT, cityName);
                byte[] key = cacheKey.getBytes(StandardCharsets.UTF_8);
                byte[] value = JSON.toJSONString(cityStatisticsVO).getBytes(StandardCharsets.UTF_8);
                connection.stringCommands().set(key, value, Expiration.seconds(3600*24), RedisStringCommands.SetOption.UPSERT);
            });
            return null;
        });
    }

    /**
     * 计算n天楼宇认证通过转化率
     *
     * @param now  当前时间
     * @param days 天数 如3天，7天
     * @return 统计数据
     */
    private List<BuildingDataVO> executeBuildingRatingStatistics(LocalDateTime now, int days) {
        LocalDateTime startDate = now.minusDays(days);

        // 获取n天待审核数据
        StatusChangeVO waitApprovedParam = buildQueryParam(BuildingStatusChangeLogEntity.BizType.RATING.getCode(), null, BuildingStatusChangeLogEntity.RatingApplicationStatus.WAIT_APPROVED.getCode(), startDate, now, null);
        List<StatusChangeVO> waitApprovedList = buildingStatusChangeLogService.getBuildingRatingStatusChangeList(waitApprovedParam);
        // 转换成map  cityName -> count
        Map<String, Integer> waitApprovedMap = waitApprovedList.stream()
                .collect(Collectors.groupingBy(StatusChangeVO::getCityName, Collectors.summingInt(e -> 1)));

        // 获取已审核数据
        Set<Integer> ids = waitApprovedList.stream().map(StatusChangeVO::getBizId).collect(Collectors.toSet());
        List<StatusChangeVO> approvedList = null;
        if (CollectionUtils.isEmpty(ids)) {
            approvedList = new ArrayList<>();
        } else {
            StatusChangeVO approvedParam = buildQueryParam(BuildingStatusChangeLogEntity.BizType.RATING.getCode(), null, BuildingStatusChangeLogEntity.RatingApplicationStatus.APPROVED.getCode(), startDate, now, ids);
            approvedList = buildingStatusChangeLogService.getBuildingRatingStatusChangeList(approvedParam);
        }

        Map<String, Integer> approvedMap = approvedList.stream()
                .collect(Collectors.groupingBy(StatusChangeVO::getCityName, Collectors.summingInt(e -> 1)));

        List<BuildingDataVO> statisticsDataList = new ArrayList<>();
        // 遍历waitApprovedMap 封装成  StatisticsData
        waitApprovedMap.forEach((cityName, count) -> {
            BuildingDataVO data = new BuildingDataVO(cityName, count, approvedMap.getOrDefault(cityName, 0), 0);
            statisticsDataList.add(data);
        });

        return statisticsDataList;
    }


    /**
     * 计算n天项目(商机)转化率
     *
     * @param date
     * @param day
     * @return
     */
    private List<ProjectDataVO> executeProjectStatistics(LocalDateTime date, int day) {
        LocalDateTime startDate = date.minusDays(day);
        Map<String, List<StatusChangeVO>> statusChangeMap = new HashMap<>();
        Map<String, Map<String, Integer>> statusCityCountMap = new HashMap<>();

        // 按状态流转顺序统计各阶段数据
        int index = 0;
        for (String currentStatus : PROJECT_STATUS_FLOW) {
            // 获取上一阶段的业务IDs
            Set<Integer> previousBizIds = getPreviousBizIds(statusChangeMap, currentStatus);

            List<StatusChangeVO> statusChangeList = null;
            if (CollectionUtils.isEmpty(previousBizIds) && index != 0) {
                statusChangeList = new ArrayList<>();
            } else {
                // 查询当前阶段数据
                StatusChangeVO queryParam = buildQueryParam(BuildingStatusChangeLogEntity.BizType.BUSINESS.getCode(), null, currentStatus, startDate, date, previousBizIds);
                // 获取并存储当前阶段数据
                statusChangeList = buildingStatusChangeLogService.getProjectStatusChangeList(queryParam);
            }
            statusChangeMap.put(currentStatus, statusChangeList);
            // 统计当前阶段各城市数量
            Map<String, Integer> cityCountMap = statusChangeList.stream()
                    .filter(vo -> StringUtils.isNotBlank(vo.getCityName()))
                    .collect(Collectors.groupingBy(StatusChangeVO::getCityName, Collectors.summingInt(e -> 1)));
            statusCityCountMap.put(currentStatus, cityCountMap);
            index++;
        }

        // 获取所有城市列表
        Set<String> allCities = statusCityCountMap.values().stream().flatMap(map -> map.keySet().stream())
                .collect(Collectors.toSet());

        // 组装结果
        return allCities.stream()
                .map(cityName -> new ProjectDataVO(cityName,
                        getCount(statusCityCountMap, BuildingStatusChangeLogEntity.BizStatus.WAIT_NEGOTIATION, cityName),
                        getCount(statusCityCountMap, BuildingStatusChangeLogEntity.BizStatus.PRELIMINARY_NEGOTIATION, cityName),
                        getCount(statusCityCountMap, BuildingStatusChangeLogEntity.BizStatus.INTENTION_ACCOMPLISHED, cityName),
                        getCount(statusCityCountMap, BuildingStatusChangeLogEntity.BizStatus.QUOTATION, cityName),
                        getCount(statusCityCountMap, BuildingStatusChangeLogEntity.BizStatus.DEAL, cityName)))
                .collect(Collectors.toList());
    }


    /**
     * 项目状态流转顺序
     */
    private static final List<String> PROJECT_STATUS_FLOW = Arrays.asList(BuildingStatusChangeLogEntity.BizStatus.WAIT_NEGOTIATION.getCode(),      // 待洽谈
            BuildingStatusChangeLogEntity.BizStatus.PRELIMINARY_NEGOTIATION.getCode(),// 洽谈中
            BuildingStatusChangeLogEntity.BizStatus.INTENTION_ACCOMPLISHED.getCode(), // 达成意向
            BuildingStatusChangeLogEntity.BizStatus.QUOTATION.getCode(),             // 方案报价
            BuildingStatusChangeLogEntity.BizStatus.DEAL.getCode()                   // 成交
    );

    private StatusChangeVO buildQueryParam(String type, Integer subType, String status, LocalDateTime startDate, LocalDateTime endDate, Set<Integer> bizIds) {
        return new StatusChangeVO().setType(type).setSubType(subType).setStatus(status).setChangeTimeStart(startDate)
                .setChangeTimeEnd(endDate)
                .setBizIds(bizIds);
    }

    /**
     * 获取上一阶段的业务IDs
     */
    private Set<Integer> getPreviousBizIds(Map<String, List<StatusChangeVO>> statusChangeMap, String currentStatus) {
        int currentIndex = PROJECT_STATUS_FLOW.indexOf(currentStatus);
        if (currentIndex == 0) {
            return null; // 第一个阶段不需要前置ID
        }

        String previousStatus = PROJECT_STATUS_FLOW.get(currentIndex - 1);
        List<StatusChangeVO> previousStatusList = statusChangeMap.get(previousStatus);

        return previousStatusList == null ? Collections.emptySet() : previousStatusList.stream()
                .map(StatusChangeVO::getBizId).collect(Collectors.toSet());
    }

    /**
     * 安全获取计数
     */
    private int getCount(Map<String, Map<String, Integer>> statusCityCountMap, BuildingStatusChangeLogEntity.BizStatus status, String cityName) {
        return statusCityCountMap.getOrDefault(status.getCode(), Collections.emptyMap())
                .getOrDefault(cityName, 0);
    }

    @Override
    public CityStatisticsItemVO getBuildingStatisticsRatioInfo(Integer cityId) {
        // 获取用户的城市权限
        DataAccessVO userDataAccess = getUserDataAccess();
        if (userDataAccess == null) {
            throw new IllegalArgumentException("获取用户城市权限为空");
        }
        List<DataAccessCityParam> cityList = userDataAccess.getCityList();
        if (CollectionUtils.isEmpty(cityList)) {
            throw new IllegalArgumentException("获取用户城市权限为空");
        }
        // cityId -> cityName
        Map<Integer, String> cityMap = cityList.stream()
                .collect(Collectors.toMap(DataAccessCityParam::getId, DataAccessCityParam::getCityName));
        if (cityId != 0 && !cityMap.containsKey(cityId)) {
            throw new IllegalArgumentException("此用户无此城市权限！");
        }

        String cityName = cityMap.get(cityId);
        if (cityId != 0) {
            // 获取缓存数据
            String cacheKey = String.format(CACHE_KEY_FORMAT, cityName);
            String cacheValue = stringRedisTemplate.opsForValue().get(cacheKey);
            if (StringUtils.isNotBlank(cacheValue)) {
                CityStatisticsVO cityStatisticsVO = JSON.parseObject(cacheValue, CityStatisticsVO.class);
                CityStatisticsItemVO itemVO = convertToItemVo(cityStatisticsVO, cityName);
                return itemVO;
            } else {
                log.warn("缓存数据为空，cityId={}, cityName={}", cityId, cityName);
                // 创建汇总统计对象
                CityStatisticsVO emptyStatistics = getEmptyCityStatisticsVO(cityName);
                CityStatisticsItemVO itemVO = convertToItemVo(emptyStatistics, cityName);
                return itemVO;
            }
        }

        if (cityId == 0) {
            cityName = "全部";

            // 创建汇总统计对象
            CityStatisticsVO totalStatistics = getEmptyCityStatisticsVO(cityName);

            // 遍历所有城市数据并累加
            cityMap.forEach((id, name) -> {
                String cacheKey = String.format(CACHE_KEY_FORMAT, name);
                String cacheValue = stringRedisTemplate.opsForValue().get(cacheKey);
                if (StringUtils.isNotBlank(cacheValue)) {
                    CityStatisticsVO cityStatistics = JSON.parseObject(cacheValue, CityStatisticsVO.class);
                    mergeCityStatistics(totalStatistics, cityStatistics);
                }
            });
            CityStatisticsItemVO itemVO = convertToItemVo(totalStatistics, cityName);

            return itemVO;
        }
        return null;

    }

    private static CityStatisticsVO getEmptyCityStatisticsVO(String cityName) {
        CityStatisticsVO totalStatistics = new CityStatisticsVO();
        totalStatistics.setThreeDaysBuildingData(new BuildingDataVO(cityName, 0, 0, 0));
        totalStatistics.setSevenDaysBuildingData(new BuildingDataVO(cityName, 0, 0, 0));
        totalStatistics.setSevenDaysProjectData(new ProjectDataVO(cityName, 0, 0, 0, 0, 0));
        totalStatistics.setFourteenDaysProjectData(new ProjectDataVO(cityName, 0, 0, 0, 0, 0));
        return totalStatistics;
    }


    private CityStatisticsItemVO convertToItemVo(CityStatisticsVO cityStatisticsVO, String cityName) {
        if (cityStatisticsVO == null) {
            return null;
        }

        CityStatisticsItemVO itemVO = new CityStatisticsItemVO();
        itemVO.setCityName(cityStatisticsVO.getCityName());

        // 转换楼宇数据
        itemVO.setThreeDaysBuildingData(convertBuildingData(cityStatisticsVO.getThreeDaysBuildingData(), cityName));
        itemVO.setSevenDaysBuildingData(convertBuildingData(cityStatisticsVO.getSevenDaysBuildingData(), cityName));

        // 转换项目数据
        itemVO.setSevenDaysProjectData(convertProjectData(cityStatisticsVO.getSevenDaysProjectData(), cityName));
        itemVO.setFourteenDaysProjectData(convertProjectData(cityStatisticsVO.getFourteenDaysProjectData(), cityName));

        return itemVO;
    }

    private List<DataItemVO> convertBuildingData(BuildingDataVO buildingData, String cityName) {
        if (buildingData == null) {
            buildingData = new BuildingDataVO(cityName, 0, 0, 0);
        }
        // 计算转化率
        BigDecimal approvedRate = BigDecimal.ZERO;
        if (buildingData.getWaitApprovedCount() != 0) {
            approvedRate = calculateRate(buildingData.getApprovedCount(), buildingData.getWaitApprovedCount());
        }
        return Arrays.asList(new DataItemVO("评级申请", buildingData.getWaitApprovedCount(), null), new DataItemVO("认证通过", buildingData.getApprovedCount(), approvedRate));
    }

    private List<DataItemVO> convertProjectData(ProjectDataVO projectData, String cityName) {
        if (projectData == null) {
            projectData = new ProjectDataVO(cityName, 0, 0, 0, 0, 0);
        }
        // 抽取计算转化率的逻辑为独立方法
        BigDecimal negotiatingRate = calculateRate(projectData.getNegotiatingCount(), projectData.getCreatedCount());
        BigDecimal intentionRate = calculateRate(projectData.getIntentionCount(), projectData.getNegotiatingCount());
        BigDecimal quotaRate = calculateRate(projectData.getQuotaCount(), projectData.getIntentionCount());
        BigDecimal dealRate = calculateRate(projectData.getDealCount(), projectData.getQuotaCount());

        return Arrays.asList(new DataItemVO("新建项目(商机)", projectData.getCreatedCount(), null), new DataItemVO("洽谈中", projectData.getNegotiatingCount(), negotiatingRate), new DataItemVO("达成意向", projectData.getIntentionCount(), intentionRate), new DataItemVO("方案报价", projectData.getQuotaCount(), quotaRate), new DataItemVO("成交", projectData.getDealCount(), dealRate));
    }

    private BigDecimal calculateRate(Integer numerator, Integer denominator) {
        if (denominator == null || numerator == null || denominator == 0) {
            return null;
        }
        return BigDecimal.valueOf(numerator)
                .divide(BigDecimal.valueOf(denominator), 3, RoundingMode.HALF_UP)  // 保留3位小数
                .multiply(BigDecimal.valueOf(100))
                .setScale(1, RoundingMode.HALF_UP);
    }


    /**
     * 合并城市统计数据
     */
    private void mergeCityStatistics(CityStatisticsVO total, CityStatisticsVO current) {
        if (current == null) {
            return;
        }

        // 使用Optional避免NPE
        Optional.ofNullable(current.getThreeDaysBuildingData())
                .ifPresent(data -> mergeBuildingData(total.getThreeDaysBuildingData(), data));

        Optional.ofNullable(current.getSevenDaysBuildingData())
                .ifPresent(data -> mergeBuildingData(total.getSevenDaysBuildingData(), data));

        Optional.ofNullable(current.getSevenDaysProjectData())
                .ifPresent(data -> mergeProjectData(total.getSevenDaysProjectData(), data));

        Optional.ofNullable(current.getFourteenDaysProjectData())
                .ifPresent(data -> mergeProjectData(total.getFourteenDaysProjectData(), data));
    }

    private void mergeBuildingData(BuildingDataVO total, BuildingDataVO current) {
        total.setWaitApprovedCount(total.getWaitApprovedCount() + current.getWaitApprovedCount());
        total.setApprovedCount(total.getApprovedCount() + current.getApprovedCount());
    }

    private void mergeProjectData(ProjectDataVO total, ProjectDataVO current) {
        total.setCreatedCount(total.getCreatedCount() + current.getCreatedCount());
        total.setNegotiatingCount(total.getNegotiatingCount() + current.getNegotiatingCount());
        total.setIntentionCount(total.getIntentionCount() + current.getIntentionCount());
        total.setQuotaCount(total.getQuotaCount() + current.getQuotaCount());
        total.setDealCount(total.getDealCount() + current.getDealCount());
    }

    /**
     * @param cityId 城市ID
     * @param date   筛选日期
     * @Author：TanJie
     * @Date：2025-01-17 15:37
     * @Description：获取设备统计数据
     */
    @Override
    public KanbanDeviceStatisticsVO getDeviceStatisticsInfo(Integer cityId, LocalDate date) {
        // 获取用户城市列表权限
        DataAccessVO dataAccessVO = getUserDataAccess();
        if (dataAccessVO == null || dataAccessVO.getCityList().isEmpty()) {
            throw new ServerException(ErrorCode.NO_PERMISSION);
        }
        Map<Integer, String> cityIdNameMap = dataAccessVO.getCityList().stream()
                .collect(Collectors.toMap(DataAccessCityParam::getId, DataAccessCityParam::getCityName));
        if (cityId != 0 && !cityIdNameMap.containsKey(cityId)) {
            throw new ServerException(ErrorCode.NO_PERMISSION);
        }

        // 获取设备统计数据
        List<Integer> cityIdList;
        String accessType;
        if (cityId == 0) {
            cityIdList = cityIdNameMap.keySet().stream().toList();
            accessType = dataAccessVO.getAccessType();
        } else {
            cityIdList = List.of(cityId);
            accessType = "";
        }
        String wno = SecurityUser.getUserCode();
        ResultTemplate<KanbanDeviceStatisticsVO> result = feignCmsRpc.getDeviceStatisticsData(wno, accessType, cityIdList, date);
        return result.getData();
    }
}
