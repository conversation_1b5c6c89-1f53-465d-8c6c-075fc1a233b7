package com.coocaa.meht.module.web.service.property.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.common.PageResult;
import com.coocaa.meht.common.bean.CodeNameVO;
import com.coocaa.meht.common.bean.ResultTemplate;
import com.coocaa.meht.common.exception.ServerException;
import com.coocaa.meht.module.web.dao.BuildingPropertyCompanyMapper;
import com.coocaa.meht.module.web.dao.PropertyCompanyMapper;
import com.coocaa.meht.module.web.dao.PropertyCompanyPersonMapper;
import com.coocaa.meht.module.web.dto.convert.PropertyCompanyConvert;
import com.coocaa.meht.module.web.dto.convert.PropertyCompanyPersonConvert;
import com.coocaa.meht.module.web.dto.property.PropertyCompanyParam;
import com.coocaa.meht.module.web.entity.BuildingPropertyCompanyEntity;
import com.coocaa.meht.module.web.entity.PropertyCompanyEntity;
import com.coocaa.meht.module.web.entity.PropertyCompanyPersonEntity;
import com.coocaa.meht.module.web.enums.BooleFlagEnum;
import com.coocaa.meht.module.web.enums.PropertyCompanyTypeEnum;
import com.coocaa.meht.module.web.service.property.IPropertyCompanyService;
import com.coocaa.meht.module.web.vo.BuildingPropertyCompanyVO;
import com.coocaa.meht.module.web.vo.CompanyInfoVO;
import com.coocaa.meht.module.web.vo.property.PropertyCompanyVO;
import com.coocaa.meht.rpc.FeignAuthorityRpc;
import com.coocaa.meht.utils.AesUtils;
import com.coocaa.meht.utils.HttpUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025-01-03
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class PropertyCompanyServiceImpl extends ServiceImpl<PropertyCompanyMapper, PropertyCompanyEntity> implements IPropertyCompanyService {

    @Value("${tianyancha.api.endpoint:http://open.api.tianyancha.com}")
    private String tianyanchaApiEndpoint;

    @Value("${tianyancha.api.token:0ff1a6293924934354ea16764dec8c342c2fdb9f51f6c42423c16078540b29c7523c61b666dd877c40ecc5de00136b36}")
    private String tianyanchaApiToken;

    private final PropertyCompanyPersonMapper personMapper;
    private final BuildingPropertyCompanyMapper buildingCompanyMapper;
    private final FeignAuthorityRpc authorityRpc;

    @Override
    public Integer saveCompany(PropertyCompanyParam param) {
        // 验证类型数据，如果类型为1，则需要验证统一社会信用代码
        if (param.getType().equals(PropertyCompanyTypeEnum.ENTERPRISE.getCode())) {
            if (StringUtils.isBlank(param.getUnifiedSocialCreditCode())) {
                throw new ServerException("企业类型必须填写统一社会信用代码");
            }
            // 企业重复性校验
            long count = this.count(new LambdaQueryWrapper<PropertyCompanyEntity>()
                    .eq(PropertyCompanyEntity::getUnifiedSocialCreditCode, AesUtils.encryptHex(param.getUnifiedSocialCreditCode())));
            if (count > 0) {
                throw new ServerException("企业已存在");
            }
        } else {
            // 类型为2，验证手机号
            if (StringUtils.isBlank(param.getPhone()) || param.getPhone().length() > 11) {
                throw new ServerException("请填写正确的手机号");
            }
            // 验证身份证号
            if (StringUtils.isNotBlank(param.getIdCard()) && param.getIdCard().length() > 18) {
                throw new ServerException("身份证号必须在1-18之间");
            }
            // 个人手机号重复校验
            long count = this.count(new LambdaQueryWrapper<PropertyCompanyEntity>()
                    .eq(PropertyCompanyEntity::getPhone, AesUtils.encryptHex(param.getPhone())));
            if (count > 0) {
                throw new ServerException("手机号已存在");
            }
        }
        // 入库
        PropertyCompanyEntity companyEntity = PropertyCompanyConvert.INSTANCE.toEntity(param);
        this.saveOrUpdate(companyEntity);
        return companyEntity.getId();
    }

    @Override
    public List<PropertyCompanyVO> getCompany(String name) {
        List<PropertyCompanyEntity> entities = this.list(new LambdaQueryWrapper<PropertyCompanyEntity>()
                .eq(PropertyCompanyEntity::getStatus, BooleFlagEnum.YES.getCode())
                .like(StringUtils.isNotBlank(name), PropertyCompanyEntity::getName, name));
        List<PropertyCompanyVO> companyVOS = PropertyCompanyConvert.INSTANCE.toVOs(entities);
        if (CollectionUtils.isEmpty(companyVOS)) {
            return Collections.emptyList();
        }
        // 物业联系人
        Map<Integer, List<PropertyCompanyPersonEntity>> personMap = personMapper.selectList(new LambdaQueryWrapper<PropertyCompanyPersonEntity>()
                        .in(PropertyCompanyPersonEntity::getCompanyId, companyVOS.stream().map(PropertyCompanyVO::getId).collect(Collectors.toSet())))
                .stream().collect(Collectors.groupingBy(PropertyCompanyPersonEntity::getCompanyId));
        // 组装
        companyVOS.forEach(VO -> {
            VO.setPersonVOS(PropertyCompanyPersonConvert.INSTANCE.toVOs(personMap.getOrDefault(VO.getId(), Collections.emptyList())));
        });
        return companyVOS;
    }

    @Override
    public PageResult<CompanyInfoVO> getCompanyInfoList(String word, Integer pageNum, Integer pageSize) {
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json; charset=utf-8");
        header.put("Connection", "keep-alive");
        header.put("Authorization", AesUtils.decryptStr(tianyanchaApiToken));
        String apiUrl = String.format("%s/services/open/search/2.0?word=%s&pageSize=%s&pageNum=%s",
                tianyanchaApiEndpoint, URLEncoder.encode(word, Charset.defaultCharset()), pageSize, pageNum);

        String response = HttpUtils.get(apiUrl, header);
        JSONObject json = JSON.parseObject(response);
        if (Objects.isNull(json) || !Objects.equals(json.get("reason"), "ok")) {
            log.info("通过天眼查: [{}]失败，原因: {}", word, json.get("reason"));
            return new PageResult<>(Collections.emptyList(), 0);
        }

        JSONObject data = json.getJSONObject("result");
        List<CompanyInfoVO> companies = JSON.parseArray(JSON.toJSONString(data.get("items")), CompanyInfoVO.class);
        return new PageResult<>(companies, Long.parseLong(data.get("total").toString()));
    }

    @Override
    public PropertyCompanyVO getPerson(String projectCode) {
        // 商机物业关联
        List<BuildingPropertyCompanyEntity> buildingCompanies = buildingCompanyMapper.selectList(new LambdaQueryWrapper<BuildingPropertyCompanyEntity>()
                .eq(BuildingPropertyCompanyEntity::getProjectCode, projectCode));
        if (CollectionUtils.isEmpty(buildingCompanies)) {
            return null;
        }
        // 物业公司
        PropertyCompanyEntity companyEntity = this.getById(buildingCompanies.get(0).getPropertyId());
        PropertyCompanyVO companyVO = PropertyCompanyConvert.INSTANCE.toVO(companyEntity);
        // 物业公司联系人
        List<PropertyCompanyPersonEntity> personEntities = personMapper.selectList(new LambdaQueryWrapper<PropertyCompanyPersonEntity>()
                .eq(PropertyCompanyPersonEntity::getCompanyId, companyEntity.getId()));
        companyVO.setPersonVOS(PropertyCompanyPersonConvert.INSTANCE.toVOs(personEntities));
        return companyVO;
    }

    @Override
    public List<CodeNameVO> getSecondIndustry() {
        return Optional.ofNullable(authorityRpc.getSecondIndustry()).map(ResultTemplate::getData)
                .orElse(Collections.emptyList());
    }

}
