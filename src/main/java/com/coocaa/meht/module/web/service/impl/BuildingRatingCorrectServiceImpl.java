package com.coocaa.meht.module.web.service.impl;

import cn.hutool.core.date.DatePattern;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.coocaa.meht.common.exception.CommonException;
import com.coocaa.meht.cos.ObjectUtils;
import com.coocaa.meht.module.api.ark.DouAiService;
import com.coocaa.meht.module.web.entity.BuildingDetailsEntity;
import com.coocaa.meht.module.web.entity.BuildingMetaEntity;
import com.coocaa.meht.module.web.entity.BuildingParameterEntity;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.entity.CityCoefficientEntity;
import com.coocaa.meht.module.web.enums.BooleFlagEnum;
import com.coocaa.meht.module.web.service.BuildingDetailsService;
import com.coocaa.meht.module.web.service.BuildingParameterService;
import com.coocaa.meht.module.web.service.BuildingRatingService;
import com.coocaa.meht.module.web.service.CityCoefficientService;
import com.coocaa.meht.module.web.service.IBuildingMetaService;
import com.coocaa.meht.module.web.service.IBuildingRatingCorrectService;
import com.coocaa.meht.utils.RsaExample;
import com.coocaa.meht.utils.TransactionUtils;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeMap;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 楼宇评级修正服务实现
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-27 13:46
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class BuildingRatingCorrectServiceImpl implements IBuildingRatingCorrectService {
    private final BuildingRatingService buildingRatingService;
    private final IBuildingMetaService buildingMetaService;
    private final BuildingParameterService buildingParameterService;
    private final BuildingRatingServiceImpl buildingRatingServiceImpl;
    private final BuildingDetailsService buildingDetailsService;
    private final CityCoefficientService cityCoefficientService;
    private final DouAiService douAiService;
    private final TransactionUtils transactionUtils;
    private final RsaExample rsaExample;

    @Value("${parameter.max.dataFlag}")
    private Integer dataFlag;

    private static final List<List<String>> DEFAULT_EXPORT_HEADERS = Stream.of(
            "楼宇申请编码", "认证楼字类型", "地图楼宇名称", "省份", "城市", "区(县)", "详细地址").map(List::of).toList();

    private final AtomicReference<Map<Long, BuildingParameterEntity>> parameterEntityMapHolder = new AtomicReference<>(Map.of());

    /**
     * 重新生成AI评分过程数据
     */
    @Override
    public Boolean aiScoreDetail(List<String> buildingNos) {
        log.info("重新生成AI评分过程数据...");
        // 获取所有没有ai评分的记录
        List<BuildingRatingEntity> buildingRatingEntities = buildingRatingService.lambdaQuery()
                .in(CollectionUtils.isNotEmpty(buildingNos), BuildingRatingEntity::getBuildingNo, buildingNos)
                .eq(BuildingRatingEntity::getBuildingDesc, "楼宇数据同步,没有评级信息")
                .eq(BuildingRatingEntity::getDeleted, BooleFlagEnum.NO.getCode())
                .select(BuildingRatingEntity::getId, BuildingRatingEntity::getBuildingNo, BuildingRatingEntity::getMapNo,
                        BuildingRatingEntity::getBuildingType, BuildingRatingEntity::getBuildingName,
                        BuildingRatingEntity::getMapAdCode, BuildingRatingEntity::getMapCity,
                        BuildingRatingEntity::getProjectAiLevel, BuildingRatingEntity::getBuildingAiScore)
                .list();
        if (CollectionUtils.isEmpty(buildingRatingEntities)) {
            return true;
        }

        // 获取所有评分参数
        Map<Integer, Map<String, List<BuildingParameterEntity>>> parameterByTypeMap = buildingParameterService.lambdaQuery()
                .ne(BuildingParameterEntity::getParentId, 0)
                .list()
                .stream()
                .collect(Collectors.groupingBy(BuildingParameterEntity::getBuildingType,
                        Collectors.groupingBy(BuildingParameterEntity::getParameterCode)));

        List<BuildingDetailsEntity> detailsList = new ArrayList<>(buildingRatingEntities.size());
        int i = 1;
        for (BuildingRatingEntity buildingRatingEntity : buildingRatingEntities) {
            log.info("[{}/{}]开始处理[{}]楼宇的ai评分", i++, buildingRatingEntities.size(), buildingRatingEntity.getBuildingNo());
            try {
                Map<String, List<BuildingParameterEntity>> parameterByCodeMap = parameterByTypeMap.get(buildingRatingEntity.getBuildingType());
                if (MapUtils.isEmpty(parameterByCodeMap)) {
                    continue;
                }

                // AI评分过程数据
                BuildingDetailsEntity detailsEntity = calculateAiScore(buildingRatingEntity);
                if (Objects.nonNull(detailsEntity)) {
                    detailsList.add(detailsEntity.setBuildingNo(buildingRatingEntity.getBuildingNo()));
                }
            } catch (Exception e) {
                log.error("处理[{}]楼宇的ai评分报错", buildingRatingEntity.getBuildingNo(), e);
            }
        }
        if (CollectionUtils.isEmpty(detailsList)) {
            return true;
        }

        return transactionUtils.doBatch(List.of(
                // 移除无效的过程数据
                () -> {
                    buildingDetailsService.lambdaUpdate()
                            .set(BuildingDetailsEntity::getDeleted, BooleFlagEnum.YES.getCode())
                            .in(BuildingDetailsEntity::getBuildingNo,
                                    detailsList.stream().map(BuildingDetailsEntity::getBuildingNo).collect(Collectors.toSet()))
                            .eq(BuildingDetailsEntity::getDeleted, BooleFlagEnum.NO.getCode())
                            .update();
                    return true;
                },
                // 保存过程数据
                () -> buildingDetailsService.saveBatch(detailsList)
        ), () -> "保存AI评分的过程数据失败");
    }

    /**
     * AI评分计算
     */
    private BuildingDetailsEntity calculateAiScore(BuildingRatingEntity buildingRatingEntity) {
        // 获取AI评估数据
        Map<String, Object> aiData = douAiService.getBuildingAppraiser(
                String.format("%s%s", buildingRatingEntity.getMapCity(), buildingRatingEntity.getBuildingName()));
        if (MapUtils.isEmpty(aiData)) {
            return null;
        }
        // 处理AI数据
        BuildingDetailsEntity details = buildingRatingServiceImpl.buildDetailsFromAiData(aiData);
        // 计算AI评分
        details = buildingRatingServiceImpl.processThirdPartyData(details, buildingRatingEntity.getBuildingType(), buildingRatingEntity.getMapAdCode() , dataFlag);

        // third_building_exterior字段处理：仅保留50的长度
        details.setThirdBuildingExterior(StringUtils.substring(details.getThirdBuildingExterior(), 0, 50));

        // 返回评分过程数据
        return details;
    }

    /**
     * 根据已有的过程详情数据再次计算AI单项分
     *
     * @param buildingNos 楼宇编码
     */
    @Override
    public Boolean aiScoreDetailRecalculate(List<String> buildingNos, Integer batch) {
        // 查询所有AI总分为0的记录
        Map<String, BuildingRatingEntity> ratingEntityMap = buildingRatingService.lambdaQuery()
                .in(CollectionUtils.isNotEmpty(buildingNos), BuildingRatingEntity::getBuildingNo, buildingNos)
                .eq(BuildingRatingEntity::getBuildingAiScore, BigDecimal.ZERO)
                .eq(BuildingRatingEntity::getDeleted, BooleFlagEnum.NO.getCode())
                .select(BuildingRatingEntity::getBuildingNo, BuildingRatingEntity::getBuildingType, BuildingRatingEntity::getMapAdCode)
                .list().stream()
                .collect(Collectors.toMap(BuildingRatingEntity::getBuildingNo, e -> e));
        // 每次处理batch个数据
        int ct = 0;
        for (List<String> buildingNoList : Lists.partition(ratingEntityMap.keySet().stream().toList(), batch)) {
            log.info("开始处理[{}-{}]/[{}]", ct + 1, ct + buildingNoList.size(), ratingEntityMap.size());
            ct += buildingNoList.size();
            // 更新过程数据
            boolean detailRet = aiScoreDetailModify(buildingNoList, ratingEntityMap);
            // 打印出未写入成功的buildingNo列表
            if (!detailRet) {
                log.error("下列buildingNo的过程数据未更新成功：\n{}", buildingNoList);
                continue;
            }
            // 更新AI总分
            boolean scoreRet = aiScoreUpdate(buildingNoList);
            if (!scoreRet) {
                log.error("下列buildingNod的总分数据未更新成功：\n{}", buildingNoList);
            }
        }
        return true;
    }

    /**
     * 更新过程数据
     */
    public boolean aiScoreDetailModify(List<String> buildingBatchNos, Map<String, BuildingRatingEntity> ratingEntityMap) {
        if (CollectionUtils.isEmpty(buildingBatchNos)) {
            return true;
        }
        // 查询过程记录
        List<BuildingDetailsEntity> detailsList = buildingDetailsService.lambdaQuery()
                .in(BuildingDetailsEntity::getBuildingNo, buildingBatchNos)
                .eq(BuildingDetailsEntity::getDeleted, BooleFlagEnum.NO.getCode())
                .list();
        if (CollectionUtils.isEmpty(detailsList)) {
            return true;
        }
        //
        List<BuildingDetailsEntity> updateEntities = Lists.newArrayListWithExpectedSize(detailsList.size());
        detailsList.forEach(detail -> {
            BuildingRatingEntity buildingRatingEntity = ratingEntityMap.get(detail.getBuildingNo());
            if (Objects.isNull(buildingRatingEntity)) {
                return;
            }
            // 计算AI评分
            buildingRatingServiceImpl.processThirdPartyData(detail,
                    buildingRatingEntity.getBuildingType(), buildingRatingEntity.getMapAdCode() , dataFlag);
            // 记录更新字段
            updateEntities.add(new BuildingDetailsEntity()
                    .setId(detail.getId())
                    .setThirdBuildingGradeId(detail.getThirdBuildingGradeId())
                    .setThirdBuildingLocationId(detail.getThirdBuildingLocationId())
                    .setThirdBuildingNumberId(detail.getThirdBuildingNumberId())
                    .setThirdBuildingPriceId(detail.getThirdBuildingPriceId())
                    .setThirdBuildingAgeId(detail.getThirdBuildingAgeId())
                    .setThirdBuildingExteriorId(detail.getThirdBuildingExteriorId())
                    .setThirdBuildingLobbyId(detail.getThirdBuildingLobbyId())
                    .setThirdBuildingGarageId(detail.getThirdBuildingGarageId())
                    .setThirdBuildingHallId(detail.getThirdBuildingHallId())
                    .setThirdBuildingBrandId(detail.getThirdBuildingBrandId())
                    .setThirdBuildingRatingId(detail.getThirdBuildingRatingId())
                    .setThirdBuildingSettledId(detail.getThirdBuildingSettledId()));
        });

        // 更新过程数据
        return CollectionUtils.isEmpty(updateEntities) || buildingDetailsService.updateBatchById(updateEntities);
    }

    /**
     * 根据已有AI评分项，重新计算并更新AI评分
     */
    @Override
    public Boolean aiScoreUpdate(List<String> buildingNos) {
        log.info("根据已有AI评分项，重新计算并更新AI评分...");
        // 或取所有的评分过程数据
        List<BuildingDetailsEntity> detailsList = buildingDetailsService.lambdaQuery()
                .in(CollectionUtils.isNotEmpty(buildingNos), BuildingDetailsEntity::getBuildingNo, buildingNos)
                .eq(BuildingDetailsEntity::getDeleted, BooleFlagEnum.NO.getCode())
                .list();
        if (CollectionUtils.isEmpty(detailsList)) {
            log.info("没有需要重新计算的ai评分");
            return true;
        }

        // 查询楼宇信息
        if (CollectionUtils.isEmpty(buildingNos)) {
            buildingNos = detailsList.stream().map(BuildingDetailsEntity::getBuildingNo).distinct().collect(Collectors.toList());
        }
        List<BuildingRatingEntity> ratingEntities = buildingRatingService.lambdaQuery()
                .in(BuildingRatingEntity::getBuildingNo, buildingNos)
                .eq(BuildingRatingEntity::getDeleted, BooleFlagEnum.NO.getCode())
                .list();
        Map<String, Long> metaByNoMap = buildingMetaService.lambdaQuery()
                .in(BuildingMetaEntity::getBuildingRatingNo, buildingNos)
                .list().stream()
                .collect(Collectors.toMap(BuildingMetaEntity::getBuildingRatingNo, BuildingMetaEntity::getId));

        // 重新计算AI评分
        List<BuildingRatingEntity> ratingUpdates = new ArrayList<>(detailsList.size());
        List<BuildingMetaEntity> metaUpdates = new ArrayList<>(detailsList.size());
        getUpdateList(detailsList, ratingEntities, metaByNoMap, ratingUpdates, metaUpdates);
        if (CollectionUtils.isEmpty(ratingUpdates)) {
            log.info("没有需要重新计算的ai评分的rating记录");
            return true;
        }
        return transactionUtils.doBatch(List.of(
                // 修改rating数据
                () -> buildingRatingService.updateBatchById(ratingUpdates),
                // 修改meta数据
                () -> CollectionUtils.isEmpty(metaUpdates) || buildingMetaService.updateBatchById(metaUpdates)
        ), () -> "更新AI评分失败");
    }

    /**
     * 获取待更新的数据
     */
    private void getUpdateList(List<BuildingDetailsEntity> detailsList,
                               List<BuildingRatingEntity> ratingEntities, Map<String, Long> metaByNoMap,
                               List<BuildingRatingEntity> ratingUpdates, List<BuildingMetaEntity> metaUpdates) {
        Map<String, BuildingRatingEntity> ratingByNoMap = ratingEntities.stream()
                .collect(Collectors.toMap(BuildingRatingEntity::getBuildingNo, e -> e));
        // 获取城市系数
        Set<String> mapAdCodes = ratingEntities.stream().map(BuildingRatingEntity::getMapAdCode).collect(Collectors.toSet());
        Map<String, BigDecimal> coefficientMap = CollectionUtils.isEmpty(mapAdCodes)
                ? Map.of()
                : cityCoefficientService.lambdaQuery()
                .in(CityCoefficientEntity::getAdCode, mapAdCodes)
                .list().stream()
                .collect(Collectors.toMap(e -> String.valueOf(e.getAdCode()), CityCoefficientEntity::getCoefficient, (v1, v2) -> v1));
        for (BuildingDetailsEntity detailsEntity : detailsList) {
            try {
                BuildingRatingEntity buildingRatingEntity = ratingByNoMap.get(detailsEntity.getBuildingNo());
                if (Objects.isNull(buildingRatingEntity)) {
                    continue;
                }
                // 获取城市系数
                BigDecimal coefficient = coefficientMap.getOrDefault(buildingRatingEntity.getMapAdCode(), BigDecimal.ONE);
                // 计算AI总分
                BigDecimal aiScore = buildingRatingServiceImpl.calculateAIScoreByBuildingType(detailsEntity,
                        buildingRatingEntity.getMapAdCode(), buildingRatingEntity.getBuildingType(), coefficient);
                // 计算AI评级
                String aiProjectLevel = buildingRatingServiceImpl.calculateProjectLevel2(aiScore);
                // 记录ai评分 - rating
                ratingUpdates.add(new BuildingRatingEntity()
                        .setId(buildingRatingEntity.getId())
                        .setBuildingNo(detailsEntity.getBuildingNo())
                        .setProjectAiLevel(aiProjectLevel)
                        .setBuildingAiScore(aiScore));
                // 记录ai评分 - meta
                BuildingMetaEntity metaEntity = new BuildingMetaEntity();
                metaEntity.setId(metaByNoMap.get(detailsEntity.getBuildingNo()));
                if (Objects.nonNull(metaEntity.getId())) {
                    metaEntity.setBuildingRatingNo(detailsEntity.getBuildingNo());
                    metaEntity.setBuildingAiScore(aiScore);
                    metaEntity.setProjectLevelAi(aiProjectLevel);
                    metaUpdates.add(metaEntity);
                }
            } catch (Exception e) {
                log.error("重新计算[{}]的ai评分报错", detailsEntity.getBuildingNo(), e);
                throw new CommonException("重新计算[%s]的ai评分报错，详情：%s".formatted(detailsEntity.getBuildingNo(), e.getMessage()));
            }
        }
    }

    /**
     * 按城市导出AI评分详情信息
     *
     * @param cityNames 城市名称列表
     * @return 文件URL
     */
    @Override
    public String aiScoreExport(List<String> cityNames) {
        log.info("按城市导出AI评分详情信息...");
        // 获取文件对象
        File tempFile = getAiScoreUploadFile(cityNames);

        // 文件上传到cos，返回文件url
        return uploadToCloud(tempFile, "楼宇AI评分详情");
    }

    /**
     * 将文件上传到cos
     */
    private String uploadToCloud(File tempFile, String fileNameFlag) {
        String fileName = String.format("%s_%s.xlsx", fileNameFlag, LocalDateTime.now()
                .format(DateTimeFormatter.ofPattern(DatePattern.PURE_DATETIME_MS_PATTERN)));
        String feature = "meht";
        ObjectUtils.uploadFile(ObjectUtils.getCosFileName(feature, fileName), tempFile);
        try {
            if (!tempFile.delete()) {
                log.error("删除临时文件[{}]失败", tempFile.getAbsolutePath());
            }
        } catch (Exception e) {
            log.error("删除临时文件[{}]失败", tempFile.getAbsolutePath(), e);
        }
        return ObjectUtils.getAccessUrl(feature, fileName);
    }

    /**
     * 获取AI评分上传文件
     *
     * @param cityNames 城市名称列表
     * @return 文件对象
     */
    @Override
    public File getAiScoreUploadFile(List<String> cityNames) {
        // 获取所有城市下的楼宇数据
        Map<Integer, List<BuildingRatingEntity>> ratingMap = buildingRatingService.lambdaQuery()
                .in(CollectionUtils.isNotEmpty(cityNames), BuildingRatingEntity::getMapCity, cityNames)
                .eq(BuildingRatingEntity::getDeleted, BooleFlagEnum.NO.getCode())
                .list().stream()
                .collect(Collectors.groupingBy(BuildingRatingEntity::getBuildingType, TreeMap::new, Collectors.toList()));
        if (MapUtils.isEmpty(ratingMap)) {
            log.info("没有需要导出的ai评分");
            return null;
        }
        //
        File tempFile;
        try {
            tempFile = File.createTempFile(UUID.randomUUID().toString(), ".xlsx");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        // 评分项获取
        parameterEntityMapHolder.set(buildingParameterService.lambdaQuery()
                .list().stream()
                .collect(Collectors.toMap(BuildingParameterEntity::getId, e -> e, (k1, k2) -> k2)));
        // 根据不同的楼宇类型生成不同的sheet页数据
        try (ExcelWriter excelWriter = EasyExcel.write(tempFile).build()) {
            ratingMap.forEach((buildingType, ratingList) -> {
                switch (buildingType) {
                    //  0 写字楼
                    case 0:
                        writeOfficeBuilding(ratingList, excelWriter);
                        break;
                    // 1 商住楼
                    case 1:
                        writeResidentialBuilding(ratingList, excelWriter);
                        break;
                    // 2 综合体
                    case 2:
                        writeComprehensiveBuilding(ratingList, excelWriter);
                        break;
                    // 3 产业园区
                    case 3:
                        writeIndustrialPark(ratingList, excelWriter);
                        break;
                }
            });
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return tempFile;
    }

    /**
     * 写字楼
     */
    private void writeOfficeBuilding(List<BuildingRatingEntity> ratingEntities, ExcelWriter excelWriter) {
        fillingSheetData(ratingEntities, excelWriter,
                // 组装申请评分头部数据
                () -> List.of(
                        List.of("写字楼等级"),
                        List.of("认证单项分"),
                        List.of("地理位置"),
                        List.of("认证单项分"),
                        List.of("楼层数"),
                        List.of("认证单项分"),
                        List.of("月租金"),
                        List.of("认证单项分"),
                        List.of("楼龄"),
                        List.of("认证单项分"),
                        List.of("外观造型"),
                        List.of("认证单项分"),
                        List.of("楼盘大堂"),
                        List.of("认证单项分"),
                        List.of("地下车库"),
                        List.of("认证单项分")
                ),
                // 组装AI评分头部数据
                () -> List.of(
                        List.of("AI写字楼等级"),
                        List.of("AI单项分"),
                        List.of("AI地理位置"),
                        List.of("AI单项分"),
                        List.of("AI楼层数"),
                        List.of("AI单项分"),
                        List.of("AI月租金"),
                        List.of("AI单项分"),
                        List.of("AI楼龄"),
                        List.of("AI单项分"),
                        List.of("AI外观造型"),
                        List.of("AI单项分"),
                        List.of("AI楼盘大堂"),
                        List.of("AI单项分"),
                        List.of("AI地下车库"),
                        List.of("AI单项分")
                ),
                // 填充认证数据
                (detailsEntity) -> Objects.isNull(detailsEntity)
                        ? Collections.nCopies(8, 0L)
                        : List.of(
                        detailsEntity.getBuildingGrade(),
                        detailsEntity.getBuildingLocation(),
                        detailsEntity.getBuildingNumber(),
                        detailsEntity.getBuildingPrice(),
                        detailsEntity.getBuildingAge(),
                        detailsEntity.getBuildingExterior(),
                        detailsEntity.getBuildingLobby(),
                        detailsEntity.getBuildingGarage()
                ),
                // 填充AI数据
                (detailsEntity) -> Objects.isNull(detailsEntity)
                        ? Collections.nCopies(16, 0L)
                        : List.of(
                        detailsEntity.getThirdBuildingGrade(),
                        detailsEntity.getThirdBuildingGradeId(),
                        detailsEntity.getThirdBuildingLocation(),
                        detailsEntity.getThirdBuildingLocationId(),
                        detailsEntity.getThirdBuildingNumber(),
                        detailsEntity.getThirdBuildingNumberId(),
                        detailsEntity.getThirdBuildingPrice(),
                        detailsEntity.getThirdBuildingPriceId(),
                        detailsEntity.getThirdBuildingAge(),
                        detailsEntity.getThirdBuildingAgeId(),
                        detailsEntity.getThirdBuildingExterior(),
                        detailsEntity.getThirdBuildingExteriorId(),
                        detailsEntity.getThirdBuildingLobby(),
                        detailsEntity.getThirdBuildingLobbyId(),
                        detailsEntity.getThirdBuildingGarage(),
                        detailsEntity.getThirdBuildingGarageId()
                ),
                () -> "写字楼");
    }


    /**
     * 商住楼
     */
    private void writeResidentialBuilding(List<BuildingRatingEntity> ratingEntities, ExcelWriter excelWriter) {
        fillingSheetData(ratingEntities, excelWriter,
                // 组装申请评分头部数据
                () -> List.of(
                        List.of("楼龄"),
                        List.of("认证单项分"),
                        List.of("地理位置"),
                        List.of("认证单项分"),
                        List.of("楼层数"),
                        List.of("认证单项分"),
                        List.of("月租金"),
                        List.of("认证单项分"),
                        List.of("外观造型"),
                        List.of("认证单项分"),
                        List.of("侯梯厅"),
                        List.of("认证单项分"),
                        List.of("地下车库"),
                        List.of("认证单项分")
                ),
                // 组装AI评分头部数据
                () -> List.of(
                        List.of("AI楼龄"),
                        List.of("AI单项分"),
                        List.of("AI地理位置"),
                        List.of("AI单项分"),
                        List.of("AI楼层数"),
                        List.of("AI单项分"),
                        List.of("AI月租金"),
                        List.of("AI单项分"),
                        List.of("AI外观造型"),
                        List.of("AI单项分"),
                        List.of("AI侯梯厅"),
                        List.of("AI单项分"),
                        List.of("AI地下车库"),
                        List.of("AI单项分")
                ),
                // 填充数据
                (detailsEntity) -> Objects.isNull(detailsEntity)
                        ? Collections.nCopies(7, 0L)
                        : List.of(
                        detailsEntity.getBuildingAge(),
                        detailsEntity.getBuildingLocation(),
                        detailsEntity.getBuildingNumber(),
                        detailsEntity.getBuildingPrice(),
                        detailsEntity.getBuildingExterior(),
                        detailsEntity.getBuildingHall(),
                        detailsEntity.getBuildingGarage()
                ),
                // 填充AI数据
                (detailsEntity) -> Objects.isNull(detailsEntity)
                        ? Collections.nCopies(14, 0L)
                        : List.of(
                        detailsEntity.getThirdBuildingAge(),
                        detailsEntity.getThirdBuildingAgeId(),
                        detailsEntity.getThirdBuildingLocation(),
                        detailsEntity.getThirdBuildingLocationId(),
                        detailsEntity.getThirdBuildingNumber(),
                        detailsEntity.getThirdBuildingNumberId(),
                        detailsEntity.getThirdBuildingPrice(),
                        detailsEntity.getThirdBuildingPriceId(),
                        detailsEntity.getThirdBuildingExterior(),
                        detailsEntity.getThirdBuildingExteriorId(),
                        detailsEntity.getThirdBuildingLobby(),
                        detailsEntity.getThirdBuildingLobbyId(),
                        detailsEntity.getThirdBuildingGarage(),
                        detailsEntity.getThirdBuildingGarageId()
                ),
                () -> "商住楼");
    }

    /**
     * 综合体
     */
    private void writeComprehensiveBuilding(List<BuildingRatingEntity> ratingEntities, ExcelWriter excelWriter) {
        fillingSheetData(ratingEntities, excelWriter,
                // 组装申请评分头部数据
                () -> List.of(
                        List.of("综合体品牌"),
                        List.of("认证单项分"),
                        List.of("地理位置"),
                        List.of("认证单项分"),
                        List.of("点评评分"),
                        List.of("认证单项分")
                ),
                // 组装AI评分头部数据
                () -> List.of(
                        List.of("AI综合体品牌"),
                        List.of("AI单项分"),
                        List.of("AI地理位置"),
                        List.of("AI单项分"),
                        List.of("AI点评评分"),
                        List.of("AI单项分")
                ),
                // 填充认证数据
                (detailsEntity) -> Objects.isNull(detailsEntity)
                        ? Collections.nCopies(3, 0L)
                        : List.of(
                        detailsEntity.getBuildingBrand(),
                        detailsEntity.getBuildingLocation(),
                        detailsEntity.getBuildingRating()
                ),
                // 填充AI数据
                (detailsEntity) -> Objects.isNull(detailsEntity)
                        ? Collections.nCopies(6, 0L)
                        : List.of(
                        detailsEntity.getThirdBuildingBrand(),
                        detailsEntity.getThirdBuildingBrandId(),
                        detailsEntity.getThirdBuildingLocation(),
                        detailsEntity.getThirdBuildingLocationId(),
                        detailsEntity.getThirdBuildingRating(),
                        detailsEntity.getThirdBuildingRatingId()
                ),
                () -> "综合体");
    }

    /**
     * 产业园区
     */
    private void writeIndustrialPark(List<BuildingRatingEntity> ratingEntities, ExcelWriter excelWriter) {
        fillingSheetData(ratingEntities, excelWriter,
                // 组装申请评分头部数据
                () -> List.of(
                        List.of("楼龄"),
                        List.of("认证单项分"),
                        List.of("地理位置"),
                        List.of("认证单项分"),
                        List.of("楼层数"),
                        List.of("认证单项分"),
                        List.of("入驻率"),
                        List.of("认证单项分"),
                        List.of("外观造型"),
                        List.of("认证单项分"),
                        List.of("侯梯厅"),
                        List.of("认证单项分")
                ),
                // 组装特定的头部数据
                () -> List.of(
                        List.of("AI楼龄"),
                        List.of("AI单项分"),
                        List.of("AI地理位置"),
                        List.of("AI单项分"),
                        List.of("AI楼层数"),
                        List.of("AI单项分"),
                        List.of("AI入驻率"),
                        List.of("AI单项分"),
                        List.of("AI外观造型"),
                        List.of("AI单项分"),
                        List.of("AI侯梯厅"),
                        List.of("AI单项分")
                ),
                // 填充认证数据
                (detailsEntity) -> Objects.isNull(detailsEntity)
                        ? Collections.nCopies(6, 0L)
                        : List.of(
                        detailsEntity.getBuildingAge(),
                        detailsEntity.getBuildingLocation(),
                        detailsEntity.getBuildingNumber(),
                        detailsEntity.getBuildingSettled(),
                        detailsEntity.getBuildingExterior(),
                        detailsEntity.getBuildingHall()
                ),
                // 填充AI数据
                (detailsEntity) -> Objects.isNull(detailsEntity)
                        ? Collections.nCopies(12, 0L)
                        : List.of(
                        detailsEntity.getThirdBuildingAge(),
                        detailsEntity.getThirdBuildingAgeId(),
                        detailsEntity.getThirdBuildingLocation(),
                        detailsEntity.getThirdBuildingLocationId(),
                        detailsEntity.getThirdBuildingNumber(),
                        detailsEntity.getThirdBuildingNumberId(),
                        detailsEntity.getThirdBuildingSettled(),
                        detailsEntity.getThirdBuildingSettledId(),
                        detailsEntity.getThirdBuildingExterior(),
                        detailsEntity.getThirdBuildingExteriorId(),
                        detailsEntity.getThirdBuildingHall(),
                        detailsEntity.getThirdBuildingHallId()
                ),
                () -> " 产业园区");
    }

    /**
     * 填充sheet数据
     */
    private void fillingSheetData(List<BuildingRatingEntity> ratingEntities, ExcelWriter excelWriter,
                                  Supplier<List<List<String>>> specificHeaderSupplier,
                                  Supplier<List<List<String>>> specificThirdHeaderSupplier,
                                  Function<BuildingDetailsEntity, List<Long>> getScoreIdsFunc,
                                  Function<BuildingDetailsEntity, List<Object>> getThirdScoreIdsFunc,
                                  Supplier<String> buildingTypeNameSupplier
    ) {
        // 获取过程数据
        Set<String> buildingNos = ratingEntities.stream().map(BuildingRatingEntity::getBuildingNo).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(buildingNos)) {
            return;
        }
        Map<String, BuildingDetailsEntity> detailsEntityMap = buildingDetailsService.lambdaQuery()
                .in(BuildingDetailsEntity::getBuildingNo, buildingNos)
                .eq(BuildingDetailsEntity::getDeleted, BooleFlagEnum.NO.getCode())
                .list().stream()
                .collect(Collectors.toMap(BuildingDetailsEntity::getBuildingNo, Function.identity(), (k1, k2) -> k2));
        // 组装指标项
        List<List<String>> headers = new ArrayList<>(64);
        headers.addAll(DEFAULT_EXPORT_HEADERS);
        headers.addAll(List.of(List.of("认证总分"), List.of("认证等级")));
        headers.addAll(specificHeaderSupplier.get());
        headers.addAll(List.of(List.of("AI总分"), List.of("AI评级")));
        headers.addAll(specificThirdHeaderSupplier.get());
        // 填充数据
        List<List<Object>> dataList = ratingEntities.stream()
                .map(ratingEntity -> {
                    List<Object> rowData = Lists.newArrayListWithExpectedSize(headers.size());
                    rowData.add(ratingEntity.getBuildingNo());
                    rowData.add(buildingTypeNameSupplier.get());
                    rowData.add(ratingEntity.getBuildingName());
                    rowData.add(ratingEntity.getMapProvince());
                    rowData.add(ratingEntity.getMapCity());
                    rowData.add(ratingEntity.getMapRegion());
                    rowData.add(rsaExample.decryptByPrivate(ratingEntity.getMapAddress()));
                    BuildingDetailsEntity detailsEntity = detailsEntityMap.get(ratingEntity.getBuildingNo());
                    // 申请评分数据
                    rowData.add(ratingEntity.getBuildingScore());
                    rowData.add(ratingEntity.getProjectLevel());
                    fillingWeightScore(getScoreIdsFunc.apply(detailsEntity), rowData);
                    // AI评分数据
                    rowData.add(ratingEntity.getBuildingAiScore());
                    rowData.add(ratingEntity.getProjectAiLevel());
                    fillingThirdWeightScore(getThirdScoreIdsFunc.apply(detailsEntity), rowData);
                    return rowData;
                })
                .toList();
        // 填充指定sheet的数据
        ExcelWriterSheetBuilder sheet = EasyExcel.writerSheet(buildingTypeNameSupplier.get()).head(headers);
        excelWriter.write(dataList, sheet.build());
    }

    /**
     * 根据评分项计算加权得分
     */
    private void fillingWeightScore(List<Long> scoreIds, List<Object> rowData) {
        if (CollectionUtils.isEmpty(scoreIds)) {
            return;
        }
        for (Long scoreId : scoreIds) {
            BuildingParameterEntity entity = parameterEntityMapHolder.get().get(scoreId);
            if (Objects.isNull(entity)) {
                rowData.add(null);
                rowData.add(null);
                continue;
            }
            rowData.add(entity.getParameterName());
            rowData.add(entity.getParameterScore().multiply(entity.getWeightValue()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        }
    }

    private void fillingThirdWeightScore(List<Object> objects, List<Object> rowData) {
        if (CollectionUtils.isEmpty(objects)) {
            return;
        }
        if (objects.size() % 2 != 0) {
            return;
        }
        //
        for (int i = 0; i < objects.size() - 1; i += 2) {
            String s = String.valueOf(objects.get(i));
            rowData.add(Objects.equals(s, "0") ? null : s);
            long scoreId = Long.parseLong(String.valueOf(objects.get(i + 1)));
            BuildingParameterEntity entity = parameterEntityMapHolder.get().get(scoreId);
            if (Objects.isNull(entity)) {
                rowData.add(null);
                continue;
            }
            rowData.add(entity.getParameterScore().multiply(entity.getWeightValue()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        }
    }
}
