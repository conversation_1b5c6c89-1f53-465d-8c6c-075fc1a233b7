package com.coocaa.meht.module.web.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.meht.module.web.dao.BuildingParameterDao;
import com.coocaa.meht.module.web.dto.BuildingParameterDto;
import com.coocaa.meht.module.web.dto.BuildingTypesDto;
import com.coocaa.meht.module.web.entity.BuildingParameterEntity;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.service.BuildingParameterService;
import com.coocaa.meht.module.web.service.BuildingRatingService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class BuildingParameterServiceImpl extends ServiceImpl<BuildingParameterDao, BuildingParameterEntity> implements BuildingParameterService {

    @Value("${parameter.max.dataFlag}")
    private Integer dataFlag;

    @Resource
    private BuildingRatingService buildingRatingService;

    @Override
    public List<BuildingTypesDto> getBuildingType(String buildingNo) {
        Integer dataVersion = dataFlag;
        if (StrUtil.isNotBlank(buildingNo)) {
            BuildingRatingEntity buildingRating = buildingRatingService.getByBuildingNo(buildingNo);
            if (buildingRating != null) {
                dataVersion = buildingRating.getDataFlag();
            }
        }
        return getBuildingParameter(dataVersion);
    }

    @Override
    public List<BuildingTypesDto> getParameterBuildingType(Integer dataVersion) {

        return getBuildingParameter(dataVersion);
    }

    private List<BuildingTypesDto> getBuildingParameter(Integer dataVersion) {

        List<BuildingTypesDto> buildingTypesDtoList = this.baseMapper.getBuildingType(dataVersion);
        for (BuildingTypesDto buildingTypesDto : buildingTypesDtoList) {
            List<BuildingParameterDto> buildingParameterDtoList = this.baseMapper.getBuilding(buildingTypesDto.getId(), dataVersion);
            buildingTypesDto.setParameter(buildingParameterDtoList);
            for (BuildingParameterDto buildingParameterDto : buildingParameterDtoList) {
                List<BuildingParameterDto> buildingParameterDtoList1 = this.baseMapper.getBuildingByParentId(buildingParameterDto.getId(), buildingParameterDto.getBuildingType(), dataVersion);
                buildingParameterDto.setChild(buildingParameterDtoList1);
            }
        }
        return buildingTypesDtoList;
    }


    @Override
    public Map<String, BuildingParameterEntity> getByMapIds(List<Long> ids) {
        List<BuildingParameterEntity> list = this.list(Wrappers.<BuildingParameterEntity>lambdaQuery()
                .in(BuildingParameterEntity::getId, ids));
        Map<String, BuildingParameterEntity> result = new HashMap<>();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(ele -> result.put(ele.getParameterCode(), ele));
        }
        return result;
    }
}
