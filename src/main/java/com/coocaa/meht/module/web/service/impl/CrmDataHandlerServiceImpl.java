package com.coocaa.meht.module.web.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.nacos.shaded.io.grpc.internal.JsonUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.coocaa.meht.common.CommonConstants;
import com.coocaa.meht.common.LoginUser;
import com.coocaa.meht.common.PageResult;
import com.coocaa.meht.common.SecurityUser;
import com.coocaa.meht.common.bean.CodeNameVO;
import com.coocaa.meht.common.constants.TopicConstants;
import com.coocaa.meht.common.exception.ServerException;
import com.coocaa.meht.kafka.KafkaProducerService;
import com.coocaa.meht.module.crm.dto.CrmBusinessDto;
import com.coocaa.meht.module.crm.dto.CrmCustomerListDto;
import com.coocaa.meht.module.crm.dto.CrmPageResultDto;
import com.coocaa.meht.module.crm.dto.req.CrmBusinessReq;
import com.coocaa.meht.module.crm.dto.req.CustomerListReq;
import com.coocaa.meht.module.crm.enums.SceneTypeEnum;
import com.coocaa.meht.module.crm.service.CrmBusinessService;
import com.coocaa.meht.module.crm.service.CrmCustomerService;
import com.coocaa.meht.module.sys.service.SysUserService;
import com.coocaa.meht.module.web.dto.BuildingPropertyCompanyDTO;
import com.coocaa.meht.module.web.dto.BuildingPropertyCompanyParam;
import com.coocaa.meht.module.web.dto.BusinessOpportunityDto;
import com.coocaa.meht.module.web.dto.convert.BusinessOpportunityConvert;
import com.coocaa.meht.module.web.dto.convert.PropertyCompanyConvert;
import com.coocaa.meht.module.web.dto.crm.CrmBusinessParamDto;
import com.coocaa.meht.module.web.dto.crm.CrmBusinessUpdateParamDto;
import com.coocaa.meht.module.web.dto.crm.CrmCustomerParamDto;
import com.coocaa.meht.module.web.dto.crm.CrmCustomerResultDto;
import com.coocaa.meht.module.web.dto.crm.CrmFieldResultDto;
import com.coocaa.meht.module.web.dto.crm.CrmLoginResultDto;
import com.coocaa.meht.module.web.dto.crm.CrmOwenerUserIdUpdateParamDto;
import com.coocaa.meht.module.web.dto.crm.CrmResult2Dto;
import com.coocaa.meht.module.web.dto.crm.CrmResultDto;
import com.coocaa.meht.module.web.dto.crm.CrmUserResultDto;
import com.coocaa.meht.module.web.dto.req.BusinessReq;
import com.coocaa.meht.module.web.entity.BuildingDetailsEntity;
import com.coocaa.meht.module.web.entity.BuildingRatingEntity;
import com.coocaa.meht.module.web.entity.BusinessOpportunityEntity;
import com.coocaa.meht.module.web.entity.PropertyCompanyEntity;
import com.coocaa.meht.module.web.enums.BusinessChangeStatusEnum;
import com.coocaa.meht.module.web.service.BuildingDetailsService;
import com.coocaa.meht.module.web.service.BuildingRatingService;
import com.coocaa.meht.module.web.service.BusinessOpportunityService;
import com.coocaa.meht.module.web.service.DataHandlerService;
import com.coocaa.meht.module.web.service.IBuildingPropertyCompanyService;
import com.coocaa.meht.module.web.service.property.IPropertyCompanyService;
import com.coocaa.meht.module.web.vo.BusinessStatusChangeVO;
import com.coocaa.meht.utils.AESECBUtil;
import com.coocaa.meht.utils.DateUtils;
import com.coocaa.meht.utils.JsonUtils;
import com.coocaa.meht.utils.RsaExample;
import com.coocaa.meht.utils.UserCodeUtils;
import com.coocaa.meht.utils.UserIdUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.coocaa.meht.common.CommonConstants.CRM_NOT_LOGIN_STATUS_CODE;
import static com.coocaa.meht.common.CommonConstants.CRM_SUCCESS_STATUS_CODE;

@Slf4j
@Service
public class CrmDataHandlerServiceImpl implements DataHandlerService {
    private static final String CRM_CUSTOMER_FIELD = "/crmCustomer/field/";
    private static final String CRM_CUSTOMER_SEARCH_URL = "/crmCustomer/queryPageList";

    private static final String CRM_BUSINESS_FIELD = "/crmBusiness/field/";

    private static final String CRM_ADMIN_LOGIN = "/login";

    private static final String CRM_CUSTOMER_ADD = "/crmCustomer/add";

    private static final String CRM_USER = "/adminUser/queryUserList";

    private static final String CRM_BUSINESS_ADD = "/crmBusiness/add";

    private static final String CRM_BUSINESS_UPDATE = "/crmBusiness/update";

    private static final String CRM_QUERY_LOGIN_USER = "/adminUser/queryLoginUser";

    @Value("${crm.admin.host:https://beta-crm.coocaa.com/api}")
    private String crmHttp;

    @Value("${crm.admin.phone:3tzGg+8d4cYjG8mspYdxVQ==}")
    private String crmAdminPhone;

    @Value("${crm.admin.password:igpE9nLxoXz7997U+2dNdQ==}")
    private String crmAdminPassword;

    /**
     * 商机状态组  正式环境：1860213219512545280  开发(测试环境): 1861029917665501184
     */
    @Value("${crm.business.typeId:1860213219512545280}")
    private String businessTypeId;

    @Resource
    private RsaExample rsaExample;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private BuildingDetailsService buildingDetailsService;

    @Lazy
    @Resource
    private BuildingRatingService buildingRatingService;

    @Resource
    private SysUserService sysUserService;

    @Resource
    private BusinessOpportunityService businessOpportunityService;

    @Resource
    private IBuildingPropertyCompanyService iBuildingPropertyCompanyService;

    @Resource
    private KafkaProducerService kafkaProducerService;


    @Resource
    private IPropertyCompanyService iPropertyCompanyService;

    @Autowired
    private CrmCustomerService crmCustomerService;

    @Resource
    private CrmBusinessService crmBusinessService;


    @Override
    public void handlerBuildingRating(BuildingRatingEntity entity, LoginUser user) {
        log.info("[handlerBuildingRating][处理楼宇数据,id:{},username:{},buildingNo:{},buildingName:{}]", user.getId(),
                user.getUserName(), entity.getBuildingNo(), entity.getBuildingName());

        //商机名
        String businessName = entity.getBuildingName() + "-默认";
        // 保存商机
        long nameNum = businessOpportunityService.count(Wrappers.<BusinessOpportunityEntity>lambdaQuery()
                .eq(BusinessOpportunityEntity::getName, businessName));
        if (nameNum > 0) {
            throw new ServerException(999, "商机名称不唯一");
        }

        //保存商机
        int countNum = (int) businessOpportunityService.count(Wrappers.<BusinessOpportunityEntity>lambdaQuery()
                .eq(BusinessOpportunityEntity::getBuildingNo, entity.getBuildingNo()));
        BusinessOpportunityEntity businessOpportunityEntity = new BusinessOpportunityEntity();
        businessOpportunityEntity.setName(businessName);
        businessOpportunityEntity.setCode(businessCode(entity.getBuildingNo(), countNum));
        businessOpportunityEntity.setBuildingNo(entity.getBuildingNo());
        businessOpportunityEntity.setOwner(user.getUserCode());
        businessOpportunityEntity.setSubmitUser(user.getUserCode());
        businessOpportunityEntity.setStatus(BusinessChangeStatusEnum.TO_BE_DISCUSSED.getCode());
        businessOpportunityService.save(businessOpportunityEntity);

        // 发状态消息
        BusinessStatusChangeVO businessStatusChangeVO = new BusinessStatusChangeVO();
        businessStatusChangeVO.setBusinessCode(businessOpportunityEntity.getCode());
        businessStatusChangeVO.setStatus(businessOpportunityEntity.getStatus());
        businessStatusChangeVO.setOperatorId(UserIdUtils.getUserId());
        businessStatusChangeVO.setOperatorWno(UserCodeUtils.getUserCode());
        businessStatusChangeVO.setOperatorName(SecurityUser.getUser().getUserName());
        kafkaProducerService.sendMessage(TopicConstants.BUSINESS_STATUS_CHANGE, JSON.toJSONString(businessStatusChangeVO));
    }

    @Override
    public CrmCustomerResultDto crmHandlerBuildingRating(String buildingNo) {
        LambdaQueryWrapper<BuildingRatingEntity> queryWrapper = new QueryWrapper<BuildingRatingEntity>().lambda()
                .eq(StringUtils.isNotBlank(buildingNo), BuildingRatingEntity::getBuildingNo, buildingNo)
                .eq(BuildingRatingEntity::getBuildingStatus, BuildingRatingEntity.BuildingStatus.CONFIRMED.getValue())
                .eq(BuildingRatingEntity::getStatus, BuildingRatingEntity.Status.AUDITED.getValue())
                .eq(BuildingRatingEntity::getCrmPushStatus, 0);
        List<BuildingRatingEntity> list = buildingRatingService.list(queryWrapper);
        if (!CollectionUtils.isEmpty(list)) {
            Set<String> collect = list.stream().map(BuildingRatingEntity::getSubmitUser).collect(Collectors.toSet());
            Set<String> buildingNos = list.stream().map(BuildingRatingEntity::getBuildingNo).collect(Collectors.toSet());
            Map<String, LoginUser> users = sysUserService.getUsers(collect);

            List<BuildingDetailsEntity> buildingDetailsEntities = buildingDetailsService.list(Wrappers.<BuildingDetailsEntity>lambdaQuery()
                    .in(BuildingDetailsEntity::getBuildingNo, buildingNos));
            Map<String, BuildingDetailsEntity> buildingDetailsEntityMap = buildingDetailsEntities.stream()
                    .collect(Collectors.toMap(BuildingDetailsEntity::getBuildingNo, e -> e, (v1, v2) -> v1));
            // 生成客户数据并保存
            for (BuildingRatingEntity buildingRatingEntity : list) {
                try {
                    LoginUser loginUser = users.get(buildingRatingEntity.getSubmitUser());
                    SecurityUser.login(loginUser);
                    // 检查客户是否已经推送过CRM
                    List<Map<String, Object>> customers = Optional.ofNullable(getCustomer(buildingRatingEntity.getBuildingName()))
                            .map(CrmPageResultDto::getData)
                            .map(CrmPageResultDto.DataDto::getList).orElse(null);
                    if (CollectionUtils.isEmpty(customers)) {
                        //获取关联用户 crm系统id
                        String crmOwnerUserId = getCrmOwnerUserId(loginUser.getUserName(), loginUser.getPhone());
                        CrmCustomerParamDto.EntityDto entityDto = new CrmCustomerParamDto.EntityDto();
                        entityDto.setCustomerName(buildingRatingEntity.getBuildingName())
                                .setCustomerBase(BuildingRatingEntity.BuildingType.getNameByValue(buildingRatingEntity.getBuildingType())).setOwnerUserId(crmOwnerUserId);

                        // 生成客户数据并保存
                        BuildingDetailsEntity buildingDetailsEntity = buildingDetailsEntityMap.get(buildingRatingEntity.getBuildingNo());
                        CrmFieldResultDto customerField = getField(1);
                        List<CrmCustomerParamDto.FieldDto> fieldList = new ArrayList<>();
                        for (CrmFieldResultDto.DataDto customerFieldDatum : customerField.getData()) {
                            CrmCustomerParamDto.FieldDto fieldDto = new CrmCustomerParamDto.FieldDto();
                            fillCustomerData(fieldDto, customerFieldDatum, buildingRatingEntity, buildingDetailsEntity);
                            if (fieldDto.getValue() == null || StringUtils.isBlank(fieldDto.getFieldId())) {
                                continue;
                            }

                            fieldList.add(fieldDto);
                        }
                        CrmCustomerParamDto paramDto = new CrmCustomerParamDto();
                        paramDto.setEntity(entityDto).setField(fieldList);
                        CrmCustomerResultDto crmCustomerResultDto = saveCustomer(paramDto);
                        buildingRatingService.lambdaUpdate().set(BuildingRatingEntity::getCustomerId, crmCustomerResultDto.getData().getCustomerId())
                                .set(BuildingRatingEntity::getCrmPushStatus, 1).eq(BuildingRatingEntity::getId, buildingRatingEntity.getId())
                                .update();
                    } else {
                        Map<String, List<Map<String, Object>>> customerName = customers.stream().collect(Collectors.groupingBy(e -> e.get("customerName").toString()));
                        buildingRatingService.lambdaUpdate().set(BuildingRatingEntity::getCustomerId, customerName.get(buildingRatingEntity.getBuildingName()).get(0).get("customerId"))
                                .set(BuildingRatingEntity::getCrmPushStatus, 1).eq(BuildingRatingEntity::getId, buildingRatingEntity.getId())
                                .update();
                    }
                } catch (Exception e) {
                    log.error("[handlerBuildingRating][客户同步出错,buildingRatingEntity:{}]", buildingRatingEntity);
                    buildingRatingService.lambdaUpdate()
                            .set(BuildingRatingEntity::getCrmPushStatus, 2)
                            .eq(BuildingRatingEntity::getId, buildingRatingEntity.getId())
                            .update();
                } finally {
                    SecurityUser.clearLogin();
                }
            }
        }

        //保存商机数据
        crmBusinessAdd();
        return null;
    }


    public void crmBusinessAdd() {
        List<BusinessOpportunityEntity> businessOpportunityEntities = businessOpportunityService.list(Wrappers.<BusinessOpportunityEntity>lambdaQuery()
                .eq(BusinessOpportunityEntity::getCrmBusinessId, "")
                .eq(BusinessOpportunityEntity::getCustomerId, ""));

        Set<String> collect = businessOpportunityEntities.stream().map(BusinessOpportunityEntity::getBuildingNo).collect(Collectors.toSet());
        List<BuildingRatingEntity> list = buildingRatingService.list(Wrappers.<BuildingRatingEntity>lambdaQuery().in(BuildingRatingEntity::getBuildingNo, collect));
        Map<String, BuildingRatingEntity> buildingRatingMap = list.stream().collect(Collectors.toMap(BuildingRatingEntity::getBuildingNo, e -> e, (v1, v2) -> v1));
        Set<String> userCode = businessOpportunityEntities.stream().map(BusinessOpportunityEntity::getSubmitUser).collect(Collectors.toSet());
        Map<String, LoginUser> users = sysUserService.getUsers(userCode);

        for (BusinessOpportunityEntity businessOpportunityEntity : businessOpportunityEntities) {
            try {
                LoginUser loginUser = users.get(businessOpportunityEntity.getSubmitUser());
                SecurityUser.login(loginUser);
                BuildingRatingEntity buildingRating = buildingRatingMap.get(businessOpportunityEntity.getBuildingNo());
                // 生产商单数据
                CrmFieldResultDto businessField = this.getField(2);
                List<CrmBusinessParamDto.FieldDto> fieldBusinessList = new ArrayList<>();
                for (CrmFieldResultDto.DataDto fieldFieldDatum : businessField.getData()) {
                    CrmBusinessParamDto.FieldDto fieldDto = new CrmBusinessParamDto.FieldDto();
                    this.fillBusinessData(fieldDto, fieldFieldDatum);
                    if (fieldDto.getValue() == null || StringUtils.isBlank(fieldDto.getFieldId())) {
                        continue;
                    }
                    fieldBusinessList.add(fieldDto);
                }
                //获取关联用户 crm系统id
                String crmOwnerUserId = getCrmOwnerUserId(loginUser.getUserName(), loginUser.getPhone());
                //推crm
                Map<String, String> customerMap = new HashMap<>(3);
                customerMap.put("customerId", buildingRating.getCustomerId());
                customerMap.put("customerName", buildingRating.getBuildingName());
                CrmBusinessParamDto crmBusinessParamDto = new CrmBusinessParamDto();
                CrmBusinessParamDto.EntityDto businessEntity = new CrmBusinessParamDto.EntityDto();
                businessEntity.setOwnerUserId(crmOwnerUserId).setTypeId(businessTypeId).setBusinessName(businessOpportunityEntity.getName()).setCustomer(Collections.singletonList(customerMap));
                crmBusinessParamDto.setEntity(businessEntity).setField(fieldBusinessList);
                saveBusiness(crmBusinessParamDto);

                CrmBusinessReq crmBusinessReq = new CrmBusinessReq();
                crmBusinessReq.setSearch(businessOpportunityEntity.getName());
                // 为crm做时间补偿
                String businessCrmId = null;
                for (int i = 0; i < 60; i++) {
                    PageResult<CrmBusinessDto> crmBusinessDtoPageResult = crmBusinessService.crmList(crmBusinessReq);
                    if (CollectionUtils.isNotEmpty(crmBusinessDtoPageResult.getList())) {
                        businessCrmId = crmBusinessDtoPageResult.getList().get(0).getBusinessId();
                        break;
                    }
                    try {
                        Thread.sleep(ThreadLocalRandom.current().nextLong(100, 500));
                    } catch (InterruptedException ignore) {

                    }
                }
                businessOpportunityService.lambdaUpdate()
                        .set(BusinessOpportunityEntity::getCustomerId, buildingRating.getCustomerId())
                        .set(BusinessOpportunityEntity::getCrmBusinessId, businessCrmId)
                        .eq(BusinessOpportunityEntity::getId, businessOpportunityEntity.getId())
                        .update();
            } catch (Exception e) {
                log.error("[handlerBuildingRating][商机同步出错,businessOpportunityEntity:{}]", businessOpportunityEntity);
            } finally {
                SecurityUser.clearLogin();
            }
        }

    }


    /**
     * 保存商机数据
     */
    public String businessAdd(String businessName, String buildingNo, LoginUser user) {

        //保存商机
        int countNum = (int) businessOpportunityService.count(Wrappers.<BusinessOpportunityEntity>lambdaQuery()
                .eq(BusinessOpportunityEntity::getBuildingNo, buildingNo));
        BusinessOpportunityEntity businessOpportunityEntity = new BusinessOpportunityEntity();
        businessOpportunityEntity.setName(businessName);
        businessOpportunityEntity.setCode(businessCode(buildingNo, countNum));
        businessOpportunityEntity.setBuildingNo(buildingNo);
        businessOpportunityEntity.setOwner(user.getUserCode());
        businessOpportunityEntity.setSubmitUser(user.getUserCode());
        businessOpportunityEntity.setStatus(BusinessChangeStatusEnum.TO_BE_DISCUSSED.getCode());
        businessOpportunityService.save(businessOpportunityEntity);

        // 发商机状态变更消息
        BusinessStatusChangeVO businessStatusChangeVO = new BusinessStatusChangeVO();
        businessStatusChangeVO.setBusinessCode(businessOpportunityEntity.getCode());
        businessStatusChangeVO.setStatus(businessOpportunityEntity.getStatus());
        businessStatusChangeVO.setOperatorId(UserIdUtils.getUserId());
        businessStatusChangeVO.setOperatorWno(UserCodeUtils.getUserCode());
        businessStatusChangeVO.setOperatorName(SecurityUser.getUser().getUserName());
        kafkaProducerService.sendMessageAfterCommit(TopicConstants.BUSINESS_STATUS_CHANGE, JSON.toJSONString(businessStatusChangeVO));

        return businessOpportunityEntity.getCode();
    }


    /**
     * 商机编码
     */

    private String businessCode(String buildingNo, int num) {
        int number = num + 1;
        return buildingNo + "-" + number;
    }

    @Override
    public void syncCrmCustomerId() {
        HashMap<String, Object> param = new HashMap<>();
        param.put("type", 2);
        String result = HttpRequest.post(crmHttp + "/crmScene/queryScene")
                .header("admin-token", getAdminToken(false))
                .form(param)
                .execute().body();
        CrmResult2Dto crmResult2Dto = JsonUtils.fromJson(result, CrmResult2Dto.class);
        if (!Objects.equals(crmResult2Dto.getCode(), CRM_SUCCESS_STATUS_CODE)) {
            log.error("[syncCrmCustomerId][获取客户场景值失败,result:{}]", result);
            return;
        }
        String sceneId = "";

        for (Object crmResult2DtoDatum : crmResult2Dto.getData()) {
            Map<String, Object> resultMap = (Map<String, Object>) crmResult2DtoDatum;
            Object name = resultMap.get("name");
            if (name == null) {
                continue;
            }
            if (!"全部客户".equals(name)) {
                continue;
            }
            sceneId = (String) resultMap.get("sceneId");
        }

        if (StringUtils.isBlank(sceneId)) {
            log.error("[syncCrmCustomerId][获取客户场景值失败2,result:{}]", result);
            return;
        }

        CustomerListReq req = new CustomerListReq();
        req.setSceneId(sceneId);
        req.setPage(1);
        req.setLimit(15L);

        List<BuildingRatingEntity> buildingRatingList = buildingRatingService.listNotCustomerId();
        if (CollUtil.isEmpty(buildingRatingList)) {
            return;
        }

        for (BuildingRatingEntity buildingRatingEntity : buildingRatingList) {
            if (buildingRatingEntity.getCrmPushStatus() != 1 || StringUtils.isNotBlank(buildingRatingEntity.getCustomerId())) {
                continue;
            }
            LoginUser loginUser = sysUserService.getUserByEmpCode(buildingRatingEntity.getSubmitUser());
            if (loginUser == null) {
                continue;
            }
            try {
                String ownerUserId = getCrmOwnerUserId(loginUser.getUserName(), loginUser.getPhone());
                if (StringUtils.isBlank(ownerUserId)) {
                    continue;
                }
                req.setSearch(buildingRatingEntity.getBuildingName());
                String result2 = HttpRequest.post(crmHttp + "/crmCustomer/queryPageList")
                        .header("admin-token", getAdminToken(false))
                        .body(JsonUtils.toJson(req))
                        .execute().body();
                CrmPageResultDto crmPageResultDto = JsonUtils.fromJson(result2, CrmPageResultDto.class);
                if (!Objects.equals(crmPageResultDto.getCode(), CRM_SUCCESS_STATUS_CODE)) {
                    log.warn("[syncCrmCustomerId][查询客户信息失败,result:{}]", result2);
                    continue;
                }
                List<Map<String, Object>> list = crmPageResultDto.getData().getList();
                if (CollUtil.isEmpty(list)) {
                    log.warn("[syncCrmCustomerId][查询客户信息失败2,result:{}]", result2);
                    continue;
                }
                for (Map<String, Object> content : list) {
                    CrmCustomerListDto crmCustomerListDto = JsonUtils.fromJson(JsonUtils.toJson(content), CrmCustomerListDto.class);
                    if (ownerUserId.equals(crmCustomerListDto.getOwnerUserId()) && buildingRatingEntity.getBuildingName().equals(crmCustomerListDto.getCustomerName())) {
                        buildingRatingService.updateCustomerId(buildingRatingEntity.getId(), crmCustomerListDto.getCustomerId());
                        log.info("[syncCrmCustomerId][同步更新楼宇客户id,buildingRatingId:{},customerId:{}]", buildingRatingEntity.getId(), crmCustomerListDto.getCustomerId());
                        break;
                    } else {
                        log.info("[syncCrmCustomerId][楼宇信息不匹配,楼宇名称:{},客户名称:{},ownerUserId:{},crmOwnerUserId:{},buildingRatingId:{},customerId:{}]",
                                buildingRatingEntity.getBuildingName(), crmCustomerListDto.getCustomerName(), ownerUserId, crmCustomerListDto.getOwnerUserId(), buildingRatingEntity.getId(), crmCustomerListDto.getCustomerId());
                    }
                }
            } catch (Exception e) {
                log.error("[syncCrmCustomerId][同步更新楼宇客户id异常,buildingRatingId:{}]", buildingRatingEntity.getId(), e);
            }

        }

    }

    @Override
    public void fixOwnerUserId() {
        HashMap<String, Object> param = new HashMap<>();
        param.put("type", 2);
        String result = HttpRequest.post(crmHttp + "/crmScene/queryScene")
                .header("admin-token", getAdminToken(false))
                .form(param)
                .execute().body();
        CrmResult2Dto crmResult2Dto = JsonUtils.fromJson(result, CrmResult2Dto.class);
        if (!Objects.equals(crmResult2Dto.getCode(), CRM_SUCCESS_STATUS_CODE)) {
            log.error("[syncCrmCustomerId][获取客户场景值失败,result:{}]", result);
            return;
        }
        String sceneId = "";

        for (Object crmResult2DtoDatum : crmResult2Dto.getData()) {
            Map<String, Object> resultMap = (Map<String, Object>) crmResult2DtoDatum;
            Object name = resultMap.get("name");
            if (name == null) {
                continue;
            }
            if (!"全部客户".equals(name)) {
                continue;
            }
            sceneId = (String) resultMap.get("sceneId");
        }

        if (StringUtils.isBlank(sceneId)) {
            log.error("[syncCrmCustomerId][获取客户场景值失败2,result:{}]", result);
            return;
        }
        CustomerListReq req = new CustomerListReq();
        req.setSceneId(sceneId);
        req.setPage(1);
        req.setLimit(15L);
        Long id = 0L;

        while (true) {
            List<BuildingRatingEntity> buildingRatingList = buildingRatingService.listPushCrm(id);
            if (CollUtil.isEmpty(buildingRatingList)) {
                break;
            }

            id = buildingRatingList.get(buildingRatingList.size() - 1).getId();

            for (BuildingRatingEntity buildingRatingEntity : buildingRatingList) {
                LoginUser loginUser = sysUserService.getUserByEmpCode(buildingRatingEntity.getSubmitUser());
                if (loginUser == null) {
                    continue;
                }
                try {
                    String ownerUserId = getCrmOwnerUserId(loginUser.getUserName(), loginUser.getPhone());
                    if (StringUtils.isBlank(ownerUserId)) {
                        continue;
                    }
                    req.setSearch(buildingRatingEntity.getBuildingName());
                    String result2 = HttpRequest.post(crmHttp + "/crmCustomer/queryPageList")
                            .header("admin-token", getAdminToken(false))
                            .body(JsonUtils.toJson(req))
                            .execute().body();
                    CrmPageResultDto crmPageResultDto = JsonUtils.fromJson(result2, CrmPageResultDto.class);
                    if (!Objects.equals(crmPageResultDto.getCode(), CRM_SUCCESS_STATUS_CODE)) {
                        log.warn("[syncCrmCustomerId][查询客户信息失败,result:{}]", result2);
                        continue;
                    }
                    List<Map<String, Object>> list = crmPageResultDto.getData().getList();
                    if (CollUtil.isEmpty(list)) {
                        log.warn("[syncCrmCustomerId][查询客户信息失败2,result:{}]", result2);
                        continue;
                    }
                    for (Map<String, Object> content : list) {
                        CrmCustomerListDto crmCustomerListDto = JsonUtils.fromJson(JsonUtils.toJson(content), CrmCustomerListDto.class);
                        if (!buildingRatingEntity.getBuildingName().equals(crmCustomerListDto.getCustomerName())) {
                            continue;
                        }
                        if (ownerUserId.equals(crmCustomerListDto.getOwnerUserId())) {
                            log.info("[fixOwnerUserId][楼宇信息用户信息与crm用户信息一致,buildingRatingId:{},currentUserId:{}]", buildingRatingEntity.getId(), crmCustomerListDto.getOwnerUserId());
                            continue;
                        }
                        String approveUserCode = buildingRatingEntity.getApproveUser();
                        LoginUser approveUser = sysUserService.getUserByEmpCode(approveUserCode);
                        if (approveUser == null) {
                            log.info("[fixOwnerUserId][审批用户不存在]");
                            continue;
                        }
                        String approveUserOwnerUserId = getCrmOwnerUserId(approveUser.getUserName(), approveUser.getPhone());
                        if (StringUtils.isBlank(approveUserOwnerUserId)) {
                            log.info("[fixOwnerUserId][审批用户crmId不存在]");
                            continue;
                        }
                        if (!approveUserOwnerUserId.equals(crmCustomerListDto.getOwnerUserId())) {
                            log.info("[fixOwnerUserId][11111]");
                            continue;
                        }
                        log.info("[fixOwnerUserId][需要修复的楼宇信息,buildingRatingId:{},currentUserId:{},targetUserId:{}]", buildingRatingEntity.getId(), crmCustomerListDto.getOwnerUserId(), ownerUserId);

                        CrmOwenerUserIdUpdateParamDto paramDto = new CrmOwenerUserIdUpdateParamDto();
                        paramDto.setOwnerUserId(ownerUserId);
                        paramDto.getIds().add(crmCustomerListDto.getCustomerId());

                        String result3 = HttpRequest.post(crmHttp + "/crmCustomer/changeOwnerUser")
                                .header("admin-token", getAdminToken(false))
                                .body(JsonUtils.toJson(paramDto))
                                .execute().body();

                        CrmResultDto crmResultDto = JsonUtils.fromJson(result3, CrmResultDto.class);
                        if (crmResultDto.getCode() != CRM_SUCCESS_STATUS_CODE) {
                            log.info("[fixOwnerUserId][修复的楼宇信失败,buildingRatingId:{},currentUserId:{},targetUserId:{}]", buildingRatingEntity.getId(), crmCustomerListDto.getOwnerUserId(), ownerUserId);
                        }

                    }
                } catch (Exception e) {
                    log.error("[syncCrmCustomerId][修复的楼宇信异常,buildingRatingId:{}]", buildingRatingEntity.getId(), e);
                }
            }
        }


    }

    @Override
    public String getToken(boolean forceRefresh) {
        return getAdminToken(forceRefresh);
    }

    @Override
    @Transactional
    public void addBusiness(BusinessReq req) {
        LoginUser loginUser = SecurityUser.getUser();
        String code = businessAdd(req.getName(), req.getBuildingNo(), loginUser);

        //关联物业
        req.setCode(code);
        bindProperty(req);
    }

    @Override
    public void updateBusiness(BusinessReq req) {

        //修改媒资商机
        businessOpportunityService.lambdaUpdate()
                .set(BusinessOpportunityEntity::getName, req.getName())
                .eq(BusinessOpportunityEntity::getCode, req.getCode())
                .update();

        //关联物业
        bindProperty(req);
    }

    /**
     * 关联物业
     */
    private void bindProperty(BusinessReq req) {
        PropertyCompanyEntity propertyCompanyEntity = iPropertyCompanyService.getById(req.getPropertyId());
        BuildingPropertyCompanyDTO property = new BuildingPropertyCompanyDTO();
        property.setBuildingNo(req.getBuildingNo());
        property.setProjectCode(req.getCode());
        property.setProjectName(req.getName());
        property.setPropertyId(req.getPropertyId());
        property.setPropertyName(propertyCompanyEntity.getName());
        BuildingPropertyCompanyParam propertyCompanyParam = PropertyCompanyConvert.INSTANCE.toPropertyCompanyParam(property);
        iBuildingPropertyCompanyService.saveBuildingProperty(propertyCompanyParam);
    }

    @Override
    public List<BusinessOpportunityDto> getCustomerBusiness(String buildingNo) {
        long convertStart = System.currentTimeMillis();
        List<BusinessOpportunityEntity> list = businessOpportunityService.list(Wrappers.<BusinessOpportunityEntity>lambdaQuery()
                .eq(BusinessOpportunityEntity::getBuildingNo, buildingNo));
        long convertEnd = System.currentTimeMillis();
        log.info("商机耗时: {} ms", (convertEnd - convertStart));
        log.info("转换前商机列表: {}", list);
        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<BusinessOpportunityDto> businessOpportunityDtos = BusinessOpportunityConvert.INSTANCE.toDtoList(list);
        log.info("商机转换耗时: {} ms", (System.currentTimeMillis() - convertEnd));
        log.info("转换后商机列表: {}",  businessOpportunityDtos);
        return businessOpportunityDtos;
    }

    @Override
    public List<CodeNameVO> getSceneTypeEnum() {
        return SceneTypeEnum.getNameList();
    }


    private void fillBusinessData(CrmBusinessParamDto.FieldDto fieldDto, CrmFieldResultDto.DataDto customerFieldDatum) {
        List<String> fieldCrmList = Arrays.asList("对接人", "姓名", "职位", "联系方式");

        if (!fieldCrmList.contains(customerFieldDatum.getName())) {
            return;
        }

        fieldDto.setFieldId(customerFieldDatum.getFieldId()).setFieldName(customerFieldDatum.getFieldName())
                .setFieldType(customerFieldDatum.getFieldType()).setType(customerFieldDatum.getType())
                .setName(customerFieldDatum.getName());
        fieldDto.setValue("");
    }


    private void fillCustomerData(CrmCustomerParamDto.FieldDto fieldDto, CrmFieldResultDto.DataDto customerFieldDatum, BuildingRatingEntity entity, BuildingDetailsEntity buildingDetailsEntity) {
        if (customerFieldDatum.getName().equals("楼宇编码")) {
            fieldDto.setValue(entity.getBuildingNo());
        } else if (customerFieldDatum.getName().equals("楼宇地区")) {
            fieldDto.setValue(entity.getMapProvince() + "-" + entity.getMapCity());
        } else if (customerFieldDatum.getName().equals("详细地址")) {
            fieldDto.setValue(rsaExample.decryptByPrivate(entity.getMapAddress()));
        } else if (customerFieldDatum.getName().equals("楼宇评级")) {
            fieldDto.setValue(entity.getProjectLevel());
        } else if (customerFieldDatum.getName().equals("评级认证状态")) {
            //fieldDto.setValue(BuildingRatingEntity.BuildingStatus.getNameByValue(entity.getBuildingStatus()));
            fieldDto.setValue(BuildingRatingEntity.BuildingStatus.CONFIRMED.getName());
        } else if (customerFieldDatum.getName().equals("楼宇加权总分")) {
            fieldDto.setValue(String.valueOf(entity.getBuildingScore()));
        } else if (customerFieldDatum.getName().equals("认证生效开始时间")) {
            fieldDto.setValue(DateUtils.format(entity.getAuthenticationStart(), DateUtils.DATE_TIME_PATTERN));
        } else if (customerFieldDatum.getName().equals("认证生效结束时间")) {
            fieldDto.setValue(DateUtils.format(entity.getAuthenticationEnd(), DateUtils.DATE_TIME_PATTERN));
        } else if (customerFieldDatum.getName().equals("建筑面积")) {
            fieldDto.setValue("");
        } else if (customerFieldDatum.getName().equals("覆盖人数")) {
            fieldDto.setValue("");
        } else if (customerFieldDatum.getName().equals("车位数")) {
            fieldDto.setValue("");
        } else if (customerFieldDatum.getName().equals("物业费")) {
            fieldDto.setValue("");
        } else if (customerFieldDatum.getName().equals("出租率")) {
            fieldDto.setValue("");
        } else if (customerFieldDatum.getName().equals("房价")) {
            fieldDto.setValue("");
        } else if (customerFieldDatum.getName().equals("户数")) {
            fieldDto.setValue("");
        } else if (customerFieldDatum.getName().equals("入住率") && Objects.nonNull(buildingDetailsEntity)) {
            fieldDto.setValue(String.valueOf(buildingDetailsEntity.getBuildingSettled()));
        } else if (customerFieldDatum.getName().equals("日均人流量")) {
            fieldDto.setValue("");
        } else if (customerFieldDatum.getName().equals("综合体品牌") && Objects.nonNull(buildingDetailsEntity)) {
            fieldDto.setValue(buildingDetailsEntity.getThirdBuildingBrand());
        } else if (customerFieldDatum.getName().equals("楼宇类型")) {
            fieldDto.setValue(BuildingRatingEntity.BuildingType.getNameByValue(entity.getBuildingType()));
        }

        fieldDto.setFieldId(customerFieldDatum.getFieldId()).setFieldName(customerFieldDatum.getFieldName())
                .setFieldType(customerFieldDatum.getFieldType()).setType(customerFieldDatum.getType())
                .setName(customerFieldDatum.getName());
    }

    private String getAdminToken(boolean forceRefresh) {
        String adminTokenKey = "meht:crm:admin_token:" + AESECBUtil.decryptStr(crmAdminPhone);
        if (!forceRefresh) {
            String cache = stringRedisTemplate.opsForValue().get(adminTokenKey);
            if (StringUtils.isNotBlank(cache)) {
                return cache;
            }
        }

        String lockKey = "meht:crm:admin_token:lock";
        Boolean locked = stringRedisTemplate.opsForValue().setIfAbsent(lockKey, crmAdminPhone, 15, TimeUnit.SECONDS);
        if (!Optional.ofNullable(locked).orElse(Boolean.TRUE)) {
            throw new ServerException("获取crm用户token异常");
        }

        try {
            //获取缓存中的token
            String cache = stringRedisTemplate.opsForValue().get(adminTokenKey);
            if (StringUtils.isNotBlank(cache)) {
                if (checkAdminToken(cache)) {
                    return cache;
                }
            }

            RSA rsa = SecureUtil.rsa(CommonConstants.CRM_LOGIN_PASSWORD_PRIVATE_KEY, CommonConstants.CRM_LOGIN_PASSWORD_PUBLIC_KEY);
            String passwordEncrypt = rsa.encryptBase64(AESECBUtil.decryptStr(crmAdminPassword) + System.currentTimeMillis(), KeyType.PublicKey);

            HashMap<String, String> loginParam = new HashMap<>(3);
            loginParam.put("username", AESECBUtil.decryptStr(crmAdminPhone));
            loginParam.put("password", passwordEncrypt);
            String loginParamJson = JsonUtils.toJson(loginParam);

            //链式构建请求
            String loginResult = HttpRequest.post(crmHttp + CRM_ADMIN_LOGIN)
                    .body(loginParamJson)
                    .execute().body();
            log.info("[getAdminToken][请求悟空crm登录,结果:{},请求参数:{}]", loginResult, loginParam);
            CrmLoginResultDto crmLoginResultDto = JsonUtils.fromJson(loginResult, CrmLoginResultDto.class);

            if (!Objects.equals(crmLoginResultDto.getCode(), CRM_SUCCESS_STATUS_CODE)) {
                throw new ServerException("获取crm用户token异常");
            }
            String adminToken = crmLoginResultDto.getData().getAdminToken();
            stringRedisTemplate.opsForValue().set(adminTokenKey, adminToken, 60 * 60, TimeUnit.SECONDS);
            return adminToken;
        } catch (Exception e) {
            log.error("[getAdminToken][获取admin-token异常]", e);
            throw new ServerException("获取admin-token异常");
        } finally {
            stringRedisTemplate.delete(lockKey);
        }
    }

    /**
     * 获取字段
     *
     * @param type 1:客户 2:商机
     * @return
     */
    private CrmFieldResultDto getField(Integer type) {
        String crmCustomerKey = "meht:crm:field:" + type;
        String cache = stringRedisTemplate.opsForValue().get(crmCustomerKey);
        if (StringUtils.isNotBlank(cache)) {
            return JsonUtils.fromJson(cache, CrmFieldResultDto.class);
        }

        String result = HttpRequest.post(Objects.equals(type, 1) ? crmHttp + CRM_CUSTOMER_FIELD : crmHttp + CRM_BUSINESS_FIELD)
                .header("admin-token", getAdminToken(false))
                .form("type", Objects.equals(type, 1) ? "2" : "5")
                .execute().body();

        CrmFieldResultDto crmFieldResultDto = JsonUtils.fromJson(result, CrmFieldResultDto.class);
        if (crmFieldResultDto.getCode() == CRM_NOT_LOGIN_STATUS_CODE) {
            //token 失效刷新下token
            result = HttpRequest.post(Objects.equals(type, 1) ? crmHttp + CRM_CUSTOMER_FIELD : crmHttp + CRM_BUSINESS_FIELD)
                    .header("admin-token", getAdminToken(true))
                    .form("type", Objects.equals(type, 1) ? "2" : "5")
                    .execute().body();
        }

        crmFieldResultDto = JsonUtils.fromJson(result, CrmFieldResultDto.class);
        log.info("[getField][请求悟空crmCustomerField,结果:{},请求参数:{}]", result, type);

        if (!Objects.equals(crmFieldResultDto.getCode(), CRM_SUCCESS_STATUS_CODE)) {
            throw new ServerException("获取crm字段失败");
        }
        stringRedisTemplate.opsForValue().set(crmCustomerKey, result, 10 * 60, TimeUnit.SECONDS);
        return crmFieldResultDto;
    }

    private CrmCustomerResultDto saveCustomer(CrmCustomerParamDto paramDto) {
        String param = JsonUtils.toJson(paramDto);

        String result = HttpRequest.post(crmHttp + CRM_CUSTOMER_ADD)
                .header("admin-token", getAdminToken(false))
                .body(param)
                .execute().body();
        Map<String, Object> resultMap = JsonUtils.fromMap(result);

        if (Objects.equals(resultMap.get("code"), CRM_NOT_LOGIN_STATUS_CODE)) {
            //刷新token
            result = HttpRequest.post(crmHttp + CRM_CUSTOMER_ADD)
                    .header("admin-token", getAdminToken(true))
                    .body(param)
                    .execute().body();
        }
        log.info("[saveCustomer][请求悟空保存客户关系,param:{},结果:{}]", param, result);
        CrmCustomerResultDto crmCustomerResultDto = JsonUtils.fromJson(result, CrmCustomerResultDto.class);
        if (!Objects.equals(crmCustomerResultDto.getCode(), CRM_SUCCESS_STATUS_CODE)) {
            throw new ServerException("crm保存客服关系失败");
        }
        return crmCustomerResultDto;
    }

    private void saveBusiness(CrmBusinessParamDto crmBusinessParamDto) {
        String param = JsonUtils.toJson(crmBusinessParamDto);

        String result = HttpRequest.post(crmHttp + CRM_BUSINESS_ADD)
                .header("admin-token", getAdminToken(false))
                .body(param)
                .execute().body();

        Map<String, Object> resultMap = JsonUtils.fromMap(result);
        if (Objects.equals(resultMap.get("code"), CRM_NOT_LOGIN_STATUS_CODE)) {
            //刷新token
            result = HttpRequest.post(crmHttp + CRM_BUSINESS_ADD)
                    .header("admin-token", getAdminToken(true))
                    .body(param)
                    .execute().body();
        }
        resultMap = JsonUtils.fromMap(result);
        log.info("[saveBusiness][请求悟空保存客户关系,param:{},结果:{}]", param, result);

        if (!Objects.equals(resultMap.get("code"), CRM_SUCCESS_STATUS_CODE)) {
            log.error("[saveBusiness][请求悟空保存客户关系异常]");
        }
    }

    private void updateCrmBusiness(CrmBusinessUpdateParamDto crmBusinessUpdateParamDto) {
        String param = JsonUtils.toJson(crmBusinessUpdateParamDto);

        String result = HttpRequest.post(crmHttp + CRM_BUSINESS_UPDATE)
                .header("admin-token", getAdminToken(false))
                .body(param)
                .execute().body();

        Map<String, Object> resultMap = JsonUtils.fromMap(result);
        if (Objects.equals(resultMap.get("code"), CRM_NOT_LOGIN_STATUS_CODE)) {
            //刷新token
            result = HttpRequest.post(crmHttp + CRM_BUSINESS_ADD)
                    .header("admin-token", getAdminToken(true))
                    .body(param)
                    .execute().body();
        }
        resultMap = JsonUtils.fromMap(result);
        log.info("[saveBusiness][请求悟空保存客户关系,param:{},结果:{}]", param, result);

        if (!Objects.equals(resultMap.get("code"), CRM_SUCCESS_STATUS_CODE)) {
            log.error("[saveBusiness][请求悟空保存客户关系异常]");
        }
    }

    @Override
    public String getCrmOwnerUserId(String username, String phone) {
        HashMap<String, Object> map = new HashMap<>(2);
        map.put("realname", username);
        map.put("pageType", 0);
        String result = HttpRequest.post(crmHttp + CRM_USER)
                .header("admin-token", getAdminToken(false))
                .body(JsonUtils.toJson(map))
                .execute().body();
        log.info("[getCrmOwnerUserId][获取crmAdmintken,结果:{}]", result);
        CrmUserResultDto crmFieldResultDto = JsonUtils.fromJson(result, CrmUserResultDto.class);
        if (Objects.equals(crmFieldResultDto.getCode(), CRM_NOT_LOGIN_STATUS_CODE)) {
            //刷新token
            result = HttpRequest.post(crmHttp + CRM_USER)
                    .header("admin-token", getAdminToken(true))
                    .body(JsonUtils.toJson(map))
                    .execute().body();
        }

        log.info("[getCrmOwnerUserId][获取所属用户id,结果:{},请求参数:{}]", result, username);

        if (!Objects.equals(crmFieldResultDto.getCode(), CRM_SUCCESS_STATUS_CODE)) {
            throw new ServerException("获取crm用户id接口调用失败");
        }
        List<CrmUserResultDto.userDto> userDtoList = crmFieldResultDto.getData().getList();
        if (CollUtil.isEmpty(userDtoList)) {
            throw new ServerException("获取crm用户id用户不存在");
        }
        if (userDtoList.size() == 1 && userDtoList.get(0).getRealname().equals(username)) {
            return userDtoList.get(0).getUserId();
        }
        if (phone.length() > 11) {
            //电话号码需要解密
            phone = rsaExample.decryptByPrivate(phone);
        }

        //重名称用户通过电话号码进行匹配
        for (CrmUserResultDto.userDto userDto : userDtoList) {
            if (userDto.getMobile().equals(phone) && userDto.getRealname().equals(username)) {
                return userDto.getUserId();
            }
        }

        throw new ServerException("获取crm用户id用户不存在");
    }

    /**
     * 获取用户列表
     */
    public CrmPageResultDto getCustomer(String buildingName) {
        CustomerListReq req = new CustomerListReq();
        req.setSearch(buildingName);

        String result = HttpRequest.post(crmHttp + CRM_CUSTOMER_SEARCH_URL)
                .header("admin-token", getAdminToken(false))
                .body(JsonUtils.toJson(req))
                .execute().body();
        log.info("[crmCustomer][获取CRM客户,结果:{}]", result);
        return JsonUtils.fromJson(result, CrmPageResultDto.class);
    }


    /**
     * 检查当前token是否可用
     *
     * @param adminToken
     * @return
     */
    private boolean checkAdminToken(String adminToken) {
        String result = HttpRequest.post(crmHttp + CRM_QUERY_LOGIN_USER)
                .header("admin-token", adminToken)
                .execute().body();

        Map<String, Object> resultMap = JsonUtils.fromMap(result);

        return Objects.equals(resultMap.get("code"), CRM_SUCCESS_STATUS_CODE);
    }


}
