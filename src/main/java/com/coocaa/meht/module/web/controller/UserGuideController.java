package com.coocaa.meht.module.web.controller;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 用户引导控制器
 * @since 2023-11-20
 */
import com.coocaa.meht.module.web.dto.UserGuideReadDTO;
import com.coocaa.meht.module.web.service.UserGuideService;
import com.coocaa.meht.module.web.vo.UserGuideReadVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 用户引导控制器
 */
@RestController
@RequestMapping("/api/guides")
public class UserGuideController {

    @Autowired
    private UserGuideService userGuideService;

    /**
     * 检查功能引导是否已读
     *
     * @param featureCode 功能编码
     * @param userCode    用户编码
     * @return 是否已读
     */
    @GetMapping("/check")
    public Map<String, Object> checkGuide(@RequestParam String featureCode, @RequestParam String userCode) {
        boolean hasRead = userGuideService.checkUserRead(featureCode, userCode);
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("data", hasRead);
        return result;
    }

    /**
     * 标记功能引导已读
     *
     * @param dto 请求参数
     * @return 标记结果
     */
    @PostMapping("/read")
    public Map<String, Object> markRead(@RequestBody UserGuideReadDTO dto) {
        boolean success = userGuideService.markRead(dto.getUserCode(), dto.getFeatureCode(), "system");
        Map<String, Object> result = new HashMap<>();
        result.put("code", 200);
        result.put("data", success);
        return result;
    }
} 