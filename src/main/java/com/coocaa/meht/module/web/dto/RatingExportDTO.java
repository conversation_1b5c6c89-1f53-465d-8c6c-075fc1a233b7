package com.coocaa.meht.module.web.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2025/5/28
 */

@Data
public class RatingExportDTO {
    @ExcelProperty("楼宇编号")
    private String buildingNo;

    // ========== 人工数据(source=0) ==========
    @ExcelProperty("楼宇类型")
    private String buildingType;
    @ExcelProperty("楼宇名称")
    private String buildingName;
    @ExcelProperty("省份")
    private String mapProvince;
    @ExcelProperty("城市")
    private String mapCity;
    @ExcelProperty("区(县)")
    private String mapRegion;
    @ExcelProperty("详细地址")
    private String mapAddress;
    @ExcelProperty("认证总分")
    private BigDecimal buildingScore;
    @ExcelProperty("认证等级")
    private String projectLevel;
    @ExcelProperty("写字楼等级")
    private String buildingGrade;
    @ExcelProperty("写字楼等级分数")
    private BigDecimal buildingGradeScore;
    @ExcelProperty("地理位置")
    private String buildingLocation;
    @ExcelProperty("地理位置分数")
    private BigDecimal buildingLocationScore;
    @ExcelProperty("楼层数")
    private String buildingNumber;
    @ExcelProperty("楼层数分数")
    private BigDecimal buildingNumberScore;
    @ExcelProperty("月租金")
    private String buildingPrice;
    @ExcelProperty("月租金分数")
    private BigDecimal buildingPriceScore;
    @ExcelProperty("楼龄")
    private String buildingAge;
    @ExcelProperty("楼龄分数")
    private BigDecimal buildingAgeScore;
    @ExcelProperty("外观造型")
    private String buildingExterior;
    @ExcelProperty("外观造型分数")
    private BigDecimal buildingExteriorScore;
    @ExcelProperty("楼盘大堂")
    private String buildingLobby;
    @ExcelProperty("楼盘大堂分数")
    private BigDecimal buildingLobbyScore;
    @ExcelProperty("地下车库")
    private String buildingGarage;
    @ExcelProperty("地下车库分数")
    private BigDecimal buildingGarageScore;
    @ExcelProperty("侯梯厅")
    private String buildingHall;
    @ExcelProperty("侯梯厅分数")
    private BigDecimal buildingHallScore;
    @ExcelProperty("综合体品牌")
    private String buildingBrand;
    @ExcelProperty("综合体品牌分数")
    private BigDecimal buildingBrandScore;
    @ExcelProperty("点评评分")
    private String buildingRating;
    @ExcelProperty("点评评分分数")
    private BigDecimal buildingRatingScore;
    @ExcelProperty("入驻率")
    private String buildingSettled;
    @ExcelProperty("入驻率分数")
    private BigDecimal buildingSettledScore;

    // ========== 豆包数据(source=1) ==========
    @ExcelProperty("豆包_楼宇类型")
    private String dbBuildingType;
    @ExcelProperty("豆包_认证总分")
    private BigDecimal dbBuildingScore;
    @ExcelProperty("豆包_认证等级")
    private String dbProjectLevel;
    @ExcelProperty("豆包_写字楼等级")
    private String dbBuildingGrade;
    @ExcelProperty("豆包_写字楼等级分数")
    private BigDecimal dbBuildingGradeScore;
    @ExcelProperty("豆包_地理位置")
    private String dbBuildingLocation;
    @ExcelProperty("豆包_地理位置分数")
    private BigDecimal dbBuildingLocationScore;
    @ExcelProperty("豆包_楼层数")
    private String dbBuildingNumber;
    @ExcelProperty("豆包_楼层数分数")
    private BigDecimal dbBuildingNumberScore;
    @ExcelProperty("豆包_月租金")
    private String dbBuildingPrice;
    @ExcelProperty("豆包_月租金分数")
    private BigDecimal dbBuildingPriceScore;
    @ExcelProperty("豆包_楼龄")
    private String dbBuildingAge;
    @ExcelProperty("豆包_楼龄分数")
    private BigDecimal dbBuildingAgeScore;
    @ExcelProperty("豆包_外观造型")
    private String dbBuildingExterior;
    @ExcelProperty("豆包_外观造型分数")
    private BigDecimal dbBuildingExteriorScore;
    @ExcelProperty("豆包_楼盘大堂")
    private String dbBuildingLobby;
    @ExcelProperty("豆包_楼盘大堂分数")
    private BigDecimal dbBuildingLobbyScore;
    @ExcelProperty("豆包_地下车库")
    private String dbBuildingGarage;
    @ExcelProperty("豆包_地下车库分数")
    private BigDecimal dbBuildingGarageScore;
    @ExcelProperty("豆包_侯梯厅")
    private String dbBuildingHall;
    @ExcelProperty("豆包_侯梯厅分数")
    private BigDecimal dbBuildingHallScore;
    @ExcelProperty("豆包_综合体品牌")
    private String dbBuildingBrand;
    @ExcelProperty("豆包_综合体品牌分数")
    private BigDecimal dbBuildingBrandScore;
    @ExcelProperty("豆包_点评评分")
    private String dbBuildingRating;
    @ExcelProperty("豆包_点评评分分数")
    private BigDecimal dbBuildingRatingScore;
    @ExcelProperty("豆包_入驻率")
    private String dbBuildingSettled;
    @ExcelProperty("豆包_入驻率分数")
    private BigDecimal dbBuildingSettledScore;

    // ========== deepseek数据(source=2) ==========
    @ExcelProperty("deepseek_楼宇类型")
    private String dsBuildingType;
    @ExcelProperty("deepseek_认证总分")
    private BigDecimal dsBuildingScore;
    @ExcelProperty("deepseek_认证等级")
    private String dsProjectLevel;
    @ExcelProperty("deepseek_写字楼等级")
    private String dsBuildingGrade;
    @ExcelProperty("deepseek_写字楼等级分数")
    private BigDecimal dsBuildingGradeScore;
    @ExcelProperty("deepseek_地理位置")
    private String dsBuildingLocation;
    @ExcelProperty("deepseek_地理位置分数")
    private BigDecimal dsBuildingLocationScore;
    @ExcelProperty("deepseek_楼层数")
    private String dsBuildingNumber;
    @ExcelProperty("deepseek_楼层数分数")
    private BigDecimal dsBuildingNumberScore;
    @ExcelProperty("deepseek_月租金")
    private String dsBuildingPrice;
    @ExcelProperty("deepseek_月租金分数")
    private BigDecimal dsBuildingPriceScore;
    @ExcelProperty("deepseek_楼龄")
    private String dsBuildingAge;
    @ExcelProperty("deepseek_楼龄分数")
    private BigDecimal dsBuildingAgeScore;
    @ExcelProperty("deepseek_外观造型")
    private String dsBuildingExterior;
    @ExcelProperty("deepseek_外观造型分数")
    private BigDecimal dsBuildingExteriorScore;
    @ExcelProperty("deepseek_楼盘大堂")
    private String dsBuildingLobby;
    @ExcelProperty("deepseek_楼盘大堂分数")
    private BigDecimal dsBuildingLobbyScore;
    @ExcelProperty("deepseek_地下车库")
    private String dsBuildingGarage;
    @ExcelProperty("deepseek_地下车库分数")
    private BigDecimal dsBuildingGarageScore;
    @ExcelProperty("deepseek_侯梯厅")
    private String dsBuildingHall;
    @ExcelProperty("deepseek_侯梯厅分数")
    private BigDecimal dsBuildingHallScore;
    @ExcelProperty("deepseek_综合体品牌")
    private String dsBuildingBrand;
    @ExcelProperty("deepseek_综合体品牌分数")
    private BigDecimal dsBuildingBrandScore;
    @ExcelProperty("deepseek_点评评分")
    private String dsBuildingRating;
    @ExcelProperty("deepseek_点评评分分数")
    private BigDecimal dsBuildingRatingScore;
    @ExcelProperty("deepseek_入驻率")
    private String dsBuildingSettled;
    @ExcelProperty("deepseek_入驻率分数")
    private BigDecimal dsBuildingSettledScore;

    // ========== 通义千问数据(source=3) ==========
    @ExcelProperty("通义千问_楼宇类型")
    private String qwBuildingType;
    @ExcelProperty("通义千问_认证总分")
    private BigDecimal qwBuildingScore;
    @ExcelProperty("通义千问_认证等级")
    private String qwProjectLevel;
    @ExcelProperty("通义千问_写字楼等级")
    private String qwBuildingGrade;
    @ExcelProperty("通义千问_写字楼等级分数")
    private BigDecimal qwBuildingGradeScore;
    @ExcelProperty("通义千问_地理位置")
    private String qwBuildingLocation;
    @ExcelProperty("通义千问_地理位置分数")
    private BigDecimal qwBuildingLocationScore;
    @ExcelProperty("通义千问_楼层数")
    private String qwBuildingNumber;
    @ExcelProperty("通义千问_楼层数分数")
    private BigDecimal qwBuildingNumberScore;
    @ExcelProperty("通义千问_月租金")
    private String qwBuildingPrice;
    @ExcelProperty("通义千问_月租金分数")
    private BigDecimal qwBuildingPriceScore;
    @ExcelProperty("通义千问_楼龄")
    private String qwBuildingAge;
    @ExcelProperty("通义千问_楼龄分数")
    private BigDecimal qwBuildingAgeScore;
    @ExcelProperty("通义千问_外观造型")
    private String qwBuildingExterior;
    @ExcelProperty("通义千问_外观造型分数")
    private BigDecimal qwBuildingExteriorScore;
    @ExcelProperty("通义千问_楼盘大堂")
    private String qwBuildingLobby;
    @ExcelProperty("通义千问_楼盘大堂分数")
    private BigDecimal qwBuildingLobbyScore;
    @ExcelProperty("通义千问_地下车库")
    private String qwBuildingGarage;
    @ExcelProperty("通义千问_地下车库分数")
    private BigDecimal qwBuildingGarageScore;
    @ExcelProperty("通义千问_侯梯厅")
    private String qwBuildingHall;
    @ExcelProperty("通义千问_侯梯厅分数")
    private BigDecimal qwBuildingHallScore;
    @ExcelProperty("通义千问_综合体品牌")
    private String qwBuildingBrand;
    @ExcelProperty("通义千问_综合体品牌分数")
    private BigDecimal qwBuildingBrandScore;
    @ExcelProperty("通义千问_点评评分")
    private String qwBuildingRating;
    @ExcelProperty("通义千问_点评评分分数")
    private BigDecimal qwBuildingRatingScore;
    @ExcelProperty("通义千问_入驻率")
    private String qwBuildingSettled;
    @ExcelProperty("通义千问_入驻率分数")
    private BigDecimal qwBuildingSettledScore;

    // ========== OpenAI数据(source=4) ==========
    @ExcelProperty("OpenAI_楼宇类型")
    private String oaBuildingType;
    @ExcelProperty("OpenAI_认证总分")
    private BigDecimal oaBuildingScore;
    @ExcelProperty("OpenAI_认证等级")
    private String oaProjectLevel;
    @ExcelProperty("OpenAI_写字楼等级")
    private String oaBuildingGrade;
    @ExcelProperty("OpenAI_写字楼等级分数")
    private BigDecimal oaBuildingGradeScore;
    @ExcelProperty("OpenAI_地理位置")
    private String oaBuildingLocation;
    @ExcelProperty("OpenAI_地理位置分数")
    private BigDecimal oaBuildingLocationScore;
    @ExcelProperty("OpenAI_楼层数")
    private String oaBuildingNumber;
    @ExcelProperty("OpenAI_楼层数分数")
    private BigDecimal oaBuildingNumberScore;
    @ExcelProperty("OpenAI_月租金")
    private String oaBuildingPrice;
    @ExcelProperty("OpenAI_月租金分数")
    private BigDecimal oaBuildingPriceScore;
    @ExcelProperty("OpenAI_楼龄")
    private String oaBuildingAge;
    @ExcelProperty("OpenAI_楼龄分数")
    private BigDecimal oaBuildingAgeScore;
    @ExcelProperty("OpenAI_外观造型")
    private String oaBuildingExterior;
    @ExcelProperty("OpenAI_外观造型分数")
    private BigDecimal oaBuildingExteriorScore;
    @ExcelProperty("OpenAI_楼盘大堂")
    private String oaBuildingLobby;
    @ExcelProperty("OpenAI_楼盘大堂分数")
    private BigDecimal oaBuildingLobbyScore;
    @ExcelProperty("OpenAI_地下车库")
    private String oaBuildingGarage;
    @ExcelProperty("OpenAI_地下车库分数")
    private BigDecimal oaBuildingGarageScore;
    @ExcelProperty("OpenAI_侯梯厅")
    private String oaBuildingHall;
    @ExcelProperty("OpenAI_侯梯厅分数")
    private BigDecimal oaBuildingHallScore;
    @ExcelProperty("OpenAI_综合体品牌")
    private String oaBuildingBrand;
    @ExcelProperty("OpenAI_综合体品牌分数")
    private BigDecimal oaBuildingBrandScore;
    @ExcelProperty("OpenAI_点评评分")
    private String oaBuildingRating;
    @ExcelProperty("OpenAI_点评评分分数")
    private BigDecimal oaBuildingRatingScore;
    @ExcelProperty("OpenAI_入驻率")
    private String oaBuildingSettled;
    @ExcelProperty("OpenAI_入驻率分数")
    private BigDecimal oaBuildingSettledScore;

}