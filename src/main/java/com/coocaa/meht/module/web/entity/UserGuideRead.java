package com.coocaa.meht.module.web.entity;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 用户已读引导记录实体类
 * @since 2023-11-20
 */
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 用户已读引导记录实体类
 */
@Data
@TableName("user_guide_read")
public class UserGuideRead {

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 页面功能编码
     */
    private String featureCode;

    /**
     * 阅读时间
     */
    private Date readTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 是否删除: 0否,1是
     */
    @TableLogic
    private Integer deleteFlag;
} 