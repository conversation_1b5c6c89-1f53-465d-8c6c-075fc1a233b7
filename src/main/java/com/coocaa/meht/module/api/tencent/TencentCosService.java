package com.coocaa.meht.module.api.tencent;

import com.coocaa.meht.common.exception.ServerException;
import com.coocaa.meht.cos.ObjectUtils;
import com.coocaa.meht.utils.FtpUtils;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.model.ObjectMetadata;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.model.PutObjectResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import java.io.IOException;
import java.net.URL;
import java.time.LocalDate;
import java.util.*;
@Slf4j
@Component
public class TencentCosService {

    @Resource
    private COSClient cosClient;


    public List<Map<String,Object>> uploadCos(MultipartFile[] multipartFiles) throws IOException {
        // 初始化腾讯云 COS 客户端
        List<Map<String,Object>> keyList = new ArrayList<>();
        for (MultipartFile file : multipartFiles) {
            log.info("上传文件类型对象：{}", file);
            int suffixIdx = file.getOriginalFilename().lastIndexOf(".");
            if (suffixIdx < 0) {
                throw new ServerException("暂不支持当前的文件类型");
            }
            String suffix = file.getOriginalFilename().substring(suffixIdx + 1).toLowerCase();
            log.info("上传文件类型转小写：{}", suffix);
            if (!FtpUtils.FILE_TYPE.contains(suffix)) {
                throw new ServerException("暂不支持当前的文件类型");
            }
            // 配置上传图片的元数据
            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setContentLength(file.getSize());
            String oldFileName = file.getOriginalFilename();
            String fileType = "";//文件类型
            int dotIndex = oldFileName.lastIndexOf('.');
            if (dotIndex > 0 && dotIndex < oldFileName.length() - 1) {
                fileType = oldFileName.substring(dotIndex + 1).toLowerCase();
            }
            // 配置腾讯云 COS 存储路径
            // 生成上传到服务器的文件路径和随机文件名
            String randomUUID = UUID.randomUUID().toString();
            String key = String.format("%s.%s", randomUUID.replace("-", "") , fileType);

            // 使用腾讯云 COS SDK 上传图片
            ObjectUtils.uploadFile(ObjectUtils.getCosFileName("meht", key), file.getInputStream());
            String objectUrl = ObjectUtils.getAccessUrl("meht", key);
            Map<String,Object> map = new HashMap<>();
            map.put("name",oldFileName);
            map.put("url", objectUrl);
            map.put("size",file.getSize());
            map.put("attachmentType",suffix);
            keyList.add(map);
        }
        return keyList;
    }
}






